/**
 * @file voice_servo_integration.cc
 * @brief 语音控制与180度舵机集成实现
 */

#include "voice_servo_integration.h"
#include <esp_log.h>
#include <algorithm>
#include <sstream>
#include <regex>

static const char* TAG = "VoiceServo";

namespace voice_servo_integration {

VoiceServoController::VoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio)
    : initialized_(false) {
    
    ESP_LOGI(TAG, "创建语音舵机控制器: 左臂GPIO=%d, 右臂GPIO=%d", left_gpio, right_gpio);
    
    // 创建180度舵机控制器
    servo_controller_ = std::make_unique<servo_180::Servo180Controller>(left_gpio, right_gpio);
}

VoiceServoController::~VoiceServoController() {
    ESP_LOGI(TAG, "语音舵机控制器析构");
}

bool VoiceServoController::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "语音舵机控制器已初始化");
        return true;
    }
    
    ESP_LOGI(TAG, "初始化语音舵机控制器...");
    
    // 检查舵机控制器是否初始化成功
    if (!servo_controller_ || !servo_controller_->IsInitialized()) {
        ESP_LOGE(TAG, "舵机控制器初始化失败");
        return false;
    }
    
    // 初始化默认语音指令
    InitializeDefaultCommands();
    
    initialized_ = true;
    ESP_LOGI(TAG, "语音舵机控制器初始化成功");
    
    return true;
}

void VoiceServoController::InitializeDefaultCommands() {
    ESP_LOGI(TAG, "初始化默认语音指令...");
    
    // 挥手指令
    command_map_["挥手"] = {VoiceCommand::WAVE_HAND, VoiceCommandParams()};
    command_map_["挥挥手"] = {VoiceCommand::WAVE_HAND, VoiceCommandParams()};
    command_map_["wave"] = {VoiceCommand::WAVE_HAND, VoiceCommandParams()};
    command_map_["wave hand"] = {VoiceCommand::WAVE_HAND, VoiceCommandParams()};
    
    // 举手指令
    command_map_["举手"] = {VoiceCommand::RAISE_HAND, VoiceCommandParams()};
    command_map_["举起手"] = {VoiceCommand::RAISE_HAND, VoiceCommandParams()};
    command_map_["raise hand"] = {VoiceCommand::RAISE_HAND, VoiceCommandParams()};
    command_map_["hands up"] = {VoiceCommand::BOTH_ARMS_UP, VoiceCommandParams()};
    
    // 放下手指令
    command_map_["放下手"] = {VoiceCommand::LOWER_HAND, VoiceCommandParams()};
    command_map_["放下"] = {VoiceCommand::LOWER_HAND, VoiceCommandParams()};
    command_map_["lower hand"] = {VoiceCommand::LOWER_HAND, VoiceCommandParams()};
    command_map_["hands down"] = {VoiceCommand::BOTH_ARMS_DOWN, VoiceCommandParams()};
    
    // 敬礼指令
    command_map_["敬礼"] = {VoiceCommand::SALUTE, VoiceCommandParams()};
    command_map_["salute"] = {VoiceCommand::SALUTE, VoiceCommandParams()};
    
    // 指向指令
    command_map_["指向左边"] = {VoiceCommand::POINT_LEFT, VoiceCommandParams()};
    command_map_["指向右边"] = {VoiceCommand::POINT_RIGHT, VoiceCommandParams()};
    command_map_["指向前方"] = {VoiceCommand::POINT_FORWARD, VoiceCommandParams()};
    command_map_["point left"] = {VoiceCommand::POINT_LEFT, VoiceCommandParams()};
    command_map_["point right"] = {VoiceCommand::POINT_RIGHT, VoiceCommandParams()};
    command_map_["point forward"] = {VoiceCommand::POINT_FORWARD, VoiceCommandParams()};
    
    // 停止指令
    command_map_["停止"] = {VoiceCommand::STOP_MOTION, VoiceCommandParams()};
    command_map_["stop"] = {VoiceCommand::STOP_MOTION, VoiceCommandParams()};
    command_map_["停止动作"] = {VoiceCommand::STOP_MOTION, VoiceCommandParams()};
    
    // 重置指令
    command_map_["重置"] = {VoiceCommand::RESET_POSITION, VoiceCommandParams()};
    command_map_["reset"] = {VoiceCommand::RESET_POSITION, VoiceCommandParams()};
    command_map_["回到中间"] = {VoiceCommand::RESET_POSITION, VoiceCommandParams()};
    
    // 舞蹈指令
    command_map_["跳舞"] = {VoiceCommand::DANCE, VoiceCommandParams()};
    command_map_["dance"] = {VoiceCommand::DANCE, VoiceCommandParams()};
    
    ESP_LOGI(TAG, "已注册 %d 个默认语音指令", command_map_.size());
}

bool VoiceServoController::ProcessVoiceCommand(const std::string& voice_text) {
    if (!initialized_) {
        ESP_LOGE(TAG, "控制器未初始化");
        return false;
    }
    
    ESP_LOGI(TAG, "处理语音指令: %s", voice_text.c_str());
    
    // 转换为小写以便匹配
    std::string lower_text = voice_text;
    std::transform(lower_text.begin(), lower_text.end(), lower_text.begin(), ::tolower);
    
    // 解析语音指令
    VoiceCommandParams params;
    VoiceCommand command = ParseVoiceText(lower_text, params);
    
    if (command == VoiceCommand::UNKNOWN) {
        ESP_LOGW(TAG, "未识别的语音指令: %s", voice_text.c_str());
        return false;
    }
    
    // 执行指令
    bool success = ExecuteVoiceCommand(command, params);
    
    // 调用回调函数
    if (command_callback_) {
        command_callback_(command, success);
    }
    
    return success;
}

VoiceCommand VoiceServoController::ParseVoiceText(const std::string& voice_text, VoiceCommandParams& params) {
    // 首先检查完全匹配
    auto it = command_map_.find(voice_text);
    if (it != command_map_.end()) {
        params = it->second.second;
        return it->second.first;
    }
    
    // 检查部分匹配
    for (const auto& pair : command_map_) {
        if (voice_text.find(pair.first) != std::string::npos) {
            params = pair.second.second;
            
            // 提取额外参数
            params.target_arm = ExtractTargetArm(voice_text);
            params.repeat_count = ExtractRepeatCount(voice_text);
            params.speed = ExtractSpeed(voice_text);
            
            return pair.second.first;
        }
    }
    
    return VoiceCommand::UNKNOWN;
}

std::string VoiceServoController::ExtractTargetArm(const std::string& text) {
    if (text.find("左") != std::string::npos || text.find("left") != std::string::npos) {
        return "left";
    } else if (text.find("右") != std::string::npos || text.find("right") != std::string::npos) {
        return "right";
    } else if (text.find("双") != std::string::npos || text.find("both") != std::string::npos || 
               text.find("两") != std::string::npos) {
        return "both";
    }
    return "both";  // 默认双臂
}

int VoiceServoController::ExtractRepeatCount(const std::string& text) {
    // 简单的数字提取
    if (text.find("一次") != std::string::npos || text.find("1") != std::string::npos) return 1;
    if (text.find("两次") != std::string::npos || text.find("2") != std::string::npos) return 2;
    if (text.find("三次") != std::string::npos || text.find("3") != std::string::npos) return 3;
    if (text.find("四次") != std::string::npos || text.find("4") != std::string::npos) return 4;
    if (text.find("五次") != std::string::npos || text.find("5") != std::string::npos) return 5;
    return 1;  // 默认1次
}

uint8_t VoiceServoController::ExtractSpeed(const std::string& text) {
    if (text.find("快") != std::string::npos || text.find("fast") != std::string::npos) return 80;
    if (text.find("慢") != std::string::npos || text.find("slow") != std::string::npos) return 30;
    return 50;  // 默认中等速度
}

bool VoiceServoController::ExecuteVoiceCommand(VoiceCommand command, const VoiceCommandParams& params) {
    if (!servo_controller_) {
        ESP_LOGE(TAG, "舵机控制器无效");
        return false;
    }
    
    ESP_LOGI(TAG, "执行语音指令: %s, 目标: %s, 速度: %d", 
             VoiceCommandToString(command).c_str(), params.target_arm.c_str(), params.speed);
    
    switch (command) {
        case VoiceCommand::WAVE_HAND:
            return ExecuteWaveCommand(params);
            
        case VoiceCommand::RAISE_HAND:
        case VoiceCommand::BOTH_ARMS_UP:
            return ExecuteRaiseCommand(params);
            
        case VoiceCommand::LOWER_HAND:
        case VoiceCommand::BOTH_ARMS_DOWN:
            return ExecuteLowerCommand(params);
            
        case VoiceCommand::SALUTE:
            return ExecuteSaluteCommand(params);
            
        case VoiceCommand::POINT_LEFT:
        case VoiceCommand::POINT_RIGHT:
        case VoiceCommand::POINT_FORWARD:
            return ExecutePointCommand(command, params);
            
        case VoiceCommand::STOP_MOTION:
            servo_controller_->StopAll();
            return true;
            
        case VoiceCommand::RESET_POSITION:
            return servo_controller_->SetBothArms(90.0f, 90.0f, params.speed, true);
            
        case VoiceCommand::DANCE:
            return ExecuteDanceCommand(params);
            
        default:
            ESP_LOGW(TAG, "不支持的语音指令: %s", VoiceCommandToString(command).c_str());
            return false;
    }
}

bool VoiceServoController::ExecuteWaveCommand(const VoiceCommandParams& params) {
    if (params.target_arm == "left") {
        auto* left_servo = servo_controller_->GetLeftServo();
        return left_servo ? left_servo->Wave(params.repeat_count, params.speed, 45.0f) : false;
    } else if (params.target_arm == "right") {
        auto* right_servo = servo_controller_->GetRightServo();
        return right_servo ? right_servo->Wave(params.repeat_count, params.speed, 45.0f) : false;
    } else {
        return servo_controller_->DualWave(params.repeat_count, params.speed, params.synchronized);
    }
}

bool VoiceServoController::ExecuteRaiseCommand(const VoiceCommandParams& params) {
    float angle = (params.angle > 0) ? params.angle : 150.0f;
    
    if (params.target_arm == "left") {
        return servo_controller_->SetSingleArm("left", angle, params.speed);
    } else if (params.target_arm == "right") {
        return servo_controller_->SetSingleArm("right", angle, params.speed);
    } else {
        return servo_controller_->DualRaise(angle, params.speed);
    }
}

bool VoiceServoController::ExecuteLowerCommand(const VoiceCommandParams& params) {
    float angle = (params.angle > 0) ? params.angle : 30.0f;
    
    if (params.target_arm == "left") {
        return servo_controller_->SetSingleArm("left", angle, params.speed);
    } else if (params.target_arm == "right") {
        return servo_controller_->SetSingleArm("right", angle, params.speed);
    } else {
        return servo_controller_->SetBothArms(angle, angle, params.speed, true);
    }
}

bool VoiceServoController::ExecuteSaluteCommand(const VoiceCommandParams& params) {
    return servo_controller_->DualSalute(params.speed);
}

bool VoiceServoController::ExecutePointCommand(VoiceCommand command, const VoiceCommandParams& params) {
    float angle;
    std::string target_arm;
    
    switch (command) {
        case VoiceCommand::POINT_LEFT:
            angle = 45.0f;
            target_arm = "left";
            break;
        case VoiceCommand::POINT_RIGHT:
            angle = 135.0f;
            target_arm = "right";
            break;
        case VoiceCommand::POINT_FORWARD:
            angle = 90.0f;
            target_arm = params.target_arm;
            break;
        default:
            return false;
    }
    
    if (target_arm == "both") {
        return servo_controller_->SetBothArms(angle, angle, params.speed, true);
    } else {
        return servo_controller_->SetSingleArm(target_arm, angle, params.speed);
    }
}

bool VoiceServoController::ExecuteDanceCommand(const VoiceCommandParams& params) {
    ESP_LOGI(TAG, "执行舞蹈动作");
    
    // 简单的舞蹈序列
    servo_controller_->DualWave(2, params.speed, false);  // 交替挥手
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    servo_controller_->DualRaise(150.0f, params.speed);   // 举手
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    servo_controller_->SetBothArms(30.0f, 30.0f, params.speed, true);  // 放下
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    servo_controller_->SetBothArms(90.0f, 90.0f, params.speed, true);  // 回到中性
    
    return true;
}

void VoiceServoController::SetCommandCallback(std::function<void(VoiceCommand, bool)> callback) {
    command_callback_ = callback;
}

std::vector<std::string> VoiceServoController::GetSupportedCommands() const {
    std::vector<std::string> commands;
    for (const auto& pair : command_map_) {
        commands.push_back(pair.first);
    }
    return commands;
}

void VoiceServoController::EmergencyStop() {
    ESP_LOGW(TAG, "紧急停止所有舵机动作");
    if (servo_controller_) {
        servo_controller_->StopAll();
    }
}

// 辅助函数实现
std::string VoiceCommandToString(VoiceCommand command) {
    switch (command) {
        case VoiceCommand::WAVE_HAND: return "WAVE_HAND";
        case VoiceCommand::RAISE_HAND: return "RAISE_HAND";
        case VoiceCommand::LOWER_HAND: return "LOWER_HAND";
        case VoiceCommand::SALUTE: return "SALUTE";
        case VoiceCommand::POINT_LEFT: return "POINT_LEFT";
        case VoiceCommand::POINT_RIGHT: return "POINT_RIGHT";
        case VoiceCommand::POINT_FORWARD: return "POINT_FORWARD";
        case VoiceCommand::BOTH_ARMS_UP: return "BOTH_ARMS_UP";
        case VoiceCommand::BOTH_ARMS_DOWN: return "BOTH_ARMS_DOWN";
        case VoiceCommand::STOP_MOTION: return "STOP_MOTION";
        case VoiceCommand::RESET_POSITION: return "RESET_POSITION";
        case VoiceCommand::DANCE: return "DANCE";
        case VoiceCommand::UNKNOWN: return "UNKNOWN";
        default: return "UNKNOWN";
    }
}

VoiceCommand StringToVoiceCommand(const std::string& command_str) {
    if (command_str == "WAVE_HAND") return VoiceCommand::WAVE_HAND;
    if (command_str == "RAISE_HAND") return VoiceCommand::RAISE_HAND;
    if (command_str == "LOWER_HAND") return VoiceCommand::LOWER_HAND;
    if (command_str == "SALUTE") return VoiceCommand::SALUTE;
    if (command_str == "POINT_LEFT") return VoiceCommand::POINT_LEFT;
    if (command_str == "POINT_RIGHT") return VoiceCommand::POINT_RIGHT;
    if (command_str == "POINT_FORWARD") return VoiceCommand::POINT_FORWARD;
    if (command_str == "BOTH_ARMS_UP") return VoiceCommand::BOTH_ARMS_UP;
    if (command_str == "BOTH_ARMS_DOWN") return VoiceCommand::BOTH_ARMS_DOWN;
    if (command_str == "STOP_MOTION") return VoiceCommand::STOP_MOTION;
    if (command_str == "RESET_POSITION") return VoiceCommand::RESET_POSITION;
    if (command_str == "DANCE") return VoiceCommand::DANCE;
    return VoiceCommand::UNKNOWN;
}

std::unique_ptr<VoiceServoController> CreateVoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio) {
    return std::make_unique<VoiceServoController>(left_gpio, right_gpio);
}

} // namespace voice_servo_integration
