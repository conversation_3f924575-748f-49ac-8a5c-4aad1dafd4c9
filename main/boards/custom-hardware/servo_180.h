/**
 * @file servo_180.h
 * @brief 180度舵机控制器
 * 
 * 基于现有360度舵机控制逻辑，实现180度舵机的精确角度控制
 * 避免360度舵机可能出现的震动和不稳定问题
 */

#pragma once
#include "driver/mcpwm_prelude.h"
#include "driver/gpio.h"
#include "esp_err.h"
#include <functional>

namespace servo_180 {

/**
 * @brief 180度舵机PWM配置
 */
typedef struct {
    uint32_t min_pulse_width_us;    // 最小脉冲宽度（0度）
    uint32_t max_pulse_width_us;    // 最大脉冲宽度（180度）
    uint32_t neutral_pulse_width_us; // 中性脉冲宽度（90度）
} servo_180_pwm_range_t;

/**
 * @brief 舵机动作类型
 */
enum class ServoAction {
    STOP,              // 停止
    MOVE_TO_ANGLE,     // 移动到指定角度
    WAVE,              // 挥手
    RAISE,             // 举手
    LOWER,             // 放下
    SALUTE,            // 敬礼
    POINT,             // 指向
    SWEEP,             // 扫描
    CUSTOM             // 自定义
};

/**
 * @brief 舵机状态
 */
enum class ServoState {
    IDLE,              // 空闲
    MOVING,            // 运动中
    HOLDING,           // 保持位置
    ERROR              // 错误状态
};

/**
 * @brief 180度舵机类
 */
class Servo180 {
public:
    /**
     * @brief 构造函数
     * @param gpio GPIO引脚
     * @param timer MCPWM定时器句柄
     * @param pwm_range PWM范围配置（可选）
     * @param reverse 是否反向
     */
    Servo180(gpio_num_t gpio, mcpwm_timer_handle_t timer, 
             const servo_180_pwm_range_t* pwm_range = nullptr, bool reverse = false);
    
    /**
     * @brief 析构函数
     */
    ~Servo180();
    
    /**
     * @brief 设置目标角度
     * @param angle 目标角度（0-180度）
     * @param speed 运动速度（1-100）
     * @param smooth 是否平滑运动
     * @return 是否成功
     */
    bool SetAngle(float angle, uint8_t speed = 50, bool smooth = true);
    
    /**
     * @brief 获取当前角度
     * @return 当前角度
     */
    float GetCurrentAngle() const;
    
    /**
     * @brief 停止舵机
     */
    void Stop();
    
    /**
     * @brief 执行动作
     * @param action 动作类型
     * @param parameters 动作参数
     * @return 是否成功
     */
    bool ExecuteAction(ServoAction action, const std::map<std::string, float>& parameters = {});
    
    /**
     * @brief 挥手动作
     * @param count 挥手次数
     * @param speed 速度
     * @param amplitude 幅度（度）
     * @return 是否成功
     */
    bool Wave(int count = 3, uint8_t speed = 60, float amplitude = 45.0f);
    
    /**
     * @brief 举手动作
     * @param angle 举手角度
     * @param speed 速度
     * @return 是否成功
     */
    bool RaiseArm(float angle = 150.0f, uint8_t speed = 50);
    
    /**
     * @brief 放下手臂
     * @param speed 速度
     * @return 是否成功
     */
    bool LowerArm(uint8_t speed = 50);
    
    /**
     * @brief 敬礼动作
     * @param speed 速度
     * @return 是否成功
     */
    bool Salute(uint8_t speed = 50);
    
    /**
     * @brief 指向动作
     * @param angle 指向角度
     * @param speed 速度
     * @return 是否成功
     */
    bool Point(float angle, uint8_t speed = 50);
    
    /**
     * @brief 扫描动作
     * @param start_angle 起始角度
     * @param end_angle 结束角度
     * @param speed 速度
     * @param count 扫描次数
     * @return 是否成功
     */
    bool Sweep(float start_angle = 0.0f, float end_angle = 180.0f, 
               uint8_t speed = 30, int count = 1);
    
    /**
     * @brief 校准舵机
     * @param min_pulse 最小脉冲宽度
     * @param max_pulse 最大脉冲宽度
     * @return 是否成功
     */
    bool Calibrate(uint32_t min_pulse = 500, uint32_t max_pulse = 2500);
    
    /**
     * @brief 设置角度限制
     * @param min_angle 最小角度
     * @param max_angle 最大角度
     */
    void SetAngleLimits(float min_angle, float max_angle);
    
    /**
     * @brief 获取舵机状态
     * @return 舵机状态
     */
    ServoState GetState() const;
    
    /**
     * @brief 获取GPIO引脚
     * @return GPIO引脚号
     */
    gpio_num_t GetGPIO() const { return gpio_; }
    
    /**
     * @brief 检查是否初始化
     * @return 是否已初始化
     */
    bool IsInitialized() const { return initialized_; }
    
    /**
     * @brief 设置运动完成回调
     * @param callback 回调函数
     */
    void SetMoveCompleteCallback(std::function<void(float)> callback);
    
    /**
     * @brief 等待运动完成
     * @param timeout_ms 超时时间（毫秒）
     * @return 是否在超时前完成
     */
    bool WaitForMoveComplete(uint32_t timeout_ms = 5000);

private:
    // 硬件相关
    gpio_num_t gpio_;
    mcpwm_timer_handle_t timer_;
    mcpwm_oper_handle_t oper_;
    mcpwm_cmpr_handle_t cmpr_;
    mcpwm_gen_handle_t gen_;
    
    // 配置参数
    servo_180_pwm_range_t pwm_range_;
    bool reverse_;
    bool initialized_;
    
    // 状态变量
    float current_angle_;
    float target_angle_;
    ServoState state_;
    float min_angle_limit_;
    float max_angle_limit_;
    
    // 运动控制
    TaskHandle_t move_task_;
    std::function<void(float)> move_complete_callback_;
    
    // 私有方法
    esp_err_t SetupPWM();
    void SetPulseWidth(uint32_t pulse_width_us);
    uint32_t AngleToPulseWidth(float angle);
    float PulseWidthToAngle(uint32_t pulse_width);
    bool IsAngleValid(float angle);
    void UpdateCurrentAngle();
    
    // 运动任务
    static void MoveTask(void* parameter);
    void ExecuteMove(float target_angle, uint8_t speed, bool smooth);
    
    // 动作实现
    void ExecuteWaveAction(int count, uint8_t speed, float amplitude);
    void ExecuteSweepAction(float start_angle, float end_angle, uint8_t speed, int count);
};

/**
 * @brief 180度舵机控制器管理类
 */
class Servo180Controller {
public:
    /**
     * @brief 构造函数
     * @param left_gpio 左臂GPIO
     * @param right_gpio 右臂GPIO
     */
    Servo180Controller(gpio_num_t left_gpio, gpio_num_t right_gpio);
    
    /**
     * @brief 析构函数
     */
    ~Servo180Controller();
    
    /**
     * @brief 初始化控制器
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 设置双臂角度
     * @param left_angle 左臂角度
     * @param right_angle 右臂角度
     * @param speed 速度
     * @param synchronized 是否同步运动
     * @return 是否成功
     */
    bool SetBothArms(float left_angle, float right_angle, uint8_t speed = 50, bool synchronized = true);
    
    /**
     * @brief 设置单臂角度
     * @param target 目标臂（"left", "right"）
     * @param angle 角度
     * @param speed 速度
     * @return 是否成功
     */
    bool SetSingleArm(const std::string& target, float angle, uint8_t speed = 50);
    
    /**
     * @brief 执行双臂动作
     * @param action 动作类型
     * @param parameters 动作参数
     * @return 是否成功
     */
    bool ExecuteDualAction(ServoAction action, const std::map<std::string, float>& parameters = {});
    
    /**
     * @brief 双臂挥手
     * @param count 挥手次数
     * @param speed 速度
     * @param synchronized 是否同步
     * @return 是否成功
     */
    bool DualWave(int count = 3, uint8_t speed = 60, bool synchronized = true);
    
    /**
     * @brief 双臂举手
     * @param angle 举手角度
     * @param speed 速度
     * @return 是否成功
     */
    bool DualRaise(float angle = 150.0f, uint8_t speed = 50);
    
    /**
     * @brief 双臂敬礼
     * @param speed 速度
     * @return 是否成功
     */
    bool DualSalute(uint8_t speed = 50);
    
    /**
     * @brief 停止所有舵机
     */
    void StopAll();
    
    /**
     * @brief 获取左臂舵机
     * @return 左臂舵机指针
     */
    Servo180* GetLeftServo() { return left_servo_.get(); }
    
    /**
     * @brief 获取右臂舵机
     * @return 右臂舵机指针
     */
    Servo180* GetRightServo() { return right_servo_.get(); }
    
    /**
     * @brief 检查是否初始化
     * @return 是否已初始化
     */
    bool IsInitialized() const { return initialized_; }

private:
    std::unique_ptr<Servo180> left_servo_;
    std::unique_ptr<Servo180> right_servo_;
    mcpwm_timer_handle_t shared_timer_;
    bool initialized_;
    
    // 私有方法
    bool InitializeTimer();
    void CleanupTimer();
};

/**
 * @brief 动作类型转字符串
 */
std::string ServoActionToString(ServoAction action);

/**
 * @brief 字符串转动作类型
 */
ServoAction StringToServoAction(const std::string& action_str);

/**
 * @brief 舵机状态转字符串
 */
std::string ServoStateToString(ServoState state);

} // namespace servo_180
