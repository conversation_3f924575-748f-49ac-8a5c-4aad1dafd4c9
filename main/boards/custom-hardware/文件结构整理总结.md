# Custom-Hardware 文件结构整理总结

## 📁 整理概述

本次整理将custom-hardware文件夹下的硬件设备代码按功能分类组织，确保结构清晰，避免在外层出现设备调用相关的文件。

## 🗂️ 整理后的文件结构

```
main/boards/custom-hardware/
├── 📁 ai_layer/                      # AI交互层
│   └── xiaozhi_ai_complete.cc        # 完整AI实现
├── 📁 core_layer/                    # 核心任务层
│   └── task_manager.cc               # 任务管理器
├── 📁 device_layer/                  # 设备抽象层
│   ├── device_manager.cc             # 设备管理器
│   ├── display_device.cc             # 显示设备
│   ├── servo_device.cc               # 舵机设备
│   ├── motor_device.cc               # 电机设备
│   └── sensor_device.cc              # 传感器设备
├── 📁 hardware_layer/                # 硬件驱动层
│   ├── mcp_communication.cc          # MCP通信协议
│   ├── servo_driver.cc               # 舵机驱动
│   ├── motor_driver.cc               # 电机驱动
│   └── sensor_driver.cc              # 传感器驱动
├── 📁 expression_system/             # 表情系统
│   ├── expression_manager.cc         # 表情管理器
│   ├── eye_renderer.cc               # 眼部渲染器
│   ├── emotion_config.cc             # 情感配置
│   ├── dual_screen_expression.cc     # 双屏表情系统
│   └── expression_integration.cc     # 表情系统集成
├── 📁 motion_system/                 # 运动控制系统 ⭐ 新整理
│   ├── motion_controller.cc          # 运动控制器
│   ├── servo_controller.cc           # 舵机控制器
│   ├── motor_controller.cc           # 电机控制器
│   ├── person_follow_controller.cc   # 人物跟随控制器
│   ├── servo_180.h                   # 180度舵机控制器头文件 ⭐ 移动
│   ├── servo_180.cc                  # 180度舵机控制器实现 ⭐ 移动
│   ├── voice_servo_integration.h     # 语音舵机集成头文件 ⭐ 移动
│   └── voice_servo_integration.cc    # 语音舵机集成实现 ⭐ 移动
├── 📁 sensor_system/                 # 传感器系统
│   ├── sensor_manager.cc             # 传感器管理器
│   ├── ultrasonic_sensor.cc          # 超声波传感器
│   ├── infrared_sensor.cc            # 红外传感器
│   └── millimeter_wave_radar.cc      # 毫米波雷达
├── 📄 system_controller.cc           # 系统控制器（根目录）
├── 📄 custom_hardware.cc             # 硬件初始化（根目录）
├── 📄 ai_toy_demo.cc                 # AI玩具演示（根目录）
├── 📄 CMakeLists.txt                 # 构建配置
├── 📄 项目完成总结.md                # 项目总结文档
├── 📄 舵机系统升级说明.md            # 舵机升级说明
└── 📄 文件结构整理总结.md            # 本文档
```

## 🔄 主要变更

### 移动的文件
1. **servo_180.h/cc** 
   - 从：`main/boards/custom-hardware/`
   - 到：`main/boards/custom-hardware/motion_system/`

2. **voice_servo_integration.h/cc**
   - 从：`main/boards/custom-hardware/`
   - 到：`main/boards/custom-hardware/motion_system/`

### 删除的文件
- `main/boards/custom-hardware/servo_180.h` ❌
- `main/boards/custom-hardware/servo_180.cc` ❌
- `main/boards/custom-hardware/voice_servo_integration.h` ❌
- `main/boards/custom-hardware/voice_servo_integration.cc` ❌

### 更新的文件
1. **CMakeLists.txt**
   - 更新了MOTION_SYSTEM_SRCS，包含新的文件路径
   - 移除了SERVO_180_SRCS变量
   - 更新了ALL_SRCS配置

2. **custom_hardware.cc**
   - 更新了包含路径：
     ```cpp
     #include "motion_system/servo_180.h"
     #include "motion_system/voice_servo_integration.h"
     ```

## 📋 分类原则

### 按功能模块分类
- **AI层** - 人工智能相关功能
- **运动系统** - 舵机、电机、运动控制相关
- **表情系统** - 显示、表情、眼部渲染相关
- **传感器系统** - 各类传感器和数据处理
- **设备层** - 设备抽象和管理
- **硬件层** - 底层硬件驱动

### 避免外层设备文件
- 所有硬件设备相关的文件都放在对应的子文件夹中
- 根目录只保留系统级的控制和配置文件
- 确保模块间的清晰分离

## 🔧 编译配置更新

### CMakeLists.txt 变更
```cmake
# 运动系统源文件（包含180度舵机控制器）
set(MOTION_SYSTEM_SRCS
    motion_system/motion_controller.cc
    motion_system/servo_controller.cc
    motion_system/motor_controller.cc
    motion_system/person_follow_controller.cc
    motion_system/servo_180.cc                    # ⭐ 新增
    motion_system/voice_servo_integration.cc     # ⭐ 新增
)

# 所有源文件（移除SERVO_180_SRCS引用）
set(ALL_SRCS
    custom_hardware.cc
    ${AI_LAYER_SRCS}
    ${CORE_LAYER_SRCS}
    ${DEVICE_LAYER_SRCS}
    ${HARDWARE_LAYER_SRCS}
    ${EXPRESSION_SYSTEM_SRCS}
    ${MOTION_SYSTEM_SRCS}                        # ⭐ 包含新文件
    ${SENSOR_SYSTEM_SRCS}
    ${SYSTEM_CONTROLLER_SRCS}
    ${DEMO_SRCS}
)
```

### 包含目录配置
```cmake
set(INCLUDE_DIRS
    .
    ai_layer
    core_layer
    device_layer
    hardware_layer
    expression_system
    motion_system          # ⭐ 确保包含motion_system
    sensor_system
)
```

## 🎯 整理效果

### 结构清晰度提升
- ✅ 按功能模块清晰分类
- ✅ 避免根目录文件混乱
- ✅ 便于后续维护和扩展

### 编译兼容性
- ✅ 更新了所有相关的包含路径
- ✅ CMakeLists.txt正确配置
- ✅ 保持编译兼容性

### 文档同步更新
- ✅ 更新了项目总结文档
- ✅ 更新了舵机升级说明
- ✅ 创建了本整理总结文档

## 🚀 后续编译步骤

1. **清理构建缓存**
   ```bash
   idf.py fullclean
   ```

2. **重新配置**
   ```bash
   idf.py menuconfig
   ```

3. **编译项目**
   ```bash
   idf.py build
   ```

4. **验证编译**
   - 检查所有文件是否正确包含
   - 验证链接是否成功
   - 确认没有路径错误

## 📝 注意事项

### 开发时注意
- 新增motion_system相关文件时，记得更新CMakeLists.txt
- 包含头文件时使用正确的路径：`motion_system/servo_180.h`
- 保持模块间的清晰边界

### 维护建议
- 定期检查文件分类是否合理
- 避免在根目录添加设备相关文件
- 保持文档与实际结构同步

---

**整理完成时间**：2025年1月  
**整理目标**：结构清晰，便于维护  
**编译状态**：待验证  
**文档状态**：已同步更新
