/**
 * @file device_interface.h
 * @brief 设备抽象层接口定义
 * 
 * 封装硬件驱动，提供标准化接口，屏蔽底层硬件差异
 * 通过MCP通信协议与硬件交互
 */

#ifndef DEVICE_INTERFACE_H
#define DEVICE_INTERFACE_H

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <map>

namespace device_layer {

/**
 * @brief 设备状态枚举
 */
enum class DeviceState {
    UNINITIALIZED,     // 未初始化
    INITIALIZING,      // 初始化中
    READY,             // 就绪
    BUSY,              // 忙碌
    ERROR,             // 错误
    OFFLINE            // 离线
};

/**
 * @brief 设备类型枚举
 */
enum class DeviceType {
    DISPLAY,           // 显示设备
    SERVO,             // 舵机
    MOTOR,             // 电机
    SENSOR,            // 传感器
    AUDIO,             // 音频设备
    STORAGE,           // 存储设备
    COMMUNICATION,     // 通信设备
    UNKNOWN            // 未知设备
};

/**
 * @brief 设备信息结构
 */
struct DeviceInfo {
    std::string id;              // 设备ID
    std::string name;            // 设备名称
    DeviceType type;             // 设备类型
    DeviceState state;           // 设备状态
    std::string version;         // 版本信息
    std::string description;     // 设备描述
    std::map<std::string, std::string> properties;  // 设备属性
    
    DeviceInfo() : type(DeviceType::UNKNOWN), state(DeviceState::UNINITIALIZED) {}
};

/**
 * @brief 设备回调接口
 */
class DeviceCallback {
public:
    virtual ~DeviceCallback() = default;
    
    // 设备状态变化回调
    virtual void OnDeviceStateChanged(const std::string& device_id, 
                                    DeviceState old_state, DeviceState new_state) = 0;
    
    // 设备数据回调
    virtual void OnDeviceDataReceived(const std::string& device_id, 
                                    const std::string& data) = 0;
    
    // 设备错误回调
    virtual void OnDeviceError(const std::string& device_id, 
                             const std::string& error_message) = 0;
};

/**
 * @brief 基础设备接口
 */
class IDevice {
public:
    virtual ~IDevice() = default;
    
    /**
     * @brief 初始化设备
     * @return 是否成功
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 反初始化设备
     */
    virtual void Deinitialize() = 0;
    
    /**
     * @brief 获取设备信息
     * @return 设备信息
     */
    virtual DeviceInfo GetDeviceInfo() const = 0;
    
    /**
     * @brief 获取设备状态
     * @return 设备状态
     */
    virtual DeviceState GetState() const = 0;
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    virtual void SetCallback(std::shared_ptr<DeviceCallback> callback) = 0;
    
    /**
     * @brief 执行命令
     * @param command 命令字符串
     * @param parameters 参数
     * @return 是否成功
     */
    virtual bool ExecuteCommand(const std::string& command, 
                              const std::map<std::string, std::string>& parameters) = 0;
    
    /**
     * @brief 读取数据
     * @param data_type 数据类型
     * @return 数据字符串
     */
    virtual std::string ReadData(const std::string& data_type) = 0;
    
    /**
     * @brief 写入数据
     * @param data_type 数据类型
     * @param data 数据内容
     * @return 是否成功
     */
    virtual bool WriteData(const std::string& data_type, const std::string& data) = 0;
    
    /**
     * @brief 检查设备健康状态
     * @return 是否健康
     */
    virtual bool IsHealthy() const = 0;
    
    /**
     * @brief 重置设备
     * @return 是否成功
     */
    virtual bool Reset() = 0;
};

/**
 * @brief 显示设备接口
 */
class IDisplayDevice : public IDevice {
public:
    /**
     * @brief 显示表情
     * @param emotion 表情类型
     * @param duration 持续时间(毫秒)
     * @return 是否成功
     */
    virtual bool ShowEmotion(const std::string& emotion, uint32_t duration = 0) = 0;
    
    /**
     * @brief 显示文本
     * @param text 文本内容
     * @param x X坐标
     * @param y Y坐标
     * @return 是否成功
     */
    virtual bool ShowText(const std::string& text, int x = 0, int y = 0) = 0;
    
    /**
     * @brief 清屏
     * @return 是否成功
     */
    virtual bool Clear() = 0;
    
    /**
     * @brief 设置亮度
     * @param brightness 亮度值(0-100)
     * @return 是否成功
     */
    virtual bool SetBrightness(uint8_t brightness) = 0;
    
    /**
     * @brief 播放动画
     * @param animation_name 动画名称
     * @param loop 是否循环
     * @return 是否成功
     */
    virtual bool PlayAnimation(const std::string& animation_name, bool loop = false) = 0;
};

/**
 * @brief 舵机设备接口
 */
class IServoDevice : public IDevice {
public:
    /**
     * @brief 设置角度
     * @param servo_id 舵机ID
     * @param angle 角度值(0-180)
     * @param speed 速度
     * @return 是否成功
     */
    virtual bool SetAngle(const std::string& servo_id, float angle, uint8_t speed = 50) = 0;
    
    /**
     * @brief 执行动作
     * @param servo_id 舵机ID
     * @param action 动作名称
     * @param parameters 动作参数
     * @return 是否成功
     */
    virtual bool ExecuteAction(const std::string& servo_id, const std::string& action,
                             const std::map<std::string, std::string>& parameters) = 0;
    
    /**
     * @brief 停止舵机
     * @param servo_id 舵机ID
     * @return 是否成功
     */
    virtual bool Stop(const std::string& servo_id) = 0;
    
    /**
     * @brief 获取当前角度
     * @param servo_id 舵机ID
     * @return 当前角度
     */
    virtual float GetCurrentAngle(const std::string& servo_id) const = 0;
};

/**
 * @brief 电机设备接口
 */
class IMotorDevice : public IDevice {
public:
    /**
     * @brief 设置速度
     * @param motor_id 电机ID
     * @param speed 速度(-100到100)
     * @return 是否成功
     */
    virtual bool SetSpeed(const std::string& motor_id, int8_t speed) = 0;
    
    /**
     * @brief 移动
     * @param direction 方向
     * @param speed 速度
     * @param duration 持续时间(毫秒)
     * @return 是否成功
     */
    virtual bool Move(const std::string& direction, uint8_t speed, uint32_t duration = 0) = 0;
    
    /**
     * @brief 转向
     * @param angle 转向角度
     * @param speed 速度
     * @return 是否成功
     */
    virtual bool Turn(float angle, uint8_t speed) = 0;
    
    /**
     * @brief 停止所有电机
     * @return 是否成功
     */
    virtual bool StopAll() = 0;
    
    /**
     * @brief 获取当前速度
     * @param motor_id 电机ID
     * @return 当前速度
     */
    virtual int8_t GetCurrentSpeed(const std::string& motor_id) const = 0;
};

/**
 * @brief 传感器设备接口
 */
class ISensorDevice : public IDevice {
public:
    /**
     * @brief 开始检测
     * @param sensor_id 传感器ID
     * @return 是否成功
     */
    virtual bool StartDetection(const std::string& sensor_id) = 0;
    
    /**
     * @brief 停止检测
     * @param sensor_id 传感器ID
     * @return 是否成功
     */
    virtual bool StopDetection(const std::string& sensor_id) = 0;
    
    /**
     * @brief 获取传感器数据
     * @param sensor_id 传感器ID
     * @return 传感器数据
     */
    virtual std::string GetSensorData(const std::string& sensor_id) = 0;
    
    /**
     * @brief 设置检测参数
     * @param sensor_id 传感器ID
     * @param parameters 参数
     * @return 是否成功
     */
    virtual bool SetDetectionParameters(const std::string& sensor_id,
                                      const std::map<std::string, std::string>& parameters) = 0;
};

/**
 * @brief 设备管理器
 */
class DeviceManager {
public:
    static DeviceManager& GetInstance();
    
    /**
     * @brief 初始化设备管理器
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 注册设备
     * @param device 设备实例
     * @return 是否成功
     */
    bool RegisterDevice(std::shared_ptr<IDevice> device);
    
    /**
     * @brief 注销设备
     * @param device_id 设备ID
     * @return 是否成功
     */
    bool UnregisterDevice(const std::string& device_id);
    
    /**
     * @brief 获取设备
     * @param device_id 设备ID
     * @return 设备实例
     */
    std::shared_ptr<IDevice> GetDevice(const std::string& device_id);
    
    /**
     * @brief 获取指定类型的设备列表
     * @param type 设备类型
     * @return 设备列表
     */
    std::vector<std::shared_ptr<IDevice>> GetDevicesByType(DeviceType type);
    
    /**
     * @brief 获取所有设备信息
     * @return 设备信息列表
     */
    std::vector<DeviceInfo> GetAllDeviceInfo() const;
    
    /**
     * @brief 设置全局回调
     * @param callback 回调接口
     */
    void SetGlobalCallback(std::shared_ptr<DeviceCallback> callback);

private:
    DeviceManager() = default;
    ~DeviceManager() = default;
    DeviceManager(const DeviceManager&) = delete;
    DeviceManager& operator=(const DeviceManager&) = delete;
    
    std::map<std::string, std::shared_ptr<IDevice>> devices_;
    std::shared_ptr<DeviceCallback> global_callback_;
    bool initialized_;
};

} // namespace device_layer

#endif // DEVICE_INTERFACE_H
