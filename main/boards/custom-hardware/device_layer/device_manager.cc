/**
 * @file device_manager.cc
 * @brief 设备管理器实现
 */

#include "device_interface.h"
#include <esp_log.h>

static const char* TAG = "DEV_MGR";

namespace device_layer {

DeviceManager& DeviceManager::GetInstance() {
    static DeviceManager instance;
    return instance;
}

bool DeviceManager::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "Device manager already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing device manager...");
    
    initialized_ = true;
    
    ESP_LOGI(TAG, "Device manager initialized successfully");
    return true;
}

bool DeviceManager::RegisterDevice(std::shared_ptr<IDevice> device) {
    if (!device) {
        ESP_LOGE(TAG, "Invalid device pointer");
        return false;
    }
    
    DeviceInfo info = device->GetDeviceInfo();
    if (devices_.find(info.id) != devices_.end()) {
        ESP_LOGW(TAG, "Device %s already registered", info.id.c_str());
        return false;
    }
    
    devices_[info.id] = device;
    ESP_LOGI(TAG, "Registered device: %s", info.id.c_str());
    
    return true;
}

bool DeviceManager::UnregisterDevice(const std::string& device_id) {
    auto it = devices_.find(device_id);
    if (it == devices_.end()) {
        ESP_LOGW(TAG, "Device %s not found", device_id.c_str());
        return false;
    }
    
    devices_.erase(it);
    ESP_LOGI(TAG, "Unregistered device: %s", device_id.c_str());
    
    return true;
}

std::shared_ptr<IDevice> DeviceManager::GetDevice(const std::string& device_id) {
    auto it = devices_.find(device_id);
    if (it != devices_.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::vector<std::shared_ptr<IDevice>> DeviceManager::GetDevicesByType(DeviceType type) {
    std::vector<std::shared_ptr<IDevice>> result;
    
    for (const auto& pair : devices_) {
        if (pair.second->GetDeviceInfo().type == type) {
            result.push_back(pair.second);
        }
    }
    
    return result;
}

std::vector<DeviceInfo> DeviceManager::GetAllDeviceInfo() const {
    std::vector<DeviceInfo> result;
    
    for (const auto& pair : devices_) {
        result.push_back(pair.second->GetDeviceInfo());
    }
    
    return result;
}

void DeviceManager::SetGlobalCallback(std::shared_ptr<DeviceCallback> callback) {
    global_callback_ = callback;
    
    // 为所有已注册的设备设置回调
    for (const auto& pair : devices_) {
        pair.second->SetCallback(callback);
    }
}

} // namespace device_layer
