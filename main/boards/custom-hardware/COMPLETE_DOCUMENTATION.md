# AI玩具系统完整文档

## 🎯 项目概述

基于ESP32-S3 R16N8开发板构建的智能AI玩具系统，采用四层架构设计，实现语音交互、表情显示、运动控制、人物跟随等功能。

## 🏗️ 系统架构

### 四层架构设计

```
┌─────────────────────────────────────────┐
│           小智AI交互层                    │
│     (ai_layer/ai_interface.h)          │
├─────────────────────────────────────────┤
│           核心任务层                      │
│    (core_layer/task_manager.h)         │
├─────────────────────────────────────────┤
│           设备抽象层                      │
│   (device_layer/device_interface.h)    │
├─────────────────────────────────────────┤
│           硬件驱动层                      │
│  (hardware_layer/mcp_communication.h)  │
└─────────────────────────────────────────┘
```

### 核心组件

- **MainSystemController** - 系统总控制器
- **UnifiedExpressionController** - 统一表情控制
- **VoiceServoController** - 语音舵机集成
- **XiaozhiAI** - AI交互引擎
- **SensorManager** - 传感器管理
- **DeviceManager** - 设备管理

## 📁 文件结构

```
main/boards/custom-hardware/
├── ai_layer/                    # AI交互层
│   ├── ai_interface.h          # AI接口定义
│   └── xiaozhi_ai_complete.cc  # 小智AI实现
├── core_layer/                 # 核心任务层
│   ├── task_manager.h          # 任务管理器
│   └── task_manager.cc
├── device_layer/               # 设备抽象层
│   ├── device_interface.h      # 设备接口
│   └── device_manager.cc       # 设备管理器
├── hardware_layer/             # 硬件驱动层
│   ├── mcp_communication.h     # MCP通信
│   └── mcp_communication.cc
├── expression_system/          # 表情系统
│   ├── expression_manager.h    # 表情管理器
│   └── dual_screen_expression.cc
├── motion_system/              # 运动系统
│   ├── servo_180.h/cc         # 180度舵机控制
│   ├── voice_servo_integration.h/cc # 语音舵机集成
│   └── motion_controller.cc
├── sensor_system/              # 传感器系统
│   ├── sensor_manager.h        # 传感器管理器
│   └── sensor_manager.cc
├── system_controller.h/cc      # 系统主控制器
├── custom_hardware.cc          # 硬件初始化
└── COMPLETE_DOCUMENTATION.md   # 本文档
```

## 🚀 核心功能

### 1. 表情系统
- **双屏显示**: 支持左右眼独立控制
- **丰富表情**: 开心、伤心、惊讶、困惑等多种表情
- **动画效果**: 眨眼、脉冲、跟随等动画
- **实时渲染**: 基于LVGL的高效图形渲染

### 2. 语音交互
- **语音识别**: 集成小智AI语音识别
- **意图理解**: 智能解析用户指令
- **情感回复**: 根据情感状态生成回复
- **多模态交互**: 语音+表情+动作协同

### 3. 运动控制（180度舵机）
- **精确角度控制**: 180度标准舵机，精度±2度
- **平滑运动**: 智能分步运动算法，避免突然跳跃
- **语音控制**: 支持自然语言指令控制舵机动作
- **双臂协调**: 同步和异步运动模式
- **支持命令**: "挥手"、"举手"、"敬礼"、"跳舞"、"停止"、"重置"

### 4. 传感器融合
- **多传感器**: 毫米波雷达、超声波、红外
- **数据融合**: 多源数据综合分析
- **环境感知**: 障碍物、悬崖、人物检测
- **实时响应**: 快速的环境变化响应

## 🔧 硬件配置

### 主控制器
- **MCU**: ESP32-S3 R16N8
- **内存**: 16MB Flash + 8MB PSRAM
- **通信**: WiFi + Bluetooth

### 扩展芯片
- **MCP23017**: I2C GPIO扩展芯片
- **地址**: 0x20 (可配置)
- **引脚**: 16个GPIO扩展引脚

### 外设配置
- **显示屏**: 双240x240 LCD屏幕
- **舵机**: 180度标准舵机 - 左臂(GPIO9) + 右臂(GPIO10)
- **电机**: 差分驱动轮式底盘
- **传感器**: 前置毫米波雷达 + 多方向超声波

## 🎮 语音控制使用指南

### 唤醒小智
```
"你好小智"
```

### 舵机控制命令
```
"挥手"     # 执行挥手动作
"举手"     # 执行举手动作  
"敬礼"     # 执行敬礼动作
"跳舞"     # 执行跳舞动作
"停止"     # 停止所有动作
"重置"     # 重置舵机位置
```

## 🛠️ 开发指南

### 环境搭建

1. **安装ESP-IDF**
   ```bash
   git clone https://github.com/espressif/esp-idf.git
   cd esp-idf
   ./install.sh
   . ./export.sh
   ```

2. **编译烧录**
   ```bash
   cd xiaozhi-esp32
   idf.py build
   idf.py flash monitor
   ```

### 添加新功能

1. **定义接口**
   ```cpp
   class INewFeature {
   public:
       virtual bool Initialize() = 0;
       virtual bool Execute() = 0;
   };
   ```

2. **实现功能**
   ```cpp
   class NewFeatureImpl : public INewFeature {
       bool Initialize() override { /* 实现 */ }
       bool Execute() override { /* 实现 */ }
   };
   ```

## 🐛 故障排除

### 常见问题
1. **编译错误**: 检查依赖库和头文件路径
2. **运行异常**: 查看串口日志输出
3. **通信失败**: 检查I2C连接和地址配置
4. **显示异常**: 验证LVGL配置和显示驱动
5. **表情系统失败**: 检查双屏连接和LVGL初始化

### 调试技巧
- 使用串口监控查看实时日志
- 通过LED指示灯观察系统状态
- 分模块测试定位问题范围
- 使用示波器检查硬件信号

## 📊 项目状态

### ✅ 已完成功能
- 180度舵机控制系统
- 语音识别和处理
- 系统架构和接口设计
- 语音控制集成
- 文档整理

### 🔄 待完善功能
- 表情系统初始化
- MCP通信优化
- 屏幕显示调试
- 传感器数据融合

## 📝 更新日志

### v1.0.0 (2025-07-12)
- ✅ 完成基础架构设计
- ✅ 实现180度舵机控制
- ✅ 集成语音控制功能
- ✅ 删除演示代码
- ✅ 整理项目文档

---

*本文档持续更新中，最新版本请查看项目仓库。*
