/**
 * @file servo_180.cc
 * @brief 180度舵机控制器实现
 */

#include "servo_180.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <cmath>
#include <algorithm>

static const char* TAG = "Servo180";

namespace servo_180 {

// 默认SG90 180度舵机PWM范围（微秒）
static const servo_180_pwm_range_t default_pwm_range = {
    .min_pulse_width_us = 500,      // 0.5ms - 0度
    .max_pulse_width_us = 2500,     // 2.5ms - 180度
    .neutral_pulse_width_us = 1500  // 1.5ms - 90度
};

#define SERVO_PWM_FREQ 50  // 50Hz
#define SERVO_PWM_PERIOD_US (1000000 / SERVO_PWM_FREQ)

Servo180::Servo180(gpio_num_t gpio, mcpwm_timer_handle_t timer, 
                   const servo_180_pwm_range_t* pwm_range, bool reverse)
    : gpio_(gpio), timer_(timer), oper_(nullptr), cmpr_(nullptr), gen_(nullptr),
      reverse_(reverse), initialized_(false), current_angle_(90.0f), target_angle_(90.0f),
      state_(ServoState::IDLE), min_angle_limit_(0.0f), max_angle_limit_(180.0f),
      move_task_(nullptr) {
    
    ESP_LOGI(TAG, "Servo180 构造: GPIO=%d, Timer=%p, Reverse=%d", gpio_, timer_, reverse_);
    
    if (gpio_ == GPIO_NUM_NC || timer_ == nullptr) {
        ESP_LOGE(TAG, "无效参数: GPIO=%d, Timer=%p", gpio_, timer_);
        return;
    }
    
    // 设置PWM范围
    if (pwm_range) {
        pwm_range_ = *pwm_range;
        ESP_LOGI(TAG, "使用自定义PWM范围: min=%lu, neutral=%lu, max=%lu", 
                 pwm_range_.min_pulse_width_us, pwm_range_.neutral_pulse_width_us, pwm_range_.max_pulse_width_us);
    } else {
        pwm_range_ = default_pwm_range;
        ESP_LOGI(TAG, "使用默认PWM范围: min=%lu, neutral=%lu, max=%lu", 
                 pwm_range_.min_pulse_width_us, pwm_range_.neutral_pulse_width_us, pwm_range_.max_pulse_width_us);
    }
    
    // 初始化PWM
    esp_err_t err = SetupPWM();
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "PWM初始化失败，GPIO: %d, 错误: %d", gpio_, err);
        return;
    }
    
    // 设置初始位置为90度
    SetPulseWidth(pwm_range_.neutral_pulse_width_us);
    initialized_ = true;
    
    ESP_LOGI(TAG, "Servo180初始化成功，GPIO: %d", gpio_);
}

Servo180::~Servo180() {
    ESP_LOGI(TAG, "Servo180析构: GPIO=%d", gpio_);
    
    if (move_task_) {
        vTaskDelete(move_task_);
        move_task_ = nullptr;
    }
    
    if (initialized_) {
        Stop();
        if (gen_) mcpwm_del_generator(gen_);
        if (cmpr_) mcpwm_del_comparator(cmpr_);
        if (oper_) mcpwm_del_operator(oper_);
        ESP_LOGI(TAG, "PWM资源已清理，GPIO: %d", gpio_);
    }
}

esp_err_t Servo180::SetupPWM() {
    esp_err_t err;
    
    // 创建操作器
    mcpwm_operator_config_t oper_config = { .group_id = 0 };
    err = mcpwm_new_operator(&oper_config, &oper_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "创建MCPWM操作器失败，GPIO: %d, 错误: %d", gpio_, err);
        return err;
    }
    
    // 连接定时器
    err = mcpwm_operator_connect_timer(oper_, timer_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "连接定时器失败，GPIO: %d, 错误: %d", gpio_, err);
        mcpwm_del_operator(oper_);
        oper_ = nullptr;
        return err;
    }
    
    // 创建比较器
    mcpwm_comparator_config_t cmpr_config = {};
    cmpr_config.flags.update_cmp_on_tez = true;
    err = mcpwm_new_comparator(oper_, &cmpr_config, &cmpr_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "创建比较器失败，GPIO: %d, 错误: %d", gpio_, err);
        mcpwm_del_operator(oper_);
        oper_ = nullptr;
        return err;
    }
    
    // 创建生成器
    mcpwm_generator_config_t gen_config = { .gen_gpio_num = gpio_ };
    err = mcpwm_new_generator(oper_, &gen_config, &gen_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "创建生成器失败，GPIO: %d, 错误: %d", gpio_, err);
        mcpwm_del_comparator(cmpr_);
        mcpwm_del_operator(oper_);
        cmpr_ = nullptr;
        oper_ = nullptr;
        return err;
    }
    
    // 设置PWM动作
    err = mcpwm_generator_set_action_on_timer_event(gen_, 
        MCPWM_GEN_TIMER_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, MCPWM_TIMER_EVENT_EMPTY, MCPWM_GEN_ACTION_HIGH));
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "设置定时器事件动作失败，GPIO: %d, 错误: %d", gpio_, err);
        return err;
    }
    
    err = mcpwm_generator_set_action_on_compare_event(gen_,
        MCPWM_GEN_COMPARE_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, cmpr_, MCPWM_GEN_ACTION_LOW));
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "设置比较事件动作失败，GPIO: %d, 错误: %d", gpio_, err);
        return err;
    }
    
    ESP_LOGI(TAG, "PWM设置完成，GPIO: %d", gpio_);
    return ESP_OK;
}

void Servo180::SetPulseWidth(uint32_t pulse_width_us) {
    if (!initialized_ || !cmpr_) {
        ESP_LOGE(TAG, "舵机未初始化或比较器无效");
        return;
    }
    
    // 限制脉冲宽度范围
    pulse_width_us = std::max(pulse_width_us, pwm_range_.min_pulse_width_us);
    pulse_width_us = std::min(pulse_width_us, pwm_range_.max_pulse_width_us);
    
    esp_err_t err = mcpwm_comparator_set_compare_value(cmpr_, pulse_width_us);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "设置PWM脉冲宽度失败: %lu us, 错误: %d", pulse_width_us, err);
    }
}

uint32_t Servo180::AngleToPulseWidth(float angle) {
    // 限制角度范围
    angle = std::max(angle, 0.0f);
    angle = std::min(angle, 180.0f);
    
    // 如果反向，翻转角度
    if (reverse_) {
        angle = 180.0f - angle;
    }
    
    // 线性映射角度到脉冲宽度
    uint32_t pulse_width = pwm_range_.min_pulse_width_us + 
        (uint32_t)((angle / 180.0f) * (pwm_range_.max_pulse_width_us - pwm_range_.min_pulse_width_us));
    
    return pulse_width;
}

float Servo180::PulseWidthToAngle(uint32_t pulse_width) {
    // 限制脉冲宽度范围
    pulse_width = std::max(pulse_width, pwm_range_.min_pulse_width_us);
    pulse_width = std::min(pulse_width, pwm_range_.max_pulse_width_us);
    
    // 线性映射脉冲宽度到角度
    float angle = ((float)(pulse_width - pwm_range_.min_pulse_width_us) / 
                   (pwm_range_.max_pulse_width_us - pwm_range_.min_pulse_width_us)) * 180.0f;
    
    // 如果反向，翻转角度
    if (reverse_) {
        angle = 180.0f - angle;
    }
    
    return angle;
}

bool Servo180::IsAngleValid(float angle) {
    return (angle >= min_angle_limit_ && angle <= max_angle_limit_);
}

bool Servo180::SetAngle(float angle, uint8_t speed, bool smooth) {
    if (!initialized_) {
        ESP_LOGE(TAG, "舵机未初始化");
        return false;
    }
    
    if (!IsAngleValid(angle)) {
        ESP_LOGW(TAG, "角度超出限制: %.1f (限制: %.1f - %.1f)", 
                 angle, min_angle_limit_, max_angle_limit_);
        return false;
    }
    
    target_angle_ = angle;
    
    if (smooth && speed < 100) {
        // 平滑运动
        if (move_task_) {
            vTaskDelete(move_task_);
        }
        
        BaseType_t ret = xTaskCreate(MoveTask, "servo_move", 2048, this, 5, &move_task_);
        if (ret != pdPASS) {
            ESP_LOGE(TAG, "创建运动任务失败");
            return false;
        }
        
        state_ = ServoState::MOVING;
    } else {
        // 直接移动
        uint32_t pulse_width = AngleToPulseWidth(angle);
        SetPulseWidth(pulse_width);
        current_angle_ = angle;
        state_ = ServoState::HOLDING;
        
        if (move_complete_callback_) {
            move_complete_callback_(current_angle_);
        }
    }
    
    ESP_LOGI(TAG, "设置角度: %.1f度, 速度: %d, 平滑: %s", 
             angle, speed, smooth ? "是" : "否");
    
    return true;
}

float Servo180::GetCurrentAngle() const {
    return current_angle_;
}

void Servo180::Stop() {
    if (move_task_) {
        vTaskDelete(move_task_);
        move_task_ = nullptr;
    }
    
    state_ = ServoState::IDLE;
    ESP_LOGI(TAG, "舵机停止，GPIO: %d", gpio_);
}

bool Servo180::Wave(int count, uint8_t speed, float amplitude) {
    if (!initialized_) {
        return false;
    }
    
    ESP_LOGI(TAG, "执行挥手动作: %d次, 速度: %d, 幅度: %.1f度", count, speed, amplitude);
    
    float center_angle = current_angle_;
    float left_angle = center_angle - amplitude / 2;
    float right_angle = center_angle + amplitude / 2;
    
    // 确保角度在有效范围内
    left_angle = std::max(left_angle, min_angle_limit_);
    right_angle = std::min(right_angle, max_angle_limit_);
    
    for (int i = 0; i < count; i++) {
        SetAngle(right_angle, speed, true);
        WaitForMoveComplete(2000);
        
        SetAngle(left_angle, speed, true);
        WaitForMoveComplete(2000);
    }
    
    // 回到中心位置
    SetAngle(center_angle, speed, true);
    WaitForMoveComplete(2000);
    
    return true;
}

bool Servo180::RaiseArm(float angle, uint8_t speed) {
    ESP_LOGI(TAG, "举手到角度: %.1f度", angle);
    return SetAngle(angle, speed, true);
}

bool Servo180::LowerArm(uint8_t speed) {
    ESP_LOGI(TAG, "放下手臂");
    return SetAngle(30.0f, speed, true);  // 放下到30度位置
}

bool Servo180::Salute(uint8_t speed) {
    ESP_LOGI(TAG, "执行敬礼动作");
    
    // 敬礼动作：快速举到120度，保持2秒，然后放下
    if (!SetAngle(120.0f, speed, true)) {
        return false;
    }
    
    WaitForMoveComplete(2000);
    vTaskDelay(pdMS_TO_TICKS(2000));  // 保持2秒
    
    return LowerArm(speed);
}

bool Servo180::Point(float angle, uint8_t speed) {
    ESP_LOGI(TAG, "指向角度: %.1f度", angle);
    return SetAngle(angle, speed, true);
}

void Servo180::SetAngleLimits(float min_angle, float max_angle) {
    min_angle_limit_ = std::max(min_angle, 0.0f);
    max_angle_limit_ = std::min(max_angle, 180.0f);
    
    ESP_LOGI(TAG, "设置角度限制: %.1f - %.1f度", min_angle_limit_, max_angle_limit_);
}

ServoState Servo180::GetState() const {
    return state_;
}

void Servo180::SetMoveCompleteCallback(std::function<void(float)> callback) {
    move_complete_callback_ = callback;
}

bool Servo180::WaitForMoveComplete(uint32_t timeout_ms) {
    uint32_t start_time = xTaskGetTickCount();
    uint32_t timeout_ticks = pdMS_TO_TICKS(timeout_ms);
    
    while (state_ == ServoState::MOVING) {
        if ((xTaskGetTickCount() - start_time) > timeout_ticks) {
            ESP_LOGW(TAG, "等待运动完成超时");
            return false;
        }
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    return true;
}

void Servo180::MoveTask(void* parameter) {
    Servo180* servo = static_cast<Servo180*>(parameter);
    
    float start_angle = servo->current_angle_;
    float target_angle = servo->target_angle_;
    float angle_diff = target_angle - start_angle;
    
    const int steps = 20;  // 分20步完成运动
    const int step_delay = 50;  // 每步延时50ms
    
    for (int i = 1; i <= steps; i++) {
        float current_step_angle = start_angle + (angle_diff * i / steps);
        uint32_t pulse_width = servo->AngleToPulseWidth(current_step_angle);
        servo->SetPulseWidth(pulse_width);
        servo->current_angle_ = current_step_angle;
        
        vTaskDelay(pdMS_TO_TICKS(step_delay));
    }
    
    servo->current_angle_ = target_angle;
    servo->state_ = ServoState::HOLDING;
    
    if (servo->move_complete_callback_) {
        servo->move_complete_callback_(servo->current_angle_);
    }
    
    servo->move_task_ = nullptr;
    vTaskDelete(nullptr);
}

// 辅助函数实现
std::string ServoActionToString(ServoAction action) {
    switch (action) {
        case ServoAction::STOP: return "STOP";
        case ServoAction::MOVE_TO_ANGLE: return "MOVE_TO_ANGLE";
        case ServoAction::WAVE: return "WAVE";
        case ServoAction::RAISE: return "RAISE";
        case ServoAction::LOWER: return "LOWER";
        case ServoAction::SALUTE: return "SALUTE";
        case ServoAction::POINT: return "POINT";
        case ServoAction::SWEEP: return "SWEEP";
        case ServoAction::CUSTOM: return "CUSTOM";
        default: return "UNKNOWN";
    }
}

ServoAction StringToServoAction(const std::string& action_str) {
    if (action_str == "STOP") return ServoAction::STOP;
    if (action_str == "MOVE_TO_ANGLE") return ServoAction::MOVE_TO_ANGLE;
    if (action_str == "WAVE") return ServoAction::WAVE;
    if (action_str == "RAISE") return ServoAction::RAISE;
    if (action_str == "LOWER") return ServoAction::LOWER;
    if (action_str == "SALUTE") return ServoAction::SALUTE;
    if (action_str == "POINT") return ServoAction::POINT;
    if (action_str == "SWEEP") return ServoAction::SWEEP;
    if (action_str == "CUSTOM") return ServoAction::CUSTOM;
    return ServoAction::STOP;
}

std::string ServoStateToString(ServoState state) {
    switch (state) {
        case ServoState::IDLE: return "IDLE";
        case ServoState::MOVING: return "MOVING";
        case ServoState::HOLDING: return "HOLDING";
        case ServoState::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

// Servo180Controller实现
Servo180Controller::Servo180Controller(gpio_num_t left_gpio, gpio_num_t right_gpio)
    : shared_timer_(nullptr), initialized_(false) {

    ESP_LOGI(TAG, "Servo180Controller构造: 左臂GPIO=%d, 右臂GPIO=%d", left_gpio, right_gpio);

    // 初始化定时器
    if (!InitializeTimer()) {
        ESP_LOGE(TAG, "定时器初始化失败");
        return;
    }

    // 创建左臂舵机
    left_servo_ = std::make_unique<Servo180>(left_gpio, shared_timer_);
    if (!left_servo_->IsInitialized()) {
        ESP_LOGE(TAG, "左臂舵机初始化失败");
        return;
    }

    // 创建右臂舵机
    right_servo_ = std::make_unique<Servo180>(right_gpio, shared_timer_);
    if (!right_servo_->IsInitialized()) {
        ESP_LOGE(TAG, "右臂舵机初始化失败");
        return;
    }

    initialized_ = true;
    ESP_LOGI(TAG, "Servo180Controller初始化成功");
}

Servo180Controller::~Servo180Controller() {
    ESP_LOGI(TAG, "Servo180Controller析构");

    if (left_servo_) {
        left_servo_.reset();
    }

    if (right_servo_) {
        right_servo_.reset();
    }

    CleanupTimer();
}

bool Servo180Controller::InitializeTimer() {
    mcpwm_timer_config_t timer_config = {};
    timer_config.group_id = 0;
    timer_config.clk_src = MCPWM_TIMER_CLK_SRC_DEFAULT;
    timer_config.resolution_hz = 1000000;  // 1MHz, 1us per tick
    timer_config.period_ticks = SERVO_PWM_PERIOD_US;
    timer_config.count_mode = MCPWM_TIMER_COUNT_MODE_UP;

    esp_err_t err = mcpwm_new_timer(&timer_config, &shared_timer_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "创建MCPWM定时器失败: %d", err);
        return false;
    }

    err = mcpwm_timer_enable(shared_timer_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "启用MCPWM定时器失败: %d", err);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
        return false;
    }

    err = mcpwm_timer_start_stop(shared_timer_, MCPWM_TIMER_START_NO_STOP);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "启动MCPWM定时器失败: %d", err);
        mcpwm_timer_disable(shared_timer_);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
        return false;
    }

    ESP_LOGI(TAG, "MCPWM定时器初始化成功");
    return true;
}

void Servo180Controller::CleanupTimer() {
    if (shared_timer_) {
        mcpwm_timer_start_stop(shared_timer_, MCPWM_TIMER_STOP_EMPTY);
        mcpwm_timer_disable(shared_timer_);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
        ESP_LOGI(TAG, "MCPWM定时器已清理");
    }
}

bool Servo180Controller::Initialize() {
    return initialized_;
}

bool Servo180Controller::SetBothArms(float left_angle, float right_angle, uint8_t speed, bool synchronized) {
    if (!initialized_) {
        ESP_LOGE(TAG, "控制器未初始化");
        return false;
    }

    ESP_LOGI(TAG, "设置双臂角度: 左=%.1f度, 右=%.1f度, 速度=%d, 同步=%s",
             left_angle, right_angle, speed, synchronized ? "是" : "否");

    if (synchronized) {
        // 同步运动：同时启动，等待都完成
        bool left_ok = left_servo_->SetAngle(left_angle, speed, true);
        bool right_ok = right_servo_->SetAngle(right_angle, speed, true);

        if (left_ok && right_ok) {
            left_servo_->WaitForMoveComplete(5000);
            right_servo_->WaitForMoveComplete(5000);
            return true;
        }
        return false;
    } else {
        // 异步运动：分别执行
        bool left_ok = left_servo_->SetAngle(left_angle, speed, true);
        bool right_ok = right_servo_->SetAngle(right_angle, speed, true);
        return left_ok && right_ok;
    }
}

bool Servo180Controller::SetSingleArm(const std::string& target, float angle, uint8_t speed) {
    if (!initialized_) {
        ESP_LOGE(TAG, "控制器未初始化");
        return false;
    }

    ESP_LOGI(TAG, "设置单臂角度: %s=%.1f度, 速度=%d", target.c_str(), angle, speed);

    if (target == "left" || target == "左臂") {
        return left_servo_->SetAngle(angle, speed, true);
    } else if (target == "right" || target == "右臂") {
        return right_servo_->SetAngle(angle, speed, true);
    } else {
        ESP_LOGW(TAG, "未知的目标臂: %s", target.c_str());
        return false;
    }
}

bool Servo180Controller::DualWave(int count, uint8_t speed, bool synchronized) {
    if (!initialized_) {
        return false;
    }

    ESP_LOGI(TAG, "双臂挥手: %d次, 速度=%d, 同步=%s", count, speed, synchronized ? "是" : "否");

    if (synchronized) {
        // 同步挥手：双臂同时挥动
        for (int i = 0; i < count; i++) {
            SetBothArms(120.0f, 120.0f, speed, true);  // 同时举起
            vTaskDelay(pdMS_TO_TICKS(500));

            SetBothArms(60.0f, 60.0f, speed, true);   // 同时放下
            vTaskDelay(pdMS_TO_TICKS(500));
        }

        // 回到中性位置
        SetBothArms(90.0f, 90.0f, speed, true);
    } else {
        // 异步挥手：交替挥动
        for (int i = 0; i < count; i++) {
            left_servo_->Wave(1, speed, 60.0f);
            vTaskDelay(pdMS_TO_TICKS(200));
            right_servo_->Wave(1, speed, 60.0f);
            vTaskDelay(pdMS_TO_TICKS(200));
        }
    }

    return true;
}

bool Servo180Controller::DualRaise(float angle, uint8_t speed) {
    ESP_LOGI(TAG, "双臂举手到: %.1f度", angle);
    return SetBothArms(angle, angle, speed, true);
}

bool Servo180Controller::DualSalute(uint8_t speed) {
    ESP_LOGI(TAG, "双臂敬礼");

    // 双臂敬礼：举到120度，保持2秒，然后放下
    if (!SetBothArms(120.0f, 120.0f, speed, true)) {
        return false;
    }

    vTaskDelay(pdMS_TO_TICKS(2000));  // 保持2秒

    return SetBothArms(30.0f, 30.0f, speed, true);  // 放下
}

void Servo180Controller::StopAll() {
    if (left_servo_) {
        left_servo_->Stop();
    }

    if (right_servo_) {
        right_servo_->Stop();
    }

    ESP_LOGI(TAG, "所有舵机已停止");
}

} // namespace servo_180
