/**
 * @file ai_toy_demo.h
 * @brief AI玩具系统演示程序头文件
 */

#pragma once

namespace ai_toy_demo {

/**
 * @brief 初始化AI玩具演示
 * @return 成功返回true，失败返回false
 */
bool InitializeDemo();

/**
 * @brief 运行完整演示
 */
void RunFullDemo();

/**
 * @brief 运行基础演示
 */
void RunBasicDemo();

/**
 * @brief 运行高级演示
 */
void RunAdvancedDemo();

/**
 * @brief 显示系统状态
 */
void ShowSystemStatus();

/**
 * @brief 启动AI玩具演示（兼容接口）
 * @return 成功返回true，失败返回false
 */
bool StartAIToyDemo();

/**
 * @brief 清理演示资源
 */
void CleanupDemo();

} // namespace ai_toy_demo
