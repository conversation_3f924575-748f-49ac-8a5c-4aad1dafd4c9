/**
 * @file infrared_sensor.cc
 * @brief 红外传感器完整实现
 */

#include "sensor_manager.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <driver/gpio.h>
#include <driver/adc.h>
#include <esp_adc_cal.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <cmath>
#include <algorithm>

static const char* TAG = "InfraredSensor";

namespace sensor_system {

// 红外传感器类型
enum class IRSensorType {
    DIGITAL,    // 数字红外传感器（有/无检测）
    ANALOG      // 模拟红外传感器（距离测量）
};

// 红外传感器配置
struct InfraredConfig {
    IRSensorType type;
    gpio_num_t gpio_pin;
    adc1_channel_t adc_channel;
    float max_distance;         // 最大检测距离(米)
    float min_distance;         // 最小检测距离(米)
    uint32_t sample_count;      // 采样次数
    uint32_t sample_interval;   // 采样间隔(毫秒)

    InfraredConfig() : type(IRSensorType::DIGITAL), gpio_pin(GPIO_NUM_NC),
                      adc_channel(ADC1_CHANNEL_0), max_distance(0.8f), min_distance(0.04f),
                      sample_count(5), sample_interval(10) {}
};

class InfraredSensorImpl : public ISensor {
public:
    InfraredSensorImpl(const std::string& id, gpio_num_t gpio_pin, IRSensorType type = IRSensorType::DIGITAL)
        : sensor_id_(id), initialized_(false), last_value_(0.0f) {
        config_.gpio_pin = gpio_pin;
        config_.type = type;

        // 根据GPIO设置ADC通道
        if (type == IRSensorType::ANALOG) {
            SetADCChannel(gpio_pin);
        }
    }

    ~InfraredSensorImpl() {
        if (initialized_) {
            Cleanup();
        }
    }

    bool Initialize() override {
        if (initialized_) {
            ESP_LOGW(TAG, "传感器%s已初始化", sensor_id_.c_str());
            return true;
        }

        ESP_LOGI(TAG, "初始化红外传感器%s: GPIO=%d, 类型=%s",
                 sensor_id_.c_str(), config_.gpio_pin,
                 (config_.type == IRSensorType::DIGITAL) ? "数字" : "模拟");

        if (config_.type == IRSensorType::DIGITAL) {
            return InitializeDigital();
        } else {
            return InitializeAnalog();
        }
    }

    bool ReadData(SensorData& data) override {
        if (!initialized_) {
            ESP_LOGE(TAG, "传感器%s未初始化", sensor_id_.c_str());
            return false;
        }

        float value = 0.0f;
        bool success = false;

        if (config_.type == IRSensorType::DIGITAL) {
            success = ReadDigitalValue(value);
            data.data_type = SensorDataType::PRESENCE;
            data.unit = "bool";
        } else {
            success = ReadAnalogDistance(value);
            data.data_type = SensorDataType::DISTANCE;
            data.unit = "m";
        }

        if (!success) {
            ESP_LOGW(TAG, "传感器%s读取失败", sensor_id_.c_str());
            return false;
        }

        // 填充传感器数据
        data.sensor_id = sensor_id_;
        data.timestamp = esp_timer_get_time();
        data.values.clear();
        data.values.push_back(value);
        data.quality = CalculateDataQuality(value);

        last_value_ = value;

        ESP_LOGD(TAG, "传感器%s读取: %.3f %s", sensor_id_.c_str(), value, data.unit.c_str());

        return true;
    }

    bool IsHealthy() const override {
        return initialized_;
    }

    std::string GetSensorId() const override {
        return sensor_id_;
    }

    SensorType GetSensorType() const override {
        return SensorType::INFRARED;
    }

    bool Calibrate() override {
        ESP_LOGI(TAG, "校准红外传感器%s", sensor_id_.c_str());

        if (config_.type == IRSensorType::ANALOG) {
            return CalibrateAnalog();
        } else {
            return CalibrateDigital();
        }
    }

    bool SetConfig(const std::string& key, const std::string& value) override {
        if (key == "max_distance") {
            config_.max_distance = std::stof(value);
            ESP_LOGI(TAG, "设置最大距离: %.2fm", config_.max_distance);
            return true;
        } else if (key == "min_distance") {
            config_.min_distance = std::stof(value);
            ESP_LOGI(TAG, "设置最小距离: %.2fm", config_.min_distance);
            return true;
        } else if (key == "sample_count") {
            config_.sample_count = std::stoul(value);
            ESP_LOGI(TAG, "设置采样次数: %d", config_.sample_count);
            return true;
        }

        ESP_LOGW(TAG, "未知配置项: %s", key.c_str());
        return false;
    }

private:
    std::string sensor_id_;
    InfraredConfig config_;
    bool initialized_;
    float last_value_;
    esp_adc_cal_characteristics_t adc_chars_;

    bool InitializeDigital() {
        // 配置GPIO为输入
        gpio_config_t io_conf = {};
        io_conf.intr_type = GPIO_INTR_DISABLE;
        io_conf.mode = GPIO_MODE_INPUT;
        io_conf.pin_bit_mask = (1ULL << config_.gpio_pin);
        io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
        io_conf.pull_up_en = GPIO_PULLUP_ENABLE;

        esp_err_t ret = gpio_config(&io_conf);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "GPIO配置失败: %d", ret);
            return false;
        }

        initialized_ = true;
        ESP_LOGI(TAG, "数字红外传感器%s初始化成功", sensor_id_.c_str());
        return true;
    }

    bool InitializeAnalog() {
        // 配置ADC
        esp_err_t ret = adc1_config_width(ADC_WIDTH_BIT_12);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "ADC宽度配置失败: %d", ret);
            return false;
        }

        ret = adc1_config_channel_atten(config_.adc_channel, ADC_ATTEN_DB_11);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "ADC通道配置失败: %d", ret);
            return false;
        }

        // 校准ADC
        esp_adc_cal_value_t val_type = esp_adc_cal_characterize(
            ADC_UNIT_1, ADC_ATTEN_DB_11, ADC_WIDTH_BIT_12, 1100, &adc_chars_);

        if (val_type == ESP_ADC_CAL_VAL_EFUSE_VREF) {
            ESP_LOGI(TAG, "使用eFuse Vref校准");
        } else if (val_type == ESP_ADC_CAL_VAL_EFUSE_TP) {
            ESP_LOGI(TAG, "使用eFuse Two Point校准");
        } else {
            ESP_LOGI(TAG, "使用默认Vref校准");
        }

        initialized_ = true;
        ESP_LOGI(TAG, "模拟红外传感器%s初始化成功", sensor_id_.c_str());
        return true;
    }

    bool ReadDigitalValue(float& value) {
        int level = gpio_get_level(config_.gpio_pin);
        value = (level == 1) ? 1.0f : 0.0f;
        return true;
    }

    bool ReadAnalogDistance(float& distance) {
        // 多次采样求平均
        uint32_t total_raw = 0;
        uint32_t valid_samples = 0;

        for (uint32_t i = 0; i < config_.sample_count; i++) {
            int raw_value = adc1_get_raw(config_.adc_channel);
            if (raw_value >= 0) {
                total_raw += raw_value;
                valid_samples++;
            }

            if (i < config_.sample_count - 1) {
                vTaskDelay(pdMS_TO_TICKS(config_.sample_interval));
            }
        }

        if (valid_samples == 0) {
            ESP_LOGE(TAG, "ADC读取失败");
            return false;
        }

        uint32_t avg_raw = total_raw / valid_samples;

        // 转换为电压
        uint32_t voltage = esp_adc_cal_raw_to_voltage(avg_raw, &adc_chars_);

        // 根据传感器特性转换为距离
        distance = ConvertVoltageToDistance(voltage);

        ESP_LOGD(TAG, "ADC原始值: %d, 电压: %dmV, 距离: %.3fm", avg_raw, voltage, distance);

        return true;
    }

    float ConvertVoltageToDistance(uint32_t voltage_mv) {
        // GP2Y0A21YK红外距离传感器的典型转换公式
        // 距离(cm) = 27.728 * (电压(V))^(-1.2045)

        float voltage_v = voltage_mv / 1000.0f;

        if (voltage_v < 0.4f) {
            // 电压太低，可能超出测量范围
            return config_.max_distance;
        }

        float distance_cm = 27.728f * pow(voltage_v, -1.2045f);
        float distance_m = distance_cm / 100.0f;

        // 限制在有效范围内
        distance_m = std::clamp(distance_m, config_.min_distance, config_.max_distance);

        return distance_m;
    }

    bool CalibrateDigital() {
        ESP_LOGI(TAG, "校准数字红外传感器");

        // 数字传感器校准：检查信号稳定性
        const int test_samples = 20;
        int high_count = 0;

        for (int i = 0; i < test_samples; i++) {
            int level = gpio_get_level(config_.gpio_pin);
            if (level == 1) {
                high_count++;
            }
            vTaskDelay(pdMS_TO_TICKS(50));
        }

        float stability = static_cast<float>(std::max(high_count, test_samples - high_count)) / test_samples;

        ESP_LOGI(TAG, "信号稳定性: %.2f", stability);

        return stability > 0.8f;  // 80%以上稳定性认为校准成功
    }

    bool CalibrateAnalog() {
        ESP_LOGI(TAG, "校准模拟红外传感器");

        // 模拟传感器校准：测量多个距离点
        const int calibration_samples = 50;
        uint32_t total_raw = 0;
        int valid_samples = 0;

        for (int i = 0; i < calibration_samples; i++) {
            int raw_value = adc1_get_raw(config_.adc_channel);
            if (raw_value >= 0) {
                total_raw += raw_value;
                valid_samples++;
            }
            vTaskDelay(pdMS_TO_TICKS(20));
        }

        if (valid_samples < calibration_samples * 0.8f) {
            ESP_LOGE(TAG, "校准失败，有效样本不足");
            return false;
        }

        uint32_t avg_raw = total_raw / valid_samples;
        uint32_t voltage = esp_adc_cal_raw_to_voltage(avg_raw, &adc_chars_);
        float distance = ConvertVoltageToDistance(voltage);

        ESP_LOGI(TAG, "校准完成，平均ADC: %d, 电压: %dmV, 距离: %.3fm",
                 avg_raw, voltage, distance);

        return true;
    }

    float CalculateDataQuality(float value) {
        if (config_.type == IRSensorType::DIGITAL) {
            return 1.0f;  // 数字传感器质量固定
        }

        // 模拟传感器质量评估
        if (value < config_.min_distance || value > config_.max_distance) {
            return 0.0f;  // 超出范围
        }

        if (value < 0.1f) {
            return 0.6f;  // 太近，可能不准确
        }

        if (value > config_.max_distance * 0.8f) {
            return 0.7f;  // 接近最大距离
        }

        return 1.0f;  // 理想范围
    }

    void SetADCChannel(gpio_num_t gpio_pin) {
        // 根据GPIO引脚设置对应的ADC通道
        switch (gpio_pin) {
            case GPIO_NUM_36: config_.adc_channel = ADC1_CHANNEL_0; break;
            case GPIO_NUM_37: config_.adc_channel = ADC1_CHANNEL_1; break;
            case GPIO_NUM_38: config_.adc_channel = ADC1_CHANNEL_2; break;
            case GPIO_NUM_39: config_.adc_channel = ADC1_CHANNEL_3; break;
            case GPIO_NUM_32: config_.adc_channel = ADC1_CHANNEL_4; break;
            case GPIO_NUM_33: config_.adc_channel = ADC1_CHANNEL_5; break;
            case GPIO_NUM_34: config_.adc_channel = ADC1_CHANNEL_6; break;
            case GPIO_NUM_35: config_.adc_channel = ADC1_CHANNEL_7; break;
            default:
                ESP_LOGW(TAG, "GPIO %d不支持ADC，使用默认通道", gpio_pin);
                config_.adc_channel = ADC1_CHANNEL_0;
                break;
        }
    }

    void Cleanup() {
        initialized_ = false;
    }
};

// 工厂函数
std::unique_ptr<ISensor> CreateInfraredSensor(const std::string& id,
                                             gpio_num_t gpio_pin,
                                             bool is_analog) {
    IRSensorType type = is_analog ? IRSensorType::ANALOG : IRSensorType::DIGITAL;
    return std::make_unique<InfraredSensorImpl>(id, gpio_pin, type);
}

} // namespace sensor_system
