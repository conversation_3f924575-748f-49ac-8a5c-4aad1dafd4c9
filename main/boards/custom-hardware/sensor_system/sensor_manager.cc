/**
 * @file sensor_manager.cc
 * @brief 传感器管理器完整实现
 */

#include "sensor_manager.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <algorithm>
#include <cmath>
#include <memory>

static const char* TAG = "SensorManager";

namespace sensor_system {

// 传感器管理器实现类
class SensorManagerImpl {
public:
    SensorManagerImpl() : initialized_(false), fusion_enabled_(true),
                         data_queue_(nullptr), fusion_task_(nullptr) {}

    ~SensorManagerImpl() {
        if (fusion_task_) {
            vTaskDelete(fusion_task_);
        }
        if (data_queue_) {
            vQueueDelete(data_queue_);
        }
    }

    bool Initialize() {
        if (initialized_) {
            ESP_LOGW(TAG, "传感器管理器已初始化");
            return true;
        }

        ESP_LOGI(TAG, "初始化传感器管理器...");

        // 创建数据队列
        data_queue_ = xQueueCreate(20, sizeof(SensorData));
        if (!data_queue_) {
            ESP_LOGE(TAG, "创建数据队列失败");
            return false;
        }

        // 创建数据融合任务
        BaseType_t ret = xTaskCreate(DataFusionTask, "sensor_fusion", 4096, this, 4, &fusion_task_);
        if (ret != pdPASS) {
            ESP_LOGE(TAG, "创建数据融合任务失败");
            return false;
        }

        initialized_ = true;
        ESP_LOGI(TAG, "传感器管理器初始化成功");

        return true;
    }

    bool AddSensor(std::unique_ptr<ISensor> sensor) {
        if (!sensor) {
            ESP_LOGE(TAG, "传感器指针为空");
            return false;
        }

        std::string sensor_id = sensor->GetSensorId();

        // 检查是否已存在
        if (sensors_.find(sensor_id) != sensors_.end()) {
            ESP_LOGW(TAG, "传感器%s已存在", sensor_id.c_str());
            return false;
        }

        // 初始化传感器
        if (!sensor->Initialize()) {
            ESP_LOGE(TAG, "传感器%s初始化失败", sensor_id.c_str());
            return false;
        }

        sensors_[sensor_id] = std::move(sensor);
        ESP_LOGI(TAG, "添加传感器: %s", sensor_id.c_str());

        return true;
    }

    std::vector<SensorData> ReadAllSensors() {
        std::vector<SensorData> all_data;

        for (const auto& pair : sensors_) {
            SensorData data;
            if (pair.second->ReadData(data)) {
                all_data.push_back(data);

                if (fusion_enabled_) {
                    // 发送到融合队列
                    if (xQueueSend(data_queue_, &data, 0) != pdTRUE) {
                        ESP_LOGW(TAG, "数据队列已满");
                    }
                }
            }
        }

        return all_data;
    }

    SensorSystemStatus GetSystemStatus() const {
        SensorSystemStatus status;
        status.total_sensors = sensors_.size();
        status.healthy_sensors = 0;
        status.fusion_enabled = fusion_enabled_;

        for (const auto& pair : sensors_) {
            if (pair.second->IsHealthy()) {
                status.healthy_sensors++;
            }
        }

        status.system_health = (status.total_sensors > 0) ?
            static_cast<float>(status.healthy_sensors) / status.total_sensors : 0.0f;

        return status;
    }

private:
    bool initialized_;
    bool fusion_enabled_;
    std::map<std::string, std::unique_ptr<ISensor>> sensors_;

    // 数据融合
    QueueHandle_t data_queue_;
    TaskHandle_t fusion_task_;

    // 数据融合任务
    static void DataFusionTask(void* parameter) {
        SensorManagerImpl* manager = static_cast<SensorManagerImpl*>(parameter);
        SensorData data;

        ESP_LOGI(TAG, "数据融合任务启动");

        while (true) {
            if (xQueueReceive(manager->data_queue_, &data, pdMS_TO_TICKS(100)) == pdTRUE) {
                // 处理传感器数据
                ESP_LOGD(TAG, "处理传感器数据: %s", data.sensor_id.c_str());
            }

            vTaskDelay(pdMS_TO_TICKS(100));
        }
    }
};

// 全局实例
static std::unique_ptr<SensorManagerImpl> g_sensor_manager_impl = nullptr;

SensorManager& SensorManager::GetInstance() {
    static SensorManager instance;
    return instance;
}

bool SensorManager::Initialize() {
    if (!g_sensor_manager_impl) {
        g_sensor_manager_impl = std::make_unique<SensorManagerImpl>();
    }

    bool success = g_sensor_manager_impl->Initialize();
    if (success) {
        initialized_ = true;
    }

    return success;
}

bool SensorManager::AddSensor(std::unique_ptr<ISensor> sensor) {
    if (!g_sensor_manager_impl) {
        ESP_LOGE(TAG, "传感器管理器未初始化");
        return false;
    }

    return g_sensor_manager_impl->AddSensor(std::move(sensor));
}

std::vector<SensorData> SensorManager::ReadAllSensors() {
    if (!g_sensor_manager_impl) {
        ESP_LOGE(TAG, "传感器管理器未初始化");
        return {};
    }

    return g_sensor_manager_impl->ReadAllSensors();
}

SensorSystemStatus SensorManager::GetSystemStatus() const {
    if (!g_sensor_manager_impl) {
        SensorSystemStatus status;
        status.total_sensors = 0;
        status.healthy_sensors = 0;
        status.system_health = 0.0f;
        status.fusion_enabled = false;
        return status;
    }

    return g_sensor_manager_impl->GetSystemStatus();
}

std::string SensorTypeToString(SensorType type) {
    switch (type) {
        case SensorType::ULTRASONIC: return "ULTRASONIC";
        case SensorType::INFRARED: return "INFRARED";
        case SensorType::MILLIMETER_WAVE: return "MILLIMETER_WAVE";
        case SensorType::GYROSCOPE: return "GYROSCOPE";
        case SensorType::ACCELEROMETER: return "ACCELEROMETER";
        case SensorType::MAGNETOMETER: return "MAGNETOMETER";
        case SensorType::TEMPERATURE: return "TEMPERATURE";
        case SensorType::HUMIDITY: return "HUMIDITY";
        case SensorType::LIGHT: return "LIGHT";
        case SensorType::UNKNOWN: return "UNKNOWN";
        default: return "UNKNOWN";
    }
}

} // namespace sensor_system
