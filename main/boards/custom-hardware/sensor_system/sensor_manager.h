/**
 * @file sensor_manager.h
 * @brief 传感器系统管理器
 * 
 * 负责管理各种传感器：毫米波雷达、超声波传感器、红外传感器
 * 实现避障、避险、人物检测功能
 */

#ifndef SENSOR_MANAGER_H
#define SENSOR_MANAGER_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

namespace sensor_system {

/**
 * @brief 传感器类型枚举
 */
enum class SensorType {
    ULTRASONIC,        // 超声波传感器
    INFRARED,          // 红外传感器
    MILLIMETER_WAVE,   // 毫米波雷达
    GYROSCOPE,         // 陀螺仪
    ACCELEROMETER,     // 加速度计
    MAGNETOMETER,      // 磁力计
    TEMPERATURE,       // 温度传感器
    HUMIDITY,          // 湿度传感器
    LIGHT,             // 光线传感器
    UNKNOWN            // 未知类型
};

/**
 * @brief 传感器状态枚举
 */
enum class SensorState {
    UNINITIALIZED,     // 未初始化
    INITIALIZING,      // 初始化中
    READY,             // 就绪
    ACTIVE,            // 活跃检测中
    ERROR,             // 错误状态
    OFFLINE            // 离线
};

/**
 * @brief 检测结果类型
 */
enum class DetectionType {
    NO_DETECTION,      // 无检测
    OBSTACLE,          // 障碍物
    PERSON,            // 人体
    CLIFF,             // 悬崖
    WALL,              // 墙壁
    MOVING_OBJECT,     // 移动物体
    STATIC_OBJECT,     // 静态物体
    UNKNOWN_OBJECT     // 未知物体
};

/**
 * @brief 传感器数据结构
 */
struct SensorData {
    std::string sensor_id;         // 传感器ID
    SensorType type;               // 传感器类型
    DetectionType detection_type;  // 检测类型
    float distance;                // 距离(米)
    float angle;                   // 角度(度)
    float confidence;              // 置信度(0.0-1.0)
    uint32_t timestamp;            // 时间戳
    std::map<std::string, float> raw_data;  // 原始数据
    
    SensorData() : type(SensorType::UNKNOWN), detection_type(DetectionType::NO_DETECTION),
                  distance(0.0f), angle(0.0f), confidence(0.0f), timestamp(0) {}
};

/**
 * @brief 传感器配置结构
 */
struct SensorConfig {
    std::string id;                // 传感器ID
    SensorType type;               // 传感器类型
    std::string name;              // 传感器名称
    uint8_t trigger_pin;           // 触发引脚
    uint8_t echo_pin;              // 回声引脚
    uint8_t tx_pin;                // 发送引脚
    uint8_t rx_pin;                // 接收引脚
    float min_range;               // 最小检测范围(米)
    float max_range;               // 最大检测范围(米)
    float detection_angle;         // 检测角度(度)
    uint32_t sample_rate;          // 采样率(Hz)
    float threshold;               // 检测阈值
    bool enable_filter;            // 是否启用滤波
    std::string description;       // 描述
    
    SensorConfig() : type(SensorType::UNKNOWN), trigger_pin(0), echo_pin(0),
                    tx_pin(0), rx_pin(0), min_range(0.0f), max_range(5.0f),
                    detection_angle(60.0f), sample_rate(10), threshold(0.5f),
                    enable_filter(true) {}
};

/**
 * @brief 避障参数
 */
struct ObstacleAvoidanceParams {
    float safe_distance;           // 安全距离(米)
    float warning_distance;        // 警告距离(米)
    float emergency_distance;      // 紧急距离(米)
    float side_detection_angle;    // 侧面检测角度(度)
    uint8_t avoidance_speed;       // 避障速度
    uint32_t detection_timeout;    // 检测超时时间(ms)
    bool enable_side_sensors;      // 是否启用侧面传感器
    
    ObstacleAvoidanceParams() : safe_distance(0.5f), warning_distance(0.3f),
                               emergency_distance(0.1f), side_detection_angle(45.0f),
                               avoidance_speed(30), detection_timeout(1000),
                               enable_side_sensors(true) {}
};

/**
 * @brief 人物检测参数
 */
struct PersonDetectionParams {
    float min_detection_distance;  // 最小检测距离(米)
    float max_detection_distance;  // 最大检测距离(米)
    float detection_sensitivity;   // 检测灵敏度
    uint32_t confirmation_time;    // 确认时间(ms)
    uint32_t lost_timeout;         // 丢失超时时间(ms)
    bool enable_movement_filter;   // 是否启用运动滤波
    float movement_threshold;      // 运动阈值
    
    PersonDetectionParams() : min_detection_distance(0.5f), max_detection_distance(8.0f),
                             detection_sensitivity(0.8f), confirmation_time(500),
                             lost_timeout(2000), enable_movement_filter(true),
                             movement_threshold(0.1f) {}
};

/**
 * @brief 传感器回调接口
 */
class SensorCallback {
public:
    virtual ~SensorCallback() = default;
    
    // 传感器数据更新回调
    virtual void OnSensorDataUpdated(const SensorData& data) = 0;
    
    // 障碍物检测回调
    virtual void OnObstacleDetected(const SensorData& data) = 0;
    
    // 悬崖检测回调
    virtual void OnCliffDetected(const SensorData& data) = 0;
    
    // 人物检测回调
    virtual void OnPersonDetected(const SensorData& data) = 0;
    
    // 人物丢失回调
    virtual void OnPersonLost(const std::string& sensor_id) = 0;
    
    // 传感器状态变化回调
    virtual void OnSensorStateChanged(const std::string& sensor_id, 
                                    SensorState old_state, SensorState new_state) = 0;
    
    // 传感器错误回调
    virtual void OnSensorError(const std::string& sensor_id, const std::string& error_message) = 0;
};

/**
 * @brief 基础传感器接口
 */
class ISensor {
public:
    virtual ~ISensor() = default;
    
    /**
     * @brief 初始化传感器
     * @param config 传感器配置
     * @return 是否成功
     */
    virtual bool Initialize(const SensorConfig& config) = 0;
    
    /**
     * @brief 开始检测
     * @return 是否成功
     */
    virtual bool StartDetection() = 0;
    
    /**
     * @brief 停止检测
     * @return 是否成功
     */
    virtual bool StopDetection() = 0;
    
    /**
     * @brief 读取传感器数据
     * @return 传感器数据
     */
    virtual SensorData ReadData() = 0;
    
    /**
     * @brief 获取传感器状态
     * @return 传感器状态
     */
    virtual SensorState GetState() const = 0;
    
    /**
     * @brief 获取传感器配置
     * @return 传感器配置
     */
    virtual SensorConfig GetConfig() const = 0;
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    virtual void SetCallback(std::shared_ptr<SensorCallback> callback) = 0;
    
    /**
     * @brief 校准传感器
     * @return 是否成功
     */
    virtual bool Calibrate() = 0;
    
    /**
     * @brief 重置传感器
     * @return 是否成功
     */
    virtual bool Reset() = 0;
};

/**
 * @brief 超声波传感器类
 */
class UltrasonicSensor : public ISensor {
public:
    UltrasonicSensor();
    ~UltrasonicSensor();
    
    bool Initialize(const SensorConfig& config) override;
    bool StartDetection() override;
    bool StopDetection() override;
    SensorData ReadData() override;
    SensorState GetState() const override;
    SensorConfig GetConfig() const override;
    void SetCallback(std::shared_ptr<SensorCallback> callback) override;
    bool Calibrate() override;
    bool Reset() override;

private:
    SensorConfig config_;
    SensorState state_;
    std::shared_ptr<SensorCallback> callback_;
    TaskHandle_t detection_task_;
    bool detection_active_;
    mutable std::mutex mutex_;
    
    static void DetectionTask(void* parameter);
    float MeasureDistance();
    void ProcessData(float distance);
};

/**
 * @brief 红外传感器类
 */
class InfraredSensor : public ISensor {
public:
    InfraredSensor();
    ~InfraredSensor();
    
    bool Initialize(const SensorConfig& config) override;
    bool StartDetection() override;
    bool StopDetection() override;
    SensorData ReadData() override;
    SensorState GetState() const override;
    SensorConfig GetConfig() const override;
    void SetCallback(std::shared_ptr<SensorCallback> callback) override;
    bool Calibrate() override;
    bool Reset() override;

private:
    SensorConfig config_;
    SensorState state_;
    std::shared_ptr<SensorCallback> callback_;
    TaskHandle_t detection_task_;
    bool detection_active_;
    mutable std::mutex mutex_;
    
    static void DetectionTask(void* parameter);
    bool DetectCliff();
    void ProcessData(bool cliff_detected);
};

/**
 * @brief 毫米波雷达传感器类
 */
class MillimeterWaveRadar : public ISensor {
public:
    MillimeterWaveRadar();
    ~MillimeterWaveRadar();
    
    bool Initialize(const SensorConfig& config) override;
    bool StartDetection() override;
    bool StopDetection() override;
    SensorData ReadData() override;
    SensorState GetState() const override;
    SensorConfig GetConfig() const override;
    void SetCallback(std::shared_ptr<SensorCallback> callback) override;
    bool Calibrate() override;
    bool Reset() override;
    
    /**
     * @brief 设置检测参数
     * @param params 人物检测参数
     * @return 是否成功
     */
    bool SetDetectionParameters(const PersonDetectionParams& params);

private:
    SensorConfig config_;
    SensorState state_;
    std::shared_ptr<SensorCallback> callback_;
    PersonDetectionParams detection_params_;
    TaskHandle_t detection_task_;
    bool detection_active_;
    mutable std::mutex mutex_;
    
    // 雷达数据缓存
    std::vector<SensorData> data_buffer_;
    uint32_t last_person_detection_time_;
    
    static void DetectionTask(void* parameter);
    bool ParseRadarData(const std::string& raw_data, SensorData& sensor_data);
    void ProcessPersonDetection(const SensorData& data);
    bool FilterMovement(const SensorData& data);
    void SendUARTCommand(const std::string& command);
    std::string ReadUARTResponse();
};

/**
 * @brief 传感器管理器
 */
class SensorManager {
public:
    static SensorManager& GetInstance();
    
    /**
     * @brief 初始化传感器管理器
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 注册传感器
     * @param sensor 传感器实例
     * @return 是否成功
     */
    bool RegisterSensor(std::shared_ptr<ISensor> sensor);
    
    /**
     * @brief 注销传感器
     * @param sensor_id 传感器ID
     * @return 是否成功
     */
    bool UnregisterSensor(const std::string& sensor_id);
    
    /**
     * @brief 开始所有传感器检测
     * @return 是否成功
     */
    bool StartAllDetection();
    
    /**
     * @brief 停止所有传感器检测
     * @return 是否成功
     */
    bool StopAllDetection();
    
    /**
     * @brief 开始指定传感器检测
     * @param sensor_id 传感器ID
     * @return 是否成功
     */
    bool StartDetection(const std::string& sensor_id);
    
    /**
     * @brief 停止指定传感器检测
     * @param sensor_id 传感器ID
     * @return 是否成功
     */
    bool StopDetection(const std::string& sensor_id);
    
    /**
     * @brief 获取传感器数据
     * @param sensor_id 传感器ID
     * @return 传感器数据
     */
    SensorData GetSensorData(const std::string& sensor_id);
    
    /**
     * @brief 获取所有传感器数据
     * @return 传感器数据映射
     */
    std::map<std::string, SensorData> GetAllSensorData();
    
    /**
     * @brief 设置避障参数
     * @param params 避障参数
     */
    void SetObstacleAvoidanceParams(const ObstacleAvoidanceParams& params);
    
    /**
     * @brief 设置人物检测参数
     * @param params 人物检测参数
     */
    void SetPersonDetectionParams(const PersonDetectionParams& params);
    
    /**
     * @brief 检查是否有障碍物
     * @return 是否检测到障碍物
     */
    bool HasObstacle() const;
    
    /**
     * @brief 检查是否有悬崖
     * @return 是否检测到悬崖
     */
    bool HasCliff() const;
    
    /**
     * @brief 检查是否检测到人物
     * @return 是否检测到人物
     */
    bool HasPerson() const;
    
    /**
     * @brief 获取最近的障碍物信息
     * @return 障碍物数据
     */
    SensorData GetNearestObstacle() const;
    
    /**
     * @brief 获取人物位置信息
     * @return 人物数据
     */
    SensorData GetPersonLocation() const;
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    void SetCallback(std::shared_ptr<SensorCallback> callback);
    
    /**
     * @brief 获取传感器状态
     * @param sensor_id 传感器ID
     * @return 传感器状态
     */
    SensorState GetSensorState(const std::string& sensor_id) const;
    
    /**
     * @brief 获取系统状态
     * @return 状态字符串
     */
    std::string GetSystemStatus() const;

private:
    SensorManager() = default;
    ~SensorManager() = default;
    SensorManager(const SensorManager&) = delete;
    SensorManager& operator=(const SensorManager&) = delete;
    
    std::map<std::string, std::shared_ptr<ISensor>> sensors_;
    std::map<std::string, SensorData> latest_data_;
    std::shared_ptr<SensorCallback> callback_;
    ObstacleAvoidanceParams obstacle_params_;
    PersonDetectionParams person_params_;
    bool initialized_;
    mutable std::mutex mutex_;
    
    // 数据融合和处理
    void ProcessSensorFusion();
    bool ValidateObstacleDetection(const SensorData& data);
    bool ValidatePersonDetection(const SensorData& data);
};

/**
 * @brief 传感器类型转字符串
 */
std::string SensorTypeToString(SensorType type);

/**
 * @brief 传感器状态转字符串
 */
std::string SensorStateToString(SensorState state);

/**
 * @brief 检测类型转字符串
 */
std::string DetectionTypeToString(DetectionType type);

} // namespace sensor_system

#endif // SENSOR_MANAGER_H
