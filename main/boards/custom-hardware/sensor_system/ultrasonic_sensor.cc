/**
 * @file ultrasonic_sensor.cc
 * @brief 超声波传感器完整实现
 */

#include "sensor_manager.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <driver/gpio.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <cmath>

static const char* TAG = "UltrasonicSensor";

namespace sensor_system {

// 超声波传感器配置
struct UltrasonicConfig {
    gpio_num_t trig_pin;
    gpio_num_t echo_pin;
    float max_distance;     // 最大检测距离(米)
    uint32_t timeout_us;    // 超时时间(微秒)

    UltrasonicConfig() : trig_pin(GPIO_NUM_NC), echo_pin(GPIO_NUM_NC),
                        max_distance(4.0f), timeout_us(30000) {}
};

class UltrasonicSensorImpl : public ISensor {
public:
    UltrasonicSensorImpl(const std::string& id, gpio_num_t trig_pin, gpio_num_t echo_pin)
        : sensor_id_(id), initialized_(false), last_distance_(0.0f) {
        config_.trig_pin = trig_pin;
        config_.echo_pin = echo_pin;
    }

    ~UltrasonicSensorImpl() {
        if (initialized_) {
            Cleanup();
        }
    }

    bool Initialize() override {
        if (initialized_) {
            ESP_LOGW(TAG, "传感器%s已初始化", sensor_id_.c_str());
            return true;
        }

        ESP_LOGI(TAG, "初始化超声波传感器%s: TRIG=%d, ECHO=%d",
                 sensor_id_.c_str(), config_.trig_pin, config_.echo_pin);

        // 配置TRIG引脚为输出
        gpio_config_t trig_config = {};
        trig_config.intr_type = GPIO_INTR_DISABLE;
        trig_config.mode = GPIO_MODE_OUTPUT;
        trig_config.pin_bit_mask = (1ULL << config_.trig_pin);
        trig_config.pull_down_en = GPIO_PULLDOWN_DISABLE;
        trig_config.pull_up_en = GPIO_PULLUP_DISABLE;

        esp_err_t ret = gpio_config(&trig_config);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "TRIG引脚配置失败: %d", ret);
            return false;
        }

        // 配置ECHO引脚为输入
        gpio_config_t echo_config = {};
        echo_config.intr_type = GPIO_INTR_ANYEDGE;
        echo_config.mode = GPIO_MODE_INPUT;
        echo_config.pin_bit_mask = (1ULL << config_.echo_pin);
        echo_config.pull_down_en = GPIO_PULLDOWN_ENABLE;
        echo_config.pull_up_en = GPIO_PULLUP_DISABLE;

        ret = gpio_config(&echo_config);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "ECHO引脚配置失败: %d", ret);
            return false;
        }

        // 安装GPIO中断服务
        ret = gpio_install_isr_service(0);
        if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
            ESP_LOGE(TAG, "GPIO中断服务安装失败: %d", ret);
            return false;
        }

        // 添加ECHO引脚中断处理
        ret = gpio_isr_handler_add(config_.echo_pin, EchoISR, this);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "ECHO中断处理器添加失败: %d", ret);
            return false;
        }

        // 初始化TRIG引脚为低电平
        gpio_set_level(config_.trig_pin, 0);

        initialized_ = true;
        ESP_LOGI(TAG, "超声波传感器%s初始化成功", sensor_id_.c_str());

        return true;
    }

    bool ReadData(SensorData& data) override {
        if (!initialized_) {
            ESP_LOGE(TAG, "传感器%s未初始化", sensor_id_.c_str());
            return false;
        }

        float distance = MeasureDistance();

        if (distance < 0) {
            ESP_LOGW(TAG, "传感器%s测距失败", sensor_id_.c_str());
            return false;
        }

        // 填充传感器数据
        data.sensor_id = sensor_id_;
        data.timestamp = esp_timer_get_time();
        data.data_type = SensorDataType::DISTANCE;
        data.values.clear();
        data.values.push_back(distance);
        data.unit = "m";
        data.quality = CalculateDataQuality(distance);

        last_distance_ = distance;

        ESP_LOGD(TAG, "传感器%s测距: %.3fm", sensor_id_.c_str(), distance);

        return true;
    }

    bool IsHealthy() const override {
        return initialized_ && (last_distance_ >= 0);
    }

    std::string GetSensorId() const override {
        return sensor_id_;
    }

    SensorType GetSensorType() const override {
        return SensorType::ULTRASONIC;
    }

    bool Calibrate() override {
        ESP_LOGI(TAG, "校准超声波传感器%s", sensor_id_.c_str());

        // 进行多次测量来校准
        const int calibration_samples = 10;
        float total_distance = 0;
        int valid_samples = 0;

        for (int i = 0; i < calibration_samples; i++) {
            float distance = MeasureDistance();
            if (distance > 0) {
                total_distance += distance;
                valid_samples++;
            }
            vTaskDelay(pdMS_TO_TICKS(100));
        }

        if (valid_samples < calibration_samples / 2) {
            ESP_LOGE(TAG, "校准失败，有效样本不足");
            return false;
        }

        float avg_distance = total_distance / valid_samples;
        ESP_LOGI(TAG, "校准完成，平均距离: %.3fm", avg_distance);

        return true;
    }

    bool SetConfig(const std::string& key, const std::string& value) override {
        if (key == "max_distance") {
            config_.max_distance = std::stof(value);
            ESP_LOGI(TAG, "设置最大距离: %.2fm", config_.max_distance);
            return true;
        } else if (key == "timeout_us") {
            config_.timeout_us = std::stoul(value);
            ESP_LOGI(TAG, "设置超时时间: %dus", config_.timeout_us);
            return true;
        }

        ESP_LOGW(TAG, "未知配置项: %s", key.c_str());
        return false;
    }

private:
    std::string sensor_id_;
    UltrasonicConfig config_;
    bool initialized_;
    float last_distance_;

    // 中断相关
    volatile uint64_t echo_start_time_;
    volatile uint64_t echo_end_time_;
    volatile bool echo_received_;

    float MeasureDistance() {
        // 重置中断标志
        echo_received_ = false;
        echo_start_time_ = 0;
        echo_end_time_ = 0;

        // 发送触发脉冲
        gpio_set_level(config_.trig_pin, 1);
        esp_rom_delay_us(10);  // 10微秒高电平
        gpio_set_level(config_.trig_pin, 0);

        // 等待回波
        uint64_t start_wait = esp_timer_get_time();
        while (!echo_received_ && (esp_timer_get_time() - start_wait) < config_.timeout_us) {
            vTaskDelay(pdMS_TO_TICKS(1));
        }

        if (!echo_received_) {
            ESP_LOGD(TAG, "超时，未收到回波");
            return -1.0f;
        }

        // 计算距离
        uint64_t pulse_duration = echo_end_time_ - echo_start_time_;
        float distance = (pulse_duration * 0.000001f * 343.0f) / 2.0f;  // 声速343m/s

        // 检查距离范围
        if (distance > config_.max_distance) {
            ESP_LOGD(TAG, "距离超出范围: %.3fm", distance);
            return -1.0f;
        }

        return distance;
    }

    static void IRAM_ATTR EchoISR(void* arg) {
        UltrasonicSensorImpl* sensor = static_cast<UltrasonicSensorImpl*>(arg);

        int level = gpio_get_level(sensor->config_.echo_pin);
        uint64_t current_time = esp_timer_get_time();

        if (level == 1) {
            // 上升沿 - 开始计时
            sensor->echo_start_time_ = current_time;
        } else {
            // 下降沿 - 结束计时
            sensor->echo_end_time_ = current_time;
            sensor->echo_received_ = true;
        }
    }

    float CalculateDataQuality(float distance) {
        // 基于距离和稳定性计算数据质量
        if (distance < 0.02f || distance > config_.max_distance) {
            return 0.0f;  // 超出范围
        }

        if (distance < 0.1f) {
            return 0.5f;  // 太近，质量一般
        }

        if (distance > config_.max_distance * 0.9f) {
            return 0.7f;  // 接近最大距离，质量较好
        }

        return 1.0f;  // 理想范围，质量最好
    }

    void Cleanup() {
        if (config_.echo_pin != GPIO_NUM_NC) {
            gpio_isr_handler_remove(config_.echo_pin);
        }
        initialized_ = false;
    }
};

// 工厂函数
std::unique_ptr<ISensor> CreateUltrasonicSensor(const std::string& id,
                                               gpio_num_t trig_pin,
                                               gpio_num_t echo_pin) {
    return std::make_unique<UltrasonicSensorImpl>(id, trig_pin, echo_pin);
}

} // namespace sensor_system
