# Custom Hardware 代码结构文档

## 📁 文件夹结构

```
main/boards/custom-hardware/
├── config.h                    # 硬件配置文件 (已清理)
├── custom_hardware.cc          # 主硬件控制器
├── custom_hardware.h           # 主硬件控制器头文件
├── system_controller.cc        # 系统控制器
├── system_controller.h         # 系统控制器头文件
├── CODE_STRUCTURE.md          # 本文档
├── HARDWARE_PINOUT_CONFIGURATION.md  # 硬件引脚配置文档
│
├── expression_system/          # 表情显示系统
│   ├── simple_dual_screen.h    # 简化双屏表情管理器
│   ├── simple_dual_screen.cc   # 简化双屏表情实现
│   └── README.md              # 表情系统说明
│
├── hardware_layer/            # 硬件抽象层
│   ├── mcp_communication.h    # MCP23017通信接口
│   ├── mcp_communication.cc   # MCP23017通信实现
│   └── README.md             # 硬件层说明
│
├── motion_system/             # 运动控制系统
│   ├── servo_180.h           # 180度舵机控制器
│   ├── servo_180.cc          # 180度舵机实现
│   ├── servo_controller.cc   # 舵机控制器
│   ├── voice_servo_integration.h  # 语音舵机集成
│   ├── voice_servo_integration.cc # 语音舵机实现
│   └── README.md             # 运动系统说明
│
├── sensor_system/             # 传感器系统
│   ├── millimeter_wave_radar.h   # 毫米波雷达
│   ├── millimeter_wave_radar.cc  # 毫米波雷达实现
│   └── README.md             # 传感器系统说明
│
└── ai_layer/                  # AI智能层
    ├── ai_interface.h         # AI接口定义
    ├── xiaozhi_ai_complete.h  # 小智AI完整实现
    ├── xiaozhi_ai_complete.cc # 小智AI完整实现
    └── README.md             # AI层说明
```

## 🔧 核心配置 (config.h)

### 已清理的配置项
- ✅ 音频I2S配置 (保留)
- ✅ 按键GPIO配置 (保留)
- ✅ 双屏表情显示配置 (重新整理)
- ❌ 移除了所有未使用的LCD配置块 (200+行代码)
- ✅ 添加了硬件扩展配置

### 当前有效配置
```c
// 音频系统
#define AUDIO_INPUT_SAMPLE_RATE  16000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000
#define AUDIO_I2S_METHOD_SIMPLEX

// 双屏表情显示 (GC9A01 x2)
#define DISPLAY_WIDTH   240
#define DISPLAY_HEIGHT  240
#define DISPLAY_CS_LEFT_PIN   GPIO_NUM_2
#define DISPLAY_CS_RIGHT_PIN  GPIO_NUM_45

// 硬件扩展
#define SERVO_LEFT_GPIO  GPIO_NUM_8
#define SERVO_RIGHT_GPIO GPIO_NUM_9
#define RADAR_UART_TX_GPIO GPIO_NUM_10
#define RADAR_UART_RX_GPIO GPIO_NUM_11
```

## 🎯 系统架构

### 1. 表情显示系统
- **简化双屏管理器**: 兼容LVGL 9.x
- **双GC9A01显示屏**: 240x240分辨率
- **共享SPI总线**: 独立CS控制

### 2. 运动控制系统
- **180度舵机控制**: 支持左右臂控制
- **语音集成**: 语音命令直接控制舵机
- **MCPWM驱动**: 高精度PWM控制

### 3. 传感器系统
- **毫米波雷达**: 人体检测和距离测量
- **UART通信**: 异步数据处理

### 4. AI智能层
- **意图识别**: 语音命令解析
- **动作映射**: 意图到硬件动作的转换
- **状态管理**: 系统状态跟踪

## 🔄 数据流

```
语音输入 → AI意图识别 → 动作解析 → 硬件控制 → 表情反馈
    ↓           ↓           ↓           ↓           ↓
  音频处理   → 语义理解   → 指令生成   → 舵机动作   → 双屏显示
```

## 📋 编译状态

### ✅ 已修复的问题
- 头文件依赖问题
- 枚举值缺失问题
- 格式化字符串类型问题
- MCPWM配置结构体字段顺序
- 引脚配置冲突

### 🎯 优化成果
- 清理了200+行未使用的LCD配置代码
- 统一了双屏表情系统配置
- 规范了硬件引脚分配
- 完善了代码文档结构

## 🚀 下一步计划

1. **功能测试**: 验证双屏表情显示
2. **性能优化**: 优化LVGL渲染性能
3. **集成测试**: 测试语音控制舵机功能
4. **文档完善**: 补充使用示例和API文档
