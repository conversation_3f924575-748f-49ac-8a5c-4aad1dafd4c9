#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  16000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

// 如果使用 Duplex I2S 模式，请注释下面一行
#define AUDIO_I2S_METHOD_SIMPLEX

#ifdef AUDIO_I2S_METHOD_SIMPLEX

#define AUDIO_I2S_MIC_GPIO_WS   GPIO_NUM_4
#define AUDIO_I2S_MIC_GPIO_SCK  GPIO_NUM_5
#define AUDIO_I2S_MIC_GPIO_DIN  GPIO_NUM_6
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_7
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_15
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_16

#else

#define AUDIO_I2S_GPIO_WS GPIO_NUM_4
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_5
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_6
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_7

#endif


#define BUILTIN_LED_GPIO        GPIO_NUM_48
#define BOOT_BUTTON_GPIO        GPIO_NUM_0
#define TOUCH_BUTTON_GPIO       GPIO_NUM_NC
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_NC


// ========================================
// 双屏表情显示系统配置 (GC9A01 x2)
// ========================================
#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_42  // PWM背光控制
#define DISPLAY_MOSI_PIN      GPIO_NUM_20  // SPI数据输出 (共享)
#define DISPLAY_CLK_PIN       GPIO_NUM_19  // SPI时钟信号 (共享)
#define DISPLAY_DC_PIN        GPIO_NUM_21  // 数据/命令选择 (共享)
#define DISPLAY_RST_PIN       GPIO_NUM_1   // 复位信号 (共享)
#define DISPLAY_CS_LEFT_PIN   GPIO_NUM_2   // 左屏片选信号
#define DISPLAY_CS_RIGHT_PIN  GPIO_NUM_45  // 右屏片选信号

// 双屏显示参数
#define DISPLAY_WIDTH   240
#define DISPLAY_HEIGHT  240
#define DISPLAY_MIRROR_X true
#define DISPLAY_MIRROR_Y false
#define DISPLAY_SWAP_XY false
#define DISPLAY_INVERT_COLOR true
#define DISPLAY_RGB_ORDER LCD_RGB_ELEMENT_ORDER_BGR
#define DISPLAY_OFFSET_X 0
#define DISPLAY_OFFSET_Y 0
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false
#define DISPLAY_SPI_MODE 0

// 兼容性定义 - 默认使用左屏CS
#define DISPLAY_CS_PIN DISPLAY_CS_LEFT_PIN
#define LCD_TYPE_GC9A01_SERIAL


// ========================================
// 硬件扩展配置
// ========================================

// MCP23017 I2C扩展芯片测试 - 控制LED灯
#define LAMP_GPIO GPIO_NUM_18

// 舵机控制系统
#define SERVO_LEFT_GPIO  GPIO_NUM_8   // 左臂舵机
#define SERVO_RIGHT_GPIO GPIO_NUM_9   // 右臂舵机

// 传感器系统
#define RADAR_UART_TX_GPIO GPIO_NUM_10  // 毫米波雷达TX
#define RADAR_UART_RX_GPIO GPIO_NUM_11  // 毫米波雷达RX


#endif // _BOARD_CONFIG_H_
