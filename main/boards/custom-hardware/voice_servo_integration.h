/**
 * @file voice_servo_integration.h
 * @brief 语音控制与180度舵机集成
 * 
 * 将语音指令与180度舵机控制结合，实现自然的语音交互控制
 * 替代原有的360度舵机MCP控制方案
 */

#pragma once
#include "servo_180.h"
#include <string>
#include <map>
#include <functional>

namespace voice_servo_integration {

/**
 * @brief 语音指令类型
 */
enum class VoiceCommand {
    WAVE_HAND,          // 挥手
    RAISE_HAND,         // 举手
    LOWER_HAND,         // 放下手
    SALUTE,             // 敬礼
    POINT_LEFT,         // 指向左边
    POINT_RIGHT,        // 指向右边
    POINT_FORWARD,      // 指向前方
    BOTH_ARMS_UP,       // 双臂举起
    BOTH_ARMS_DOWN,     // 双臂放下
    STOP_MOTION,        // 停止动作
    RESET_POSITION,     // 重置位置
    DANCE,              // 舞蹈动作
    UNKNOWN             // 未知指令
};

/**
 * @brief 语音指令参数
 */
struct VoiceCommandParams {
    std::string target_arm;     // 目标手臂 ("left", "right", "both")
    float angle;                // 目标角度
    uint8_t speed;              // 动作速度 (1-100)
    int repeat_count;           // 重复次数
    bool synchronized;          // 是否同步执行
    
    VoiceCommandParams() : angle(90.0f), speed(50), repeat_count(1), synchronized(true) {}
};

/**
 * @brief 语音舵机集成控制器
 */
class VoiceServoController {
public:
    /**
     * @brief 构造函数
     * @param left_gpio 左臂GPIO
     * @param right_gpio 右臂GPIO
     */
    VoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio);
    
    /**
     * @brief 析构函数
     */
    ~VoiceServoController();
    
    /**
     * @brief 初始化控制器
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 处理语音指令
     * @param voice_text 语音识别文本
     * @return 是否成功执行
     */
    bool ProcessVoiceCommand(const std::string& voice_text);
    
    /**
     * @brief 执行指定的语音指令
     * @param command 指令类型
     * @param params 指令参数
     * @return 是否成功执行
     */
    bool ExecuteVoiceCommand(VoiceCommand command, const VoiceCommandParams& params = VoiceCommandParams());
    
    /**
     * @brief 注册自定义语音指令
     * @param voice_pattern 语音模式（关键词）
     * @param command 对应的指令
     * @param params 默认参数
     * @return 是否成功注册
     */
    bool RegisterCustomCommand(const std::string& voice_pattern, VoiceCommand command, 
                              const VoiceCommandParams& params = VoiceCommandParams());
    
    /**
     * @brief 设置语音指令执行回调
     * @param callback 回调函数
     */
    void SetCommandCallback(std::function<void(VoiceCommand, bool)> callback);
    
    /**
     * @brief 获取舵机控制器
     * @return 舵机控制器指针
     */
    servo_180::Servo180Controller* GetServoController() { return servo_controller_.get(); }
    
    /**
     * @brief 检查是否初始化
     * @return 是否已初始化
     */
    bool IsInitialized() const { return initialized_; }
    
    /**
     * @brief 获取支持的语音指令列表
     * @return 指令列表
     */
    std::vector<std::string> GetSupportedCommands() const;
    
    /**
     * @brief 紧急停止所有动作
     */
    void EmergencyStop();

private:
    std::unique_ptr<servo_180::Servo180Controller> servo_controller_;
    std::map<std::string, std::pair<VoiceCommand, VoiceCommandParams>> command_map_;
    std::function<void(VoiceCommand, bool)> command_callback_;
    bool initialized_;
    
    // 私有方法
    void InitializeDefaultCommands();
    VoiceCommand ParseVoiceText(const std::string& voice_text, VoiceCommandParams& params);
    bool ExecuteWaveCommand(const VoiceCommandParams& params);
    bool ExecuteRaiseCommand(const VoiceCommandParams& params);
    bool ExecuteLowerCommand(const VoiceCommandParams& params);
    bool ExecuteSaluteCommand(const VoiceCommandParams& params);
    bool ExecutePointCommand(VoiceCommand command, const VoiceCommandParams& params);
    bool ExecuteDanceCommand(const VoiceCommandParams& params);
    
    // 语音解析辅助函数
    bool ContainsKeyword(const std::string& text, const std::vector<std::string>& keywords);
    std::string ExtractTargetArm(const std::string& text);
    int ExtractRepeatCount(const std::string& text);
    uint8_t ExtractSpeed(const std::string& text);
};

/**
 * @brief 语音指令转字符串
 */
std::string VoiceCommandToString(VoiceCommand command);

/**
 * @brief 字符串转语音指令
 */
VoiceCommand StringToVoiceCommand(const std::string& command_str);

/**
 * @brief 创建语音舵机控制器实例
 * @param left_gpio 左臂GPIO
 * @param right_gpio 右臂GPIO
 * @return 控制器实例
 */
std::unique_ptr<VoiceServoController> CreateVoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio);

} // namespace voice_servo_integration
