# Custom Hardware - 阶段1: 基础功能稳定
# 专注于双屏表情显示系统核心功能

# ========================================
# 阶段1: 仅保留最核心的双屏表情系统
# ========================================
set(CORE_EXPRESSION_SRCS
    expression_system/simple_dual_screen.cc
    expression_system/core_test.cc
)

# ========================================
# 编译配置 (极简版本)
# ========================================

# 当前启用的源文件 (仅核心表情系统)
set(ALL_SRCS
    custom_hardware.cc
    ${CORE_EXPRESSION_SRCS}
    # 暂时禁用所有其他模块，专注于双屏表情系统:
    # ${AI_LAYER_SRCS}
    # ${HARDWARE_LAYER_SRCS}
    # ${MOTION_SYSTEM_SRCS}
    # ${SYSTEM_CONTROLLER_SRCS}
)

# 包含目录 (仅核心功能)
set(INCLUDE_DIRS
    .
    expression_system
    # 暂时禁用其他目录:
    # ai_layer
    # hardware_layer
    # motion_system
)

# 组件注册 (最小依赖集合)
idf_component_register(
    SRCS ${ALL_SRCS}
    INCLUDE_DIRS ${INCLUDE_DIRS}
    REQUIRES driver esp_lcd lvgl freertos esp_driver_spi esp_driver_gpio
)