# Custom Hardware - 最小核心功能配置
# 专注于双屏表情显示系统，暂时禁用所有有问题的模块

# ========================================
# 核心表情系统 (仅保留简化双屏)
# ========================================
set(EXPRESSION_SYSTEM_SRCS
    expression_system/simple_dual_screen.cc
    expression_system/core_test.cc
)

# ========================================
# 基础硬件层 (仅保留MCP通信)
# ========================================
set(HARDWARE_LAYER_SRCS
    hardware_layer/mcp_communication.cc
)

# ========================================
# 运动系统 (仅保留基础舵机)
# ========================================
set(MOTION_SYSTEM_SRCS
    motion_system/servo_180.cc
)

# ========================================
# AI层 (基础支持)
# ========================================
set(AI_LAYER_SRCS
    ai_layer/xiaozhi_ai_complete.cc
)

# ========================================
# 暂时完全禁用的模块
# ========================================
# 系统控制器 - 有依赖问题，暂时禁用
# set(SYSTEM_CONTROLLER_SRCS
#     system_controller.cc
# )

# ========================================
# 编译配置 (最小核心功能)
# ========================================

# 当前启用的源文件 (最小集合)
set(ALL_SRCS
    custom_hardware.cc
    ${AI_LAYER_SRCS}
    ${HARDWARE_LAYER_SRCS}
    ${EXPRESSION_SYSTEM_SRCS}
    ${MOTION_SYSTEM_SRCS}
    # 暂时禁用所有有问题的模块:
    # ${SYSTEM_CONTROLLER_SRCS}
)

# 包含目录 (仅包含当前使用的)
set(INCLUDE_DIRS
    .
    ai_layer
    hardware_layer
    expression_system
    motion_system
)

# 组件注册 (最小依赖集合)
idf_component_register(
    SRCS ${ALL_SRCS}
    INCLUDE_DIRS ${INCLUDE_DIRS}
    REQUIRES driver esp_lcd lvgl freertos esp_driver_mcpwm esp_driver_spi esp_driver_i2c
)