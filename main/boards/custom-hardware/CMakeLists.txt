# AI玩具系统 - 多层架构源文件配置

# AI层源文件
set(AI_LAYER_SRCS
    ai_layer/xiaozhi_ai_complete.cc
)

# 核心任务层源文件
set(CORE_LAYER_SRCS
    core_layer/task_manager.cc
)

# 设备抽象层源文件
set(DEVICE_LAYER_SRCS
    device_layer/device_manager.cc
    device_layer/display_device.cc
    device_layer/servo_device.cc
    device_layer/motor_device.cc
    device_layer/sensor_device.cc
)

# 硬件驱动层源文件
set(HARDWARE_LAYER_SRCS
    hardware_layer/mcp_communication.cc
    hardware_layer/servo_driver.cc
    hardware_layer/motor_driver.cc
    hardware_layer/sensor_driver.cc
)

# 表情系统源文件
set(EXPRESSION_SYSTEM_SRCS
    expression_system/expression_manager.cc
    expression_system/eye_renderer.cc
    expression_system/emotion_config.cc
    expression_system/dual_screen_expression.cc
    expression_system/expression_integration.cc
)

# 运动系统源文件（包含180度舵机控制器）
set(MOTION_SYSTEM_SRCS
    motion_system/motion_controller.cc
    motion_system/servo_controller.cc
    motion_system/motor_controller.cc
    motion_system/person_follow_controller.cc
    motion_system/servo_180.cc
    motion_system/voice_servo_integration.cc
)

# 传感器系统源文件
set(SENSOR_SYSTEM_SRCS
    sensor_system/sensor_manager.cc
    sensor_system/ultrasonic_sensor.cc
    sensor_system/infrared_sensor.cc
    sensor_system/millimeter_wave_radar.cc
)

# 系统控制器源文件
set(SYSTEM_CONTROLLER_SRCS
    system_controller.cc
)

# 演示程序源文件
set(DEMO_SRCS
    ai_toy_demo.cc
)



# 所有源文件
set(ALL_SRCS
    custom_hardware.cc
    ${AI_LAYER_SRCS}
    ${CORE_LAYER_SRCS}
    ${DEVICE_LAYER_SRCS}
    ${HARDWARE_LAYER_SRCS}
    ${EXPRESSION_SYSTEM_SRCS}
    ${MOTION_SYSTEM_SRCS}
    ${SENSOR_SYSTEM_SRCS}
    ${SYSTEM_CONTROLLER_SRCS}
    ${DEMO_SRCS}
)

# 包含目录
set(INCLUDE_DIRS
    .
    ai_layer
    core_layer
    device_layer
    hardware_layer
    expression_system
    motion_system
    sensor_system
)

idf_component_register(
    SRCS ${ALL_SRCS}
    INCLUDE_DIRS ${INCLUDE_DIRS}
    REQUIRES driver esp_lcd lvgl freertos
)