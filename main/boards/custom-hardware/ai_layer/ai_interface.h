/**
 * @file ai_interface.h
 * @brief 小智AI交互层接口定义
 * 
 * 负责语音识别、对话生成、情感化回复、角色定制等功能
 * 作为系统的最高层，处理用户交互并生成硬件控制指令
 */

#ifndef AI_INTERFACE_H
#define AI_INTERFACE_H

#include <string>
#include <functional>
#include <vector>
#include <memory>
#include <map>

namespace ai_layer {

/**
 * @brief 语音识别结果
 */
struct VoiceRecognitionResult {
    std::string text;           // 识别的文本
    float confidence;           // 置信度 (0.0-1.0)
    bool is_valid;             // 是否有效识别
    uint32_t timestamp;        // 时间戳
};

/**
 * @brief AI响应结果
 */
struct AIResponse {
    std::string text_response;     // 文本回复
    std::string audio_response;    // 音频回复路径
    std::string emotion;           // 情感状态 (happy, sad, curious, etc.)
    std::vector<std::string> hardware_commands;  // 硬件控制指令
    bool requires_action;          // 是否需要硬件动作
};

/**
 * @brief 用户意图类型
 */
enum class UserIntent {
    GREETING,           // 问候
    QUESTION,           // 提问
    COMMAND,            // 指令
    EMOTION_EXPRESS,    // 情感表达
    FOLLOW_REQUEST,     // 跟随请求
    STOP_REQUEST,       // 停止请求
    UNKNOWN             // 未知意图
};

/**
 * @brief 意图识别结果
 */
struct IntentResult {
    UserIntent intent;
    std::string target;        // 目标对象 (如 "left_arm", "both_arms")
    std::string action;        // 动作类型 (如 "wave", "raise")
    std::map<std::string, std::string> parameters;  // 参数
    float confidence;
};

/**
 * @brief AI交互层回调接口
 */
class AICallback {
public:
    virtual ~AICallback() = default;
    
    // 语音识别完成回调
    virtual void OnVoiceRecognized(const VoiceRecognitionResult& result) = 0;
    
    // AI响应生成完成回调
    virtual void OnAIResponse(const AIResponse& response) = 0;
    
    // 意图识别完成回调
    virtual void OnIntentRecognized(const IntentResult& intent) = 0;
    
    // 错误处理回调
    virtual void OnError(const std::string& error_message) = 0;
};

/**
 * @brief 小智AI接口类
 */
class XiaozhiAI {
public:
    virtual ~XiaozhiAI() = default;
    
    /**
     * @brief 初始化AI系统
     * @param config_path 配置文件路径
     * @return 是否成功
     */
    virtual bool Initialize(const std::string& config_path) = 0;
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口指针
     */
    virtual void SetCallback(std::shared_ptr<AICallback> callback) = 0;
    
    /**
     * @brief 开始语音识别
     * @return 是否成功启动
     */
    virtual bool StartVoiceRecognition() = 0;
    
    /**
     * @brief 停止语音识别
     */
    virtual void StopVoiceRecognition() = 0;
    
    /**
     * @brief 处理文本输入
     * @param text 输入文本
     * @return AI响应
     */
    virtual AIResponse ProcessTextInput(const std::string& text) = 0;
    
    /**
     * @brief 处理语音数据
     * @param audio_data 音频数据
     * @param data_size 数据大小
     * @return 是否成功处理
     */
    virtual bool ProcessAudioData(const uint8_t* audio_data, size_t data_size) = 0;
    
    /**
     * @brief 识别用户意图
     * @param text 输入文本
     * @return 意图识别结果
     */
    virtual IntentResult RecognizeIntent(const std::string& text) = 0;
    
    /**
     * @brief 生成情感化回复
     * @param text 输入文本
     * @param emotion 当前情感状态
     * @return AI响应
     */
    virtual AIResponse GenerateEmotionalResponse(const std::string& text, const std::string& emotion) = 0;
    
    /**
     * @brief 设置角色特性
     * @param personality 性格特征
     * @param voice_style 语音风格
     */
    virtual void SetPersonality(const std::string& personality, const std::string& voice_style) = 0;
    
    /**
     * @brief 获取当前情感状态
     * @return 情感状态字符串
     */
    virtual std::string GetCurrentEmotion() const = 0;
    
    /**
     * @brief 更新上下文信息
     * @param context 上下文信息
     */
    virtual void UpdateContext(const std::string& context) = 0;
    
    /**
     * @brief 检查系统状态
     * @return 是否正常运行
     */
    virtual bool IsHealthy() const = 0;
    
    /**
     * @brief 获取系统统计信息
     * @return 统计信息字符串
     */
    virtual std::string GetStatistics() const = 0;
};

/**
 * @brief 创建小智AI实例
 * @return AI实例指针
 */
std::unique_ptr<XiaozhiAI> CreateXiaozhiAI();

} // namespace ai_layer

#endif // AI_INTERFACE_H
