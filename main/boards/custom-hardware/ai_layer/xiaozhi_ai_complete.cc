/**
 * @file xiaozhi_ai_complete.cc
 * @brief 小智AI完整实现
 */

#include "ai_interface.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <cstring>
#include <algorithm>
#include <map>
#include <vector>
#include <sstream>

static const char* TAG = "XiaozhiAI";

namespace ai_layer {

// 语音识别状态
enum class VoiceState {
    IDLE,
    LISTENING,
    PROCESSING,
    RESPONDING
};

// 音频数据结构
struct AudioData {
    uint8_t* data;
    size_t size;
    uint32_t timestamp;
};

// 情感状态映射
static const std::map<std::string, std::string> emotion_responses = {
    {"happy", "我很开心！让我们一起玩吧！"},
    {"sad", "我有点难过...需要安慰吗？"},
    {"angry", "我生气了！但我还是爱你的！"},
    {"surprised", "哇，真的吗？太神奇了！"},
    {"confused", "我有点困惑...能再说一遍吗？"},
    {"excited", "太棒了！我超级兴奋！"},
    {"love", "我喜欢这个！你真棒！"},
    {"thinking", "让我想想...嗯..."},
    {"worried", "我有点担心...没关系的！"},
    {"neutral", "我在这里，随时为你服务！"}
};

// 意图识别关键词
static const std::map<std::vector<std::string>, UserIntent> intent_keywords = {
    {{"你好", "hi", "hello", "嗨", "早上好", "下午好", "晚上好"}, UserIntent::GREETING},
    {{"再见", "bye", "goodbye", "拜拜", "回见"}, UserIntent::FAREWELL},
    {{"挥手", "wave", "挥挥手", "招手"}, UserIntent::WAVE_HAND},
    {{"举手", "raise", "举起手", "抬手"}, UserIntent::RAISE_HAND},
    {{"跳舞", "dance", "舞蹈", "跳个舞"}, UserIntent::DANCE},
    {{"跟我来", "follow", "跟随", "跟着我"}, UserIntent::FOLLOW_ME},
    {{"停止", "stop", "停下", "别动"}, UserIntent::STOP},
    {{"开心", "happy", "高兴", "快乐"}, UserIntent::EXPRESS_EMOTION},
    {{"伤心", "sad", "难过", "悲伤"}, UserIntent::EXPRESS_EMOTION},
    {{"生气", "angry", "愤怒", "气愤"}, UserIntent::EXPRESS_EMOTION},
    {{"唱歌", "sing", "歌曲", "来首歌"}, UserIntent::SING},
    {{"讲故事", "story", "故事", "说个故事"}, UserIntent::TELL_STORY},
    {{"游戏", "game", "玩游戏", "玩一玩"}, UserIntent::PLAY_GAME}
};

// 预定义回复模板
static const std::map<UserIntent, std::vector<std::string>> response_templates = {
    {UserIntent::GREETING, {
        "你好！我是小智，很高兴见到你！",
        "嗨！今天过得怎么样？",
        "你好呀！我们来玩点什么吧！"
    }},
    {UserIntent::FAREWELL, {
        "再见！记得想我哦！",
        "拜拜！下次再来找我玩！",
        "再见！期待下次见面！"
    }},
    {UserIntent::WAVE_HAND, {
        "我来挥挥手！",
        "一起挥手吧！",
        "挥手真有趣！"
    }},
    {UserIntent::DANCE, {
        "我来跳个舞给你看！",
        "一起跳舞吧！",
        "舞蹈时间到！"
    }},
    {UserIntent::SING, {
        "我来唱首歌！啦啦啦~",
        "音乐时间！",
        "让我们一起唱歌！"
    }}
};

class XiaozhiAIImpl : public XiaozhiAI {
private:
    std::shared_ptr<AICallback> callback_;
    VoiceState voice_state_;
    std::string current_emotion_;
    std::string personality_;
    std::string voice_style_;
    bool initialized_;
    
    // 语音处理队列
    QueueHandle_t audio_queue_;
    TaskHandle_t voice_task_;
    
    // 上下文管理
    std::vector<std::string> conversation_history_;
    std::string current_context_;
    
    // 统计信息
    uint32_t recognition_count_;
    uint32_t successful_recognitions_;
    
public:
    XiaozhiAIImpl() : voice_state_(VoiceState::IDLE), current_emotion_("neutral"),
                     personality_("friendly"), voice_style_("cute"), initialized_(false),
                     audio_queue_(nullptr), voice_task_(nullptr),
                     recognition_count_(0), successful_recognitions_(0) {}
    
    ~XiaozhiAIImpl() {
        if (voice_task_) {
            vTaskDelete(voice_task_);
        }
        if (audio_queue_) {
            vQueueDelete(audio_queue_);
        }
    }
    
    bool Initialize(const std::string& config_path) override {
        if (initialized_) {
            ESP_LOGW(TAG, "XiaozhiAI已初始化");
            return true;
        }
        
        ESP_LOGI(TAG, "初始化XiaozhiAI系统...");
        
        // 创建音频处理队列
        audio_queue_ = xQueueCreate(10, sizeof(AudioData));
        if (!audio_queue_) {
            ESP_LOGE(TAG, "创建音频队列失败");
            return false;
        }
        
        // 创建语音处理任务
        BaseType_t ret = xTaskCreate(VoiceProcessingTask, "voice_task", 4096, this, 5, &voice_task_);
        if (ret != pdPASS) {
            ESP_LOGE(TAG, "创建语音处理任务失败");
            return false;
        }
        
        // 初始化默认设置
        current_emotion_ = "neutral";
        personality_ = "friendly";
        voice_style_ = "cute";
        voice_state_ = VoiceState::IDLE;
        
        initialized_ = true;
        ESP_LOGI(TAG, "XiaozhiAI初始化成功");
        
        return true;
    }
    
    void SetCallback(std::shared_ptr<AICallback> callback) override {
        callback_ = callback;
        ESP_LOGI(TAG, "AI回调已设置");
    }
    
    bool StartVoiceRecognition() override {
        if (!initialized_) {
            ESP_LOGE(TAG, "AI系统未初始化");
            return false;
        }
        
        if (voice_state_ != VoiceState::IDLE) {
            ESP_LOGW(TAG, "语音识别已在运行");
            return true;
        }
        
        voice_state_ = VoiceState::LISTENING;
        ESP_LOGI(TAG, "开始语音识别");
        
        if (callback_) {
            callback_->OnVoiceRecognitionStarted();
        }
        
        return true;
    }
    
    void StopVoiceRecognition() override {
        if (voice_state_ == VoiceState::IDLE) {
            return;
        }
        
        voice_state_ = VoiceState::IDLE;
        ESP_LOGI(TAG, "停止语音识别");
        
        if (callback_) {
            callback_->OnVoiceRecognitionStopped();
        }
    }
    
    AIResponse ProcessTextInput(const std::string& text) override {
        ESP_LOGI(TAG, "处理文本输入: %s", text.c_str());
        
        recognition_count_++;
        
        // 识别意图
        IntentResult intent = RecognizeIntent(text);
        
        // 生成回复
        AIResponse response = GenerateResponse(text, intent);
        
        // 更新上下文
        UpdateContext(text);
        
        // 记录对话历史
        conversation_history_.push_back("用户: " + text);
        conversation_history_.push_back("小智: " + response.text_response);
        
        // 限制历史记录长度
        if (conversation_history_.size() > 20) {
            conversation_history_.erase(conversation_history_.begin(), conversation_history_.begin() + 2);
        }
        
        if (intent.confidence > 0.7f) {
            successful_recognitions_++;
        }
        
        ESP_LOGI(TAG, "生成回复: %s", response.text_response.c_str());
        
        return response;
    }
    
    bool ProcessAudioData(const uint8_t* audio_data, size_t data_size) override {
        if (!initialized_ || voice_state_ != VoiceState::LISTENING) {
            return false;
        }
        
        // 简化的音频处理 - 在实际实现中需要集成语音识别引擎
        AudioData audio;
        audio.data = (uint8_t*)malloc(data_size);
        if (!audio.data) {
            ESP_LOGE(TAG, "音频数据内存分配失败");
            return false;
        }
        
        memcpy(audio.data, audio_data, data_size);
        audio.size = data_size;
        audio.timestamp = esp_timer_get_time() / 1000;
        
        if (xQueueSend(audio_queue_, &audio, 0) != pdTRUE) {
            free(audio.data);
            ESP_LOGW(TAG, "音频队列已满");
            return false;
        }
        
        return true;
    }
    
    IntentResult RecognizeIntent(const std::string& text) override {
        IntentResult result;
        result.intent = UserIntent::UNKNOWN;
        result.confidence = 0.0f;
        
        // 转换为小写以便匹配
        std::string lower_text = text;
        std::transform(lower_text.begin(), lower_text.end(), lower_text.begin(), ::tolower);
        
        // 遍历意图关键词
        for (const auto& pair : intent_keywords) {
            const auto& keywords = pair.first;
            UserIntent intent = pair.second;
            
            for (const std::string& keyword : keywords) {
                if (lower_text.find(keyword) != std::string::npos) {
                    result.intent = intent;
                    result.confidence = CalculateConfidence(lower_text, keyword);
                    
                    // 如果找到高置信度匹配，直接返回
                    if (result.confidence > 0.8f) {
                        return result;
                    }
                }
            }
        }
        
        // 如果没有找到明确意图，尝试情感分析
        if (result.intent == UserIntent::UNKNOWN) {
            result = AnalyzeEmotion(text);
        }
        
        ESP_LOGI(TAG, "意图识别: %s, 置信度: %.2f", 
                 UserIntentToString(result.intent).c_str(), result.confidence);
        
        return result;
    }
    
    AIResponse GenerateEmotionalResponse(const std::string& text, const std::string& emotion) override {
        AIResponse response;
        
        // 设置情感状态
        current_emotion_ = emotion;
        
        // 根据情感生成回复
        auto it = emotion_responses.find(emotion);
        if (it != emotion_responses.end()) {
            response.text_response = it->second;
        } else {
            response.text_response = "我理解你的感受。";
        }
        
        response.emotion = emotion;
        response.requires_action = true;
        
        // 根据情感添加硬件指令
        if (emotion == "happy") {
            response.hardware_commands.push_back("expression:happy");
            response.hardware_commands.push_back("servo:wave");
        } else if (emotion == "sad") {
            response.hardware_commands.push_back("expression:sad");
        } else if (emotion == "excited") {
            response.hardware_commands.push_back("expression:excited");
            response.hardware_commands.push_back("servo:dance");
        }
        
        return response;
    }
    
    void SetPersonality(const std::string& personality, const std::string& voice_style) override {
        personality_ = personality;
        voice_style_ = voice_style;
        ESP_LOGI(TAG, "设置个性: %s, 语音风格: %s", personality.c_str(), voice_style.c_str());
    }
    
    std::string GetCurrentEmotion() const override {
        return current_emotion_;
    }
    
    void UpdateContext(const std::string& context) override {
        current_context_ = context;
        ESP_LOGD(TAG, "更新上下文: %s", context.c_str());
    }

private:
    // 语音处理任务
    static void VoiceProcessingTask(void* parameter) {
        XiaozhiAIImpl* ai = static_cast<XiaozhiAIImpl*>(parameter);
        AudioData audio;
        
        while (true) {
            if (xQueueReceive(ai->audio_queue_, &audio, portMAX_DELAY) == pdTRUE) {
                ai->ProcessAudioInternal(audio);
                free(audio.data);
            }
        }
    }
    
    void ProcessAudioInternal(const AudioData& audio) {
        voice_state_ = VoiceState::PROCESSING;
        
        // 简化的语音识别 - 在实际实现中需要集成ASR引擎
        std::string recognized_text = SimulateVoiceRecognition(audio);
        
        if (!recognized_text.empty()) {
            VoiceRecognitionResult result;
            result.text = recognized_text;
            result.confidence = 0.85f;
            result.is_valid = true;
            result.timestamp = audio.timestamp;
            
            if (callback_) {
                callback_->OnVoiceRecognized(result);
            }
            
            // 处理识别结果
            AIResponse response = ProcessTextInput(recognized_text);
            
            if (callback_) {
                callback_->OnAIResponse(response);
            }
        }
        
        voice_state_ = VoiceState::LISTENING;
    }
    
    std::string SimulateVoiceRecognition(const AudioData& audio) {
        // 简化的语音识别模拟
        // 在实际实现中，这里应该调用真正的ASR引擎
        
        static int counter = 0;
        counter++;
        
        std::vector<std::string> sample_texts = {
            "你好",
            "挥手",
            "跳舞",
            "我很开心",
            "再见"
        };
        
        if (audio.size > 1000) {  // 假设有足够的音频数据
            return sample_texts[counter % sample_texts.size()];
        }
        
        return "";
    }
    
    float CalculateConfidence(const std::string& text, const std::string& keyword) {
        // 简化的置信度计算
        float base_confidence = 0.7f;
        
        // 完全匹配
        if (text == keyword) {
            return 1.0f;
        }
        
        // 包含关键词
        if (text.find(keyword) != std::string::npos) {
            // 根据文本长度调整置信度
            float length_factor = static_cast<float>(keyword.length()) / text.length();
            return base_confidence + (length_factor * 0.2f);
        }
        
        return 0.0f;
    }
    
    IntentResult AnalyzeEmotion(const std::string& text) {
        IntentResult result;
        result.intent = UserIntent::EXPRESS_EMOTION;
        result.confidence = 0.5f;
        
        // 简化的情感分析
        if (text.find("开心") != std::string::npos || text.find("高兴") != std::string::npos) {
            current_emotion_ = "happy";
            result.confidence = 0.8f;
        } else if (text.find("难过") != std::string::npos || text.find("伤心") != std::string::npos) {
            current_emotion_ = "sad";
            result.confidence = 0.8f;
        } else {
            result.intent = UserIntent::UNKNOWN;
            result.confidence = 0.0f;
        }
        
        return result;
    }
    
    AIResponse GenerateResponse(const std::string& text, const IntentResult& intent) {
        AIResponse response;
        response.emotion = current_emotion_;
        response.requires_action = false;
        
        // 根据意图生成回复
        auto it = response_templates.find(intent.intent);
        if (it != response_templates.end()) {
            const auto& templates = it->second;
            int index = esp_timer_get_time() % templates.size();
            response.text_response = templates[index];
            response.requires_action = true;
            
            // 添加对应的硬件指令
            switch (intent.intent) {
                case UserIntent::WAVE_HAND:
                    response.hardware_commands.push_back("servo:wave");
                    break;
                case UserIntent::DANCE:
                    response.hardware_commands.push_back("servo:dance");
                    response.hardware_commands.push_back("expression:excited");
                    break;
                case UserIntent::GREETING:
                    response.hardware_commands.push_back("expression:happy");
                    response.hardware_commands.push_back("servo:wave");
                    break;
                case UserIntent::FAREWELL:
                    response.hardware_commands.push_back("expression:sad");
                    response.hardware_commands.push_back("servo:wave");
                    break;
                default:
                    break;
            }
        } else {
            // 默认回复
            response.text_response = "我听到了，但不太明白你的意思。能再说一遍吗？";
        }
        
        return response;
    }
    
    std::string UserIntentToString(UserIntent intent) {
        switch (intent) {
            case UserIntent::GREETING: return "GREETING";
            case UserIntent::FAREWELL: return "FAREWELL";
            case UserIntent::WAVE_HAND: return "WAVE_HAND";
            case UserIntent::RAISE_HAND: return "RAISE_HAND";
            case UserIntent::DANCE: return "DANCE";
            case UserIntent::FOLLOW_ME: return "FOLLOW_ME";
            case UserIntent::STOP: return "STOP";
            case UserIntent::EXPRESS_EMOTION: return "EXPRESS_EMOTION";
            case UserIntent::SING: return "SING";
            case UserIntent::TELL_STORY: return "TELL_STORY";
            case UserIntent::PLAY_GAME: return "PLAY_GAME";
            case UserIntent::UNKNOWN: return "UNKNOWN";
            default: return "UNKNOWN";
        }
    }
};

// 工厂函数
std::unique_ptr<XiaozhiAI> CreateXiaozhiAI() {
    return std::make_unique<XiaozhiAIImpl>();
}

} // namespace ai_layer
