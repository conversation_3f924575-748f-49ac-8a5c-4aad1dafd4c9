# 🔌 小智AI玩具系统 - 硬件连接与针脚配置文档

## 📋 系统概述

基于ESP32-S3 R16N8开发板的智能AI玩具系统，采用MCP23017 I2C GPIO扩展芯片实现丰富的硬件接口。

### 🎯 主控制器规格
- **MCU**: ESP32-S3 R16N8
- **内存**: 16MB Flash + 8MB PSRAM  
- **频率**: 240MHz双核处理器
- **GPIO**: 45个可用GPIO引脚
- **扩展**: MCP23017提供额外16个GPIO

---

## 🖥️ 双屏表情显示系统

### 双GC9A01显示屏 (240x240圆形屏 x2)
```
功能          | GPIO引脚  | 说明
-------------|----------|------------------
MOSI (数据)   | GPIO_20  | SPI数据输出 (共享)
CLK (时钟)    | GPIO_19  | SPI时钟信号 (共享)
DC (数据/命令) | GPIO_21  | 数据/命令选择 (共享)
RST (复位)    | GPIO_1   | 显示屏复位 (共享)
CS_LEFT (左屏) | GPIO_2   | 左屏片选信号 (独立)
CS_RIGHT (右屏)| GPIO_45  | 右屏片选信号 (独立)
BL (背光)     | GPIO_42  | PWM背光控制 (共享)
```

### 双屏配置参数
- **分辨率**: 240x240像素 (每屏)
- **驱动芯片**: GC9A01 x2
- **接口**: 共享SPI总线 (SPI2_HOST)
- **时钟频率**: 40MHz
- **颜色**: 16位RGB565
- **背光**: PWM控制，支持0-100%亮度调节
- **表情功能**: 支持眼球追踪、眨眼、情感表达

---

## 🎤 音频系统

### I2S音频接口 (Simplex模式)
```
功能          | GPIO引脚  | 说明
-------------|----------|------------------
MIC_WS       | GPIO_4   | 麦克风字选择
MIC_SCK      | GPIO_5   | 麦克风串行时钟
MIC_DIN      | GPIO_6   | 麦克风数据输入
SPK_DOUT     | GPIO_7   | 扬声器数据输出
SPK_BCLK     | GPIO_15  | 扬声器位时钟
SPK_LRCK     | GPIO_16  | 扬声器左右声道时钟
```

### 音频配置参数
- **输入采样率**: 16kHz
- **输出采样率**: 24kHz
- **模式**: Simplex (独立输入输出)
- **位深度**: 16位
- **声道**: 单声道/立体声可配置

---

## 🦾 运动系统

### 180度舵机控制
```
功能          | GPIO引脚  | 说明
-------------|----------|------------------
左臂舵机      | GPIO_9   | PWM控制信号
右臂舵机      | GPIO_10  | PWM控制信号
```

### 舵机配置参数
- **类型**: 180度标准舵机
- **控制**: MCPWM PWM信号
- **频率**: 50Hz
- **脉宽范围**: 500-2500μs
- **精度**: ±2度
- **响应时间**: 0.1-0.2秒

### 支持动作
- 挥手 (Wave)
- 举手 (Raise)
- 敬礼 (Salute)
- 跳舞 (Dance)
- 停止 (Stop)
- 重置 (Reset)

---

## 🔧 系统控制

### 基础控制接口
```
功能          | GPIO引脚  | 说明
-------------|----------|------------------
内置LED      | GPIO_48  | 系统状态指示
启动按键      | GPIO_0   | Boot/用户按键
测试灯       | GPIO_18  | MCP测试用LED
```

---

## 🔌 I2C扩展系统

### MCP23017 GPIO扩展芯片
```
功能          | ESP32引脚 | 说明
-------------|----------|------------------
SDA (数据)    | GPIO_8   | I2C数据线 (建议配置)
SCL (时钟)    | GPIO_3   | I2C时钟线 (建议配置)
I2C地址      | 0x20     | 默认设备地址
I2C端口      | I2C_NUM_0| I2C总线编号
```

### MCP23017引脚分配 (16个扩展GPIO)
```
MCP引脚 | 功能分配        | 说明
-------|---------------|------------------
GPA0   | 传感器1触发    | 超声波传感器
GPA1   | 传感器1回声    | 超声波传感器
GPA2   | 传感器2触发    | 红外传感器
GPA3   | 传感器2回声    | 红外传感器
GPA4   | 雷达TX        | 毫米波雷达发送
GPA5   | 雷达RX        | 毫米波雷达接收
GPA6   | 电机1方向     | 左轮电机方向
GPA7   | 电机1使能     | 左轮电机使能
GPB0   | 电机2方向     | 右轮电机方向
GPB1   | 电机2使能     | 右轮电机使能
GPB2   | 状态LED1      | 系统状态指示
GPB3   | 状态LED2      | 网络状态指示
GPB4   | 扩展按键1     | 功能按键
GPB5   | 扩展按键2     | 模式切换
GPB6   | 备用GPIO1     | 预留扩展
GPB7   | 备用GPIO2     | 预留扩展
```

---

## 📡 传感器系统

### 超声波传感器 (避障)
```
传感器位置    | 触发引脚    | 回声引脚    | 检测范围
------------|-----------|-----------|----------
前方中央     | MCP_GPA0  | MCP_GPA1  | 2cm-4m
左侧        | MCP_GPA2  | MCP_GPA3  | 2cm-4m
右侧        | 待配置     | 待配置     | 2cm-4m
后方        | 待配置     | 待配置     | 2cm-4m
```

### 红外传感器 (悬崖检测)
```
传感器位置    | 信号引脚    | 检测类型    | 检测范围
------------|-----------|-----------|----------
底部前左     | MCP_GPA4  | 数字信号   | 2-10cm
底部前右     | MCP_GPA5  | 数字信号   | 2-10cm
底部后左     | 待配置     | 数字信号   | 2-10cm
底部后右     | 待配置     | 数字信号   | 2-10cm
```

### 毫米波雷达 (人物检测)
```
功能          | 引脚       | 说明
-------------|-----------|------------------
雷达TX       | MCP_GPA6  | 串口发送
雷达RX       | MCP_GPA7  | 串口接收
检测范围      | -         | 0.5-8米
检测角度      | -         | 60度扇形
```

---

## 🚗 移动系统

### 差分驱动电机
```
电机位置      | 方向控制    | 速度控制    | 使能控制
------------|-----------|-----------|----------
左轮电机     | MCP_GPB0  | PWM_CH1   | MCP_GPB1
右轮电机     | MCP_GPB2  | PWM_CH2   | MCP_GPB3
```

### 电机配置参数
- **类型**: 直流减速电机
- **电压**: 3.3V-5V
- **控制**: PWM调速 + 方向控制
- **编码器**: 可选霍尔编码器反馈

---

## 💡 状态指示系统

### LED指示灯
```
LED功能      | 控制引脚    | 状态说明
------------|-----------|------------------
系统状态     | GPIO_48   | 绿色=正常，红色=错误
网络状态     | MCP_GPB2  | 蓝色=连接，闪烁=断开
语音状态     | MCP_GPB3  | 白色=监听，黄色=识别
电源状态     | 硬件LED   | 红色=充电，绿色=满电
```

---

## 🔘 用户交互

### 按键系统
```
按键功能      | 引脚       | 触发方式    | 功能说明
------------|-----------|-----------|----------
启动按键     | GPIO_0    | 短按/长按  | 系统启动/重置
功能按键1    | MCP_GPB4  | 短按      | 模式切换
功能按键2    | MCP_GPB5  | 短按      | 音量调节
紧急停止     | 待配置     | 长按      | 紧急停止所有动作
```

---

## ⚡ 电源管理

### 电源分配
```
电源轨       | 电压      | 负载设备
------------|----------|------------------
主电源       | 5V       | ESP32-S3主板
显示电源     | 3.3V     | LCD屏幕
舵机电源     | 5V       | 180度舵机
电机电源     | 5V       | 驱动电机
传感器电源   | 3.3V     | 各类传感器
```

### 电源监控
- **电池电压监控**: ADC检测
- **充电状态**: 硬件指示
- **低电压保护**: 软件实现
- **过流保护**: 硬件熔断器

---

## 🔧 调试接口

### 串口调试
```
接口类型      | 引脚       | 波特率     | 用途
------------|-----------|-----------|----------
主串口       | USB-C     | 115200    | 程序下载/调试
辅助串口     | GPIO_TX/RX| 115200    | 传感器通信
```

### 编程接口
- **下载方式**: USB-C直连
- **调试方式**: JTAG/SWD
- **固件更新**: OTA无线更新

---

## 📊 完整引脚分配表

### ESP32-S3 引脚使用汇总 (双屏表情模式)
```
GPIO | 功能分配        | 方向 | 说明
-----|---------------|------|------------------
0    | 启动按键       | IN   | Boot/用户按键
1    | LCD_RST       | OUT  | 双屏复位信号 (共享)
2    | LCD_CS_LEFT   | OUT  | 左屏片选信号
3    | I2C_SCL       | OUT  | MCP23017时钟线 (建议)
4    | MIC_WS        | OUT  | 麦克风字选择
5    | MIC_SCK       | OUT  | 麦克风串行时钟
6    | MIC_DIN       | IN   | 麦克风数据输入
7    | SPK_DOUT      | OUT  | 扬声器数据输出
8    | I2C_SDA       | I/O  | MCP23017数据线 (建议)
9    | 左臂舵机       | OUT  | PWM控制信号
10   | 右臂舵机       | OUT  | PWM控制信号
15   | SPK_BCLK      | OUT  | 扬声器位时钟
16   | SPK_LRCK      | OUT  | 扬声器左右声道时钟
18   | 测试LED       | OUT  | MCP测试用LED
19   | LCD_CLK       | OUT  | 双屏SPI时钟 (共享)
20   | LCD_MOSI      | OUT  | 双屏SPI数据 (共享)
21   | LCD_DC        | OUT  | 双屏数据/命令 (共享)
42   | LCD_BL        | OUT  | PWM背光控制
45   | LCD_CS_RIGHT  | OUT  | 右屏片选信号
48   | 内置LED       | OUT  | 系统状态指示
```

### 剩余可用GPIO (双屏模式)
```
GPIO | 状态    | 建议用途
-----|--------|------------------
11   | 可用   | ADC/通用GPIO
12   | 可用   | ADC/通用GPIO
13   | 可用   | ADC/通用GPIO
14   | 可用   | ADC/通用GPIO
17   | 可用   | 通用GPIO
35   | 可用   | 通用GPIO
36   | 可用   | 通用GPIO
37   | 可用   | 通用GPIO
38   | 可用   | 通用GPIO
39   | 可用   | 通用GPIO
40   | 可用   | 通用GPIO (原LCD_DC已改为GPIO21)
41   | 可用   | 通用GPIO (原LCD_CS已改为GPIO2)
43   | 可用   | 通用GPIO
44   | 可用   | 通用GPIO
46   | 可用   | 通用GPIO
47   | 可用   | 通用GPIO (原LCD_MOSI已改为GPIO20)
```

---

## 📊 系统资源分配

### GPIO使用统计
```
总GPIO数量: 45个 (ESP32-S3)
当前已使用: 19个直连GPIO
MCP23017扩展: 16个GPIO (待配置)
剩余可用: 26个ESP32-S3 GPIO
扩展能力: 可添加更多MCP23017芯片 (每个+16GPIO)
```

### 当前实际配置状态
```
✅ 已配置并测试:
- 显示系统 (6个GPIO): 完全配置
- 音频系统 (6个GPIO): 完全配置
- 舵机系统 (2个GPIO): 完全配置
- 控制系统 (3个GPIO): 完全配置
- 测试LED (1个GPIO): 完全配置

🔄 待配置:
- I2C总线 (2个GPIO): 需要配置SDA/SCL
- MCP23017扩展 (16个GPIO): 需要连接和配置
- 传感器系统: 依赖MCP23017
- 电机系统: 依赖MCP23017
```

### 内存使用
- **程序存储**: 16MB Flash
- **运行内存**: 8MB PSRAM
- **系统预留**: ~2MB
- **用户可用**: ~14MB Flash + 6MB PSRAM

---

## 🛠️ 硬件调试指南

### 常用测试点
1. **电源测试**: 各电源轨电压
2. **信号测试**: SPI/I2C/UART通信
3. **GPIO测试**: 数字信号电平
4. **PWM测试**: 舵机控制信号

### 故障排除
1. **显示不亮**: 检查SPI连接和背光电源
2. **舵机不动**: 检查PWM信号和电源
3. **传感器无响应**: 检查I2C/GPIO连接
4. **音频异常**: 检查I2S时钟和数据线

---

---

## 🔗 连接示意图

### 主要连接关系
```
ESP32-S3 R16N8
├── SPI总线 (显示)
│   ├── GPIO_47 → LCD_MOSI
│   ├── GPIO_21 → LCD_CLK
│   ├── GPIO_41 → LCD_CS
│   ├── GPIO_40 → LCD_DC
│   ├── GPIO_45 → LCD_RST
│   └── GPIO_42 → LCD_BL (PWM)
│
├── I2S总线 (音频)
│   ├── GPIO_4  → MIC_WS
│   ├── GPIO_5  → MIC_SCK
│   ├── GPIO_6  → MIC_DIN
│   ├── GPIO_7  → SPK_DOUT
│   ├── GPIO_15 → SPK_BCLK
│   └── GPIO_16 → SPK_LRCK
│
├── PWM输出 (舵机)
│   ├── GPIO_9  → 左臂舵机
│   └── GPIO_10 → 右臂舵机
│
├── I2C总线 (扩展)
│   ├── SDA → MCP23017_SDA
│   ├── SCL → MCP23017_SCL
│   └── 0x20 → MCP23017地址
│
└── 控制接口
    ├── GPIO_0  → 启动按键
    ├── GPIO_18 → 测试LED
    └── GPIO_48 → 状态LED
```

### MCP23017扩展连接
```
MCP23017 (0x20)
├── Port A (GPA0-GPA7)
│   ├── GPA0 → 超声波1_TRIG
│   ├── GPA1 → 超声波1_ECHO
│   ├── GPA2 → 红外1_SIG
│   ├── GPA3 → 红外2_SIG
│   ├── GPA4 → 雷达_TX
│   ├── GPA5 → 雷达_RX
│   ├── GPA6 → 电机1_DIR
│   └── GPA7 → 电机1_EN
│
└── Port B (GPB0-GPB7)
    ├── GPB0 → 电机2_DIR
    ├── GPB1 → 电机2_EN
    ├── GPB2 → 状态LED1
    ├── GPB3 → 状态LED2
    ├── GPB4 → 功能按键1
    ├── GPB5 → 功能按键2
    ├── GPB6 → 备用GPIO1
    └── GPB7 → 备用GPIO2
```

---

## 📐 PCB布局建议

### 布线优先级
1. **电源线**: 最粗，最短路径
2. **时钟信号**: 等长，避免串扰
3. **数据信号**: 差分对，阻抗匹配
4. **模拟信号**: 远离数字开关

### 接地策略
- **数字地**: DGND，连接数字电路
- **模拟地**: AGND，连接音频电路
- **电源地**: PGND，连接电机电源
- **统一接地**: 单点接地，避免地环路

### EMI/EMC考虑
- **去耦电容**: 每个IC附近放置
- **滤波电感**: 电源输入端
- **屏蔽**: 敏感信号屏蔽处理
- **布局**: 高频信号远离敏感电路

---

## 🔧 软件配置映射

### GPIO配置代码 (双屏表情模式)
```cpp
// 双屏表情显示系统GPIO
#define DISPLAY_MOSI_PIN      GPIO_NUM_20  // 共享MOSI
#define DISPLAY_CLK_PIN       GPIO_NUM_19  // 共享SCK
#define DISPLAY_DC_PIN        GPIO_NUM_21  // 共享DC
#define DISPLAY_RST_PIN       GPIO_NUM_1   // 共享RST
#define DISPLAY_CS_LEFT_PIN   GPIO_NUM_2   // 左屏CS
#define DISPLAY_CS_RIGHT_PIN  GPIO_NUM_45  // 右屏CS
#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_42  // 共享背光

// 音频系统GPIO
#define AUDIO_I2S_MIC_GPIO_WS   GPIO_NUM_4
#define AUDIO_I2S_MIC_GPIO_SCK  GPIO_NUM_5
#define AUDIO_I2S_MIC_GPIO_DIN  GPIO_NUM_6
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_7
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_15
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_16

// 舵机控制GPIO
#define SERVO_LEFT_ARM_PIN    GPIO_NUM_9
#define SERVO_RIGHT_ARM_PIN   GPIO_NUM_10

// 系统控制GPIO
#define BUILTIN_LED_GPIO      GPIO_NUM_48
#define BOOT_BUTTON_GPIO      GPIO_NUM_0
#define LAMP_GPIO             GPIO_NUM_18
```

### MCP23017配置
```cpp
// MCP23017 I2C配置
#define MCP23017_I2C_ADDR     0x20
#define MCP23017_I2C_PORT     I2C_NUM_0
#define MCP23017_SDA_PIN      GPIO_NUM_8   // 建议配置
#define MCP23017_SCL_PIN      GPIO_NUM_3   // 建议配置
#define MCP23017_FREQ_HZ      400000       // 400kHz

// MCP23017引脚映射
#define MCP_ULTRASONIC1_TRIG  0  // GPA0
#define MCP_ULTRASONIC1_ECHO  1  // GPA1
#define MCP_INFRARED1_SIG     2  // GPA2
#define MCP_INFRARED2_SIG     3  // GPA3
#define MCP_RADAR_TX          4  // GPA4
#define MCP_RADAR_RX          5  // GPA5
#define MCP_MOTOR1_DIR        6  // GPA6
#define MCP_MOTOR1_EN         7  // GPA7
#define MCP_MOTOR2_DIR        8  // GPB0
#define MCP_MOTOR2_EN         9  // GPB1
#define MCP_STATUS_LED1       10 // GPB2
#define MCP_STATUS_LED2       11 // GPB3
#define MCP_BUTTON1           12 // GPB4
#define MCP_BUTTON2           13 // GPB5
#define MCP_SPARE_GPIO1       14 // GPB6
#define MCP_SPARE_GPIO2       15 // GPB7
```

---

## 📋 硬件清单

### 主要器件
```
器件类型          | 型号/规格        | 数量 | 说明
-----------------|----------------|------|------------------
主控制器          | ESP32-S3 R16N8 | 1    | 16MB+8MB
GPIO扩展         | MCP23017       | 1    | I2C接口
显示屏           | GC9A01 240x240 | 2    | 双圆形LCD (表情眼睛)
舵机             | SG90/MG90S     | 2    | 180度舵机
电机             | N20减速电机     | 2    | 3V-6V直流电机
超声波传感器      | HC-SR04        | 4    | 2-400cm检测
红外传感器        | TCRT5000       | 4    | 悬崖检测
毫米波雷达        | HLK-LD2410     | 1    | 人体检测
麦克风           | INMP441        | 1    | I2S数字麦克风
扬声器           | 8Ω 1W          | 1    | 音频输出
```

### 辅助器件
```
器件类型          | 规格           | 数量 | 说明
-----------------|---------------|------|------------------
电阻             | 10kΩ          | 10   | 上拉电阻
电容             | 100nF         | 10   | 去耦电容
电容             | 10μF          | 5    | 滤波电容
LED              | 3mm 各色      | 5    | 状态指示
按键             | 6x6mm轻触     | 3    | 用户交互
连接器           | 2.54mm排针    | 若干  | 接口连接
PCB              | 4层板         | 1    | 主控制板
```

---

## 🔍 测试验证

### 功能测试清单
- [ ] 电源系统：各路电压正常
- [ ] 显示系统：屏幕点亮，内容显示
- [ ] 音频系统：录音播放正常
- [ ] 舵机系统：动作执行准确
- [ ] 传感器系统：检测数据正确
- [ ] 通信系统：I2C/SPI/UART正常
- [ ] 扩展系统：MCP23017功能正常
- [ ] 软件系统：固件运行稳定

### 性能测试
- [ ] 响应时间：语音识别<500ms
- [ ] 动作精度：舵机误差<2度
- [ ] 检测精度：传感器误差<5%
- [ ] 续航时间：连续工作>2小时
- [ ] 稳定性：24小时无故障运行

---

## ⚠️ 重要注意事项

### 当前硬件状态
1. **✅ 基础功能已实现**: 显示、音频、舵机、基础控制
2. **🔄 扩展功能待配置**: I2C总线、MCP23017、传感器、电机
3. **📋 引脚冲突检查**: 确保新增引脚不与现有功能冲突

### 下一步开发建议
1. **配置I2C总线**:
   - 建议使用GPIO_8 (SDA) 和GPIO_3 (SCL)
   - 添加上拉电阻 (4.7kΩ)
   - 测试MCP23017通信

2. **连接MCP23017**:
   - 验证I2C地址 (0x20)
   - 测试GPIO扩展功能
   - 逐步添加传感器和电机

3. **系统集成测试**:
   - 验证所有功能协同工作
   - 检查电源负载和信号完整性
   - 优化软件性能

### 引脚选择原则
- **避免冲突**: 不使用已占用的GPIO
- **功能分组**: 相关功能的引脚尽量相邻
- **信号质量**: 高频信号远离敏感电路
- **扩展性**: 预留足够的GPIO用于未来扩展

---

*文档版本: v1.2 | 更新日期: 2025-07-12 | 包含完整硬件配置和实际状态*
