# AI玩具系统架构与硬件连接汇总文档

AI 玩具系统架构与硬件连接汇总文档


一、功能架构设计（基于 ESP32-S3 R16N8 与小智 AI）


### 1. 系统分层架构&#xA;

采用 “四层架构 + 多任务协同” 设计，基于 ESP-IDF 框架（FreeRTOS）实现语音交互与硬件控制的闭环，其中小智 AI 与硬件通过 MCP（GPIO 扩展芯片）进行通信：




| **层级**        | 核心功能&#xA;                                                     | 关键组件 / 技术&#xA;                                              |
| ------------- | ------------------------------------------------------------- | ----------------------------------------------------------- |
| **小智 AI 交互层** | 语音识别（ASR）、对话生成、情感化回复、角色定制（如童声、活泼性格）。&#xA;                     | 小智 AI 本地化模型、I2S 音频驱动、MCP 通信接口&#xA;                          |
| **核心任务层**     | 多任务并发处理：语音交互（最高优先级）、屏幕渲染、舵机控制、电机驱动、人物跟随控制。&#xA;               | FreeRTOS 任务 / 消息队列、事件通知、MCP 数据转发&#xA;                       |
| **设备抽象层**     | 封装硬件驱动，提供标准化接口（如 “显示表情”“转动舵机”“电机移动”“人物跟随”），通过 MCP 与硬件交互。&#xA; | 自定义设备接口（屏蔽底层硬件差异）、MCP 通信协议&#xA;                             |
| **硬件驱动层**     | 直接控制屏幕、舵机、电机、毫米波雷达等硬件，通过 MCP 接收来自核心任务层的控制信号。&#xA;             | `esp_lcd_gc9a01`、`lvgl`、`ledc`、L298N 驱动、MCP 驱动、毫米波雷达驱动&#xA; |

### 2. 核心任务与协同逻辑&#xA;



*   **语音交互任务**：作为 “中枢”，解析用户指令后生成硬件控制指令，通过 MCP 通信接口发送至硬件驱动层，支持 “开始跟随”“停止跟随” 等指令。


*   **屏幕渲染任务**：通过 MCP 接收表情指令（如开心、疑问、专注跟随），调用 LVGL 绘制拟人化 / 卡通化眼神（双屏联动）。


*   **舵机控制任务**：通过 MCP 接收角度指令，通过 PWM 信号驱动 180 度舵机完成上下范围内的移动动作（如跟随过程中的挥手示意）。


*   **电机驱动任务**：通过 MCP 接收移动指令（方向 + 速度），通过 L298N 控制电机实现前进 / 转向，配合人物跟随逻辑调整运动状态。


*   **人物跟随控制任务**：通过 MCP 接收毫米波雷达检测到的人体目标信息（距离、角度等），生成电机控制指令，实现稳定跟随。


**交互闭环示例**：


用户说 “跟着我”→ 小智 AI 解析为 “开启人物跟随”→ 启动毫米波雷达检测人体目标，通过 MCP 向电机任务发送跟随控制指令，向屏幕任务发送 “专注跟随表情”，向舵机任务发送 “自然下垂姿势”。


二、硬件组件及连接总表


### 1. 核心控制器及所需组件&#xA;



*   **核心控制器**：ESP32-S3 R16N8 开发板（16MB Flash + 8MB PSRAM，具备强大的运算和存储能力，能流畅运行小智 AI 模型及多任务处理）


*   **MCP 组件**：MCP23017 GPIO 扩展芯片（通过 I2C 与 ESP32-S3 连接，扩展 GPIO 用于小智 AI 与硬件通信）


*   **辅助组件**：USB 数据线（用于供电与调试）、面包板（临时搭建电路）、杜邦线（连接各模块）


### 2. 语音模块及所需组件（麦克风 + 功放）&#xA;



*   **麦克风模块**：INMP441 全向麦克风（I2S 接口）


*   **功放模块**：MAX98357A 数字音频功放（支持 3.3V 供电）


*   **辅助组件**：3.5mm 音频接口或扬声器（连接 MAX98357A 输出端）、100nF 滤波电容（麦克风 VCC 与 GND 之间）




| 模块&#xA;          | 引脚 / 功能&#xA;   | ESP32-S3 R16N8 引脚&#xA; | 备注&#xA;                         |
| ---------------- | -------------- | ---------------------- | ------------------------------- |
| **INMP441 麦克风**  | VDD（3.3V）&#xA; | 3.3V&#xA;              | 左 / 右声道选择（L/R 接 GND = 左声道）&#xA; |
|                  | GND&#xA;       | GND&#xA;               | 共地&#xA;                         |
|                  | WS（帧时钟）&#xA;   | GPIO4&#xA;             | I2S\_LRCK（与功放共用）&#xA;           |
|                  | SCK（位时钟）&#xA;  | GPIO5&#xA;             | I2S\_BCK（与功放共用）&#xA;            |
|                  | SD（数据输出）&#xA;  | GPIO6&#xA;             | I2S\_DATA\_IN&#xA;              |
| **MAX98357A 功放** | Vin（3.3V）&#xA; | 3.3V&#xA;              | SD（关机控制）接 3.3V（常开机）&#xA;        |
|                  | GND&#xA;       | GND&#xA;               | 共地&#xA;                         |
|                  | DIN（音频输入）&#xA; | GPIO7&#xA;             | I2S\_DATA\_OUT&#xA;             |
|                  | BCLK（位时钟）&#xA; | GPIO15&#xA;            | 与麦克风共用&#xA;                     |
|                  | LRC（帧时钟）&#xA;  | GPIO16&#xA;            | 与麦克风共用&#xA;                     |

### 3. 双屏及所需组件（GC9A01 圆屏，SPI 接口）&#xA;



*   **屏幕模块**：2 块 1.28 寸 GC9A01 圆形 TFT 屏幕（240×240 分辨率）


*   **辅助组件**：100μF 电解电容（屏幕 VCC 与 GND 之间滤波）、SPI 转接板（可选，方便连接）、MCP 连接线路




| 屏幕引脚&#xA;        | 左屏连接&#xA; | 右屏连接&#xA; | 连接方式（经 MCP 扩展）&#xA; | ESP32-S3 R16N8 引脚（MCP 连接端）&#xA; | 备注&#xA;              |
| ---------------- | --------- | --------- | ------------------- | ------------------------------- | -------------------- |
| VCC（3.3V）&#xA;   | 3.3V&#xA; | 3.3V&#xA; | 电源直接连接&#xA;         | -&#xA;                          | 并联 100μF 电容滤波&#xA;   |
| GND&#xA;         | GND&#xA;  | GND&#xA;  | 共地直接连接&#xA;         | -&#xA;                          | 共地&#xA;              |
| SCK（时钟）&#xA;     | SCK&#xA;  | SCK&#xA;  | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO0 → GPIO19&#xA;         | 共用 SPI 时钟线&#xA;      |
| SDA（数据）&#xA;     | SDA&#xA;  | SDA&#xA;  | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO1 → GPIO20&#xA;         | 共用 SPI 数据线（仅输出）&#xA; |
| DC（数据 / 命令）&#xA; | DC&#xA;   | DC&#xA;   | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO2 → GPIO21&#xA;         | 高低电平区分数据 / 命令&#xA;   |
| RES（复位）&#xA;     | RES&#xA;  | RES&#xA;  | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO3 → GPIO1&#xA;          | 初始化时拉低复位&#xA;        |
| CS（片选）&#xA;      | CS&#xA;   | -&#xA;    | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO4 → GPIO2&#xA;          | 左屏独立片选（低电平有效）&#xA;   |
|                  | -&#xA;    | CS&#xA;   | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO5 → GPIO45&#xA;         | 右屏独立片选（低电平有效）&#xA;   |

### 4. 180 度舵机及所需组件（双臂，PWM 控制，仅上下运动）&#xA;



*   **舵机模块**：2 个 SG90 180 度舵机（扭矩 1.8kg・cm@4.8V），轴水平安装，仅实现上下方向运动（0°-180°）


*   **辅助组件**：5V 外接电源（如 2 节 18650 锂电池 + 升压模块）、舵机支架（固定双臂结构，确保仅上下运动）、MCP 连接线路




| 舵机引脚&#xA;    | 左臂舵机&#xA;     | 右臂舵机&#xA;     | 连接方式（经 MCP 扩展）&#xA; | ESP32-S3 R16N8 引脚（MCP 连接端）&#xA;                   | 备注&#xA;                        |
| ------------ | ------------- | ------------- | ------------------- | ------------------------------------------------- | ------------------------------ |
| VCC&#xA;     | 5V（外接电源）&#xA; | 5V（外接电源）&#xA; | 电源直接连接&#xA;         | -&#xA;                                            | 避免 ESP32-S3 R16N8 电源过载&#xA;    |
| GND&#xA;     | GND&#xA;      | GND&#xA;      | 共地直接连接&#xA;         | -&#xA;                                            | 共地&#xA;                        |
| PWM（控制）&#xA; | PWM&#xA;      | PWM&#xA;      | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO6 → GPIO9（左臂）、MCP GPIO7 → GPIO10（右臂）&#xA; | 接 LEDC 硬件 PWM 通道，控制上下运动角度&#xA; |

### 5. 电机驱动及所需组件（L298N 驱动）&#xA;



*   **驱动模块**：L298N 双路电机驱动板（支持 5-12V 电机）


*   **辅助组件**：7.4V 锂电池（电机电源）、电机固定架、履带或车轮（根据移动方式选择）、1N4001 续流二极管（4 个，保护 L298N）、MCP 连接线路




| L298N 引脚&#xA; | 功能&#xA;         | 连接方式（经 MCP 扩展）&#xA;     | ESP32-S3 R16N8 引脚（MCP 连接端）&#xA; | 备注&#xA;                 |
| ------------- | --------------- | ----------------------- | ------------------------------- | ----------------------- |
| **控制信号**      | IN1（左电机方向）&#xA; | 经 MCP 扩展 GPIO&#xA;      | MCP GPIO8 → GPIO25&#xA;         | 高低电平控制方向（与 IN2 配合）&#xA; |
|               | IN2（左电机方向）&#xA; | 经 MCP 扩展 GPIO&#xA;      | MCP GPIO9 → GPIO26&#xA;         |                         |
|               | ENA（左电机调速）&#xA; | 经 MCP 扩展 GPIO（PWM）&#xA; | MCP GPIO10 → GPIO27（PWM）&#xA;   | 占空比 0\~100% 控制转速&#xA;   |
|               | IN3（右电机方向）&#xA; | 经 MCP 扩展 GPIO&#xA;      | MCP GPIO11 → GPIO28&#xA;        | 高低电平控制方向（与 IN4 配合）&#xA; |
|               | IN4（右电机方向）&#xA; | 经 MCP 扩展 GPIO&#xA;      | MCP GPIO12 → GPIO29&#xA;        |                         |
|               | ENB（右电机调速）&#xA; | 经 MCP 扩展 GPIO（PWM）&#xA; | MCP GPIO13 → GPIO30（PWM）&#xA;   | 占空比 0\~100% 控制转速&#xA;   |
| **电源与电机**     | 12V（电机电源）&#xA;  | 独立电源连接&#xA;             | -&#xA;                          | 独立供电，避免影响主板&#xA;        |
|               | GND&#xA;        | 共地直接连接&#xA;             | -&#xA;                          | 必须共地&#xA;               |
|               | OUT1/OUT2&#xA;  | 直接连接电机&#xA;             | -&#xA;                          | 无正负，方向由 IN1/IN2 控制&#xA; |
|               | OUT3/OUT4&#xA;  | 直接连接电机&#xA;             | -&#xA;                          | 无正负，方向由 IN3/IN4 控制&#xA; |

### 6. W25Q64 存储模块及所需组件&#xA;



*   **存储模块**：W25Q64 闪存模块（8MB 容量，SPI 接口）


*   **辅助组件**：10KΩ 下拉电阻（用于稳定 SPI 通信）、MCP 连接线路




| W25Q64 引脚&#xA; | 功能&#xA;   | 连接方式（经 MCP 扩展）&#xA; | ESP32-S3 R16N8 引脚（MCP 连接端）&#xA; | 备注&#xA;       |
| -------------- | --------- | ------------------- | ------------------------------- | ------------- |
| VCC&#xA;       | 电源正&#xA;  | 电源直接连接&#xA;         | -&#xA;                          | 3.3V 供电&#xA;  |
| GND&#xA;       | 接地&#xA;   | 共地直接连接&#xA;         | -&#xA;                          | 共地&#xA;       |
| CS&#xA;        | 片选&#xA;   | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO14 → GPIO31&#xA;        | 低电平有效&#xA;    |
| SCK&#xA;       | 时钟&#xA;   | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO15 → GPIO32&#xA;        | SPI 时钟线&#xA;  |
| MOSI&#xA;      | 数据输入&#xA; | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO16 → GPIO33&#xA;        | 主机输出从机输入&#xA; |
| MISO&#xA;      | 数据输出&#xA; | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO17 → GPIO34&#xA;        | 主机输入从机输出&#xA; |

### 7. 超声波传感器及所需组件（避障）&#xA;



*   **传感器模块**：HC-SR04 超声波传感器（2 个，左右侧各 1 个，0.2-4 米检测距离）


*   **辅助组件**：MCP 连接线路




| 超声波引脚&#xA; | 功能&#xA;  | 连接方式（经 MCP 扩展）&#xA; | ESP32-S3 R16N8 引脚（MCP 连接端）&#xA; | 备注&#xA;     |
| ---------- | -------- | ------------------- | ------------------------------- | ----------- |
| VCC&#xA;   | 电源正&#xA; | 电源直接连接&#xA;         | -&#xA;                          | 5V 供电&#xA;  |
| GND&#xA;   | 接地&#xA;  | 共地直接连接&#xA;         | -&#xA;                          | 共地&#xA;     |
| Trig&#xA;  | 触发&#xA;  | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO20 → GPIO37&#xA;        | 触发信号输出&#xA; |
| Echo&#xA;  | 回声&#xA;  | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO21 → GPIO38&#xA;        | 回声信号输入&#xA; |

### 8. 红外传感器及所需组件（避险）&#xA;



*   **传感器模块**：TCRT5000 红外反射传感器（2 个，底盘前端左右各 1 个，检测距离 5-30cm）


*   **辅助组件**：MCP 连接线路




| 红外引脚&#xA; | 功能&#xA;   | 连接方式（经 MCP 扩展）&#xA; | ESP32-S3 R16N8 引脚（MCP 连接端）&#xA; | 备注&#xA;                 |
| --------- | --------- | ------------------- | ------------------------------- | ----------------------- |
| VCC&#xA;  | 电源正&#xA;  | 电源直接连接&#xA;         | -&#xA;                          | 3.3V 供电&#xA;            |
| GND&#xA;  | 接地&#xA;   | 共地直接连接&#xA;         | -&#xA;                          | 共地&#xA;                 |
| OUT&#xA;  | 数据输出&#xA; | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO22 → GPIO39&#xA;        | 高电平 = 有地面，低电平 = 悬崖&#xA; |

### 9. LD2402 毫米波雷达及所需组件（人物跟随）&#xA;



*   **雷达模块**：LD2402 毫米波雷达（0.5-8 米检测距离，水平 ±60° 检测角度）


*   **辅助组件**：5V 电源（与主控共地）、MCP 连接线路




| LD2402 引脚&#xA; | 功能&#xA;   | 连接方式（经 MCP 扩展）&#xA; | ESP32-S3 R16N8 引脚（MCP 连接端）&#xA; | 备注&#xA;                |
| -------------- | --------- | ------------------- | ------------------------------- | ---------------------- |
| VCC&#xA;       | 电源正&#xA;  | 电源直接连接&#xA;         | -&#xA;                          | 5V 供电，建议使用 LDO 稳压&#xA; |
| GND&#xA;       | 接地&#xA;   | 共地直接连接&#xA;         | -&#xA;                          | 共地&#xA;                |
| TX&#xA;        | 数据发送&#xA; | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO18 → GPIO35&#xA;        | 雷达数据输出至主控&#xA;         |
| RX&#xA;        | 数据接收&#xA; | 经 MCP 扩展 GPIO&#xA;  | MCP GPIO19 → GPIO36&#xA;        | 主控指令输入至雷达&#xA;         |

三、各硬件可执行动作


### 1. 双屏（GC9A01 圆屏，模拟 “眼睛”）&#xA;

#### （1）基础表情动作&#xA;



*   开心：双屏瞳孔弯成月牙形，暖光闪烁，快速眨眼 2 次


*   疑问：瞳孔呈圆形，左右缓慢移动（模拟 “打量”），屏边缘显示问号符号


*   专注：瞳孔缩小，稳定亮白光，无闪烁（适合听故事场景）


*   惊讶：瞳孔突然放大至最大尺寸，屏体瞬间切换为冷光（1 秒后恢复）


*   委屈：瞳孔下垂呈水滴状，屏体边缘泛红光，缓慢眨眼（1.5 秒 / 次）


*   困倦：瞳孔模糊化（半透明效果），双屏同步缓慢上下闭合（模拟打盹）


#### （2）交互响应动作&#xA;



*   指令确认：接收到用户明确指令（如 “转圈”“跟着我”），瞳孔中心显示对勾符号（1 秒后消失）


*   距离感应：用户靠近（<20cm）时瞳孔缩小（避免 “紧盯感”），远离（>50cm）时瞳孔放大（增强存在感）


*   跟随注视：人物跟随状态下，双屏瞳孔随雷达检测到的人体目标方向同步偏移，模拟专注跟随的眼神


### 2. 180 度舵机（双臂，仅上下范围内移动）&#xA;

#### （1）单臂动作（基于上下运动范围）&#xA;



*   举手：单臂从 0°（下垂）抬至 90°（水平举过肩），停留 1 秒后复位


*   抬臂：单臂从 0° 平稳抬至 45°（半抬），保持姿势不变


*   挥手：单臂在 30°-60° 范围内快速上下摆动（1 秒 3 次），模拟挥手动作


*   下摆：单臂从 90°（水平）缓慢下摆至 0°（下垂），完成放下动作


*   托举：单臂从 0° 抬至 60°（半抬，掌心向上），保持 3 秒（模拟托举物品）


#### （2）双臂协同动作（基于上下运动范围）&#xA;



*   拥抱：双臂同时从 0° 抬至 90°（水平张开），再缓慢收回至 45°（胸前合拢）


*   鼓掌：双臂在 45° 位置快速上下交替摆动（左臂抬至 60° 时右臂降至 30°，反之亦然）


*   投降：双臂同时抬至 120°（上举过头），保持 2 秒后缓慢放下


*   挥手再见：双臂同步在 30°-60° 范围内上下摆动 3 次，幅度逐渐减小至停止


*   轻抚：双臂从 0° 缓慢抬至 30°，再缓慢收回至 0°，重复 2 次（模拟轻抚动作）


*   跟随伴随动作：人物跟随状态下，双臂自然下垂，随移动节奏小幅度上下摆动（1 秒 1 次），模拟自然行走姿态


### 3. 电机（L298N 驱动）&#xA;

#### （1）基础移动动作&#xA;



*   前进 / 后退：以 30%/50%/80% 三档速度移动（对应慢速靠近 / 中速移动 / 快速响应）


*   转向：左转弯（左电机停转，右电机转动）、右转弯（右电机停转，左电机转动），转向角度可通过时长控制（如转 90° 需 1 秒）


*   原地旋转：双电机反向转动（左正转 + 右反转），实现 360° 自旋（2 秒 / 圈）


*   点动：短距离移动（5cm）后立即停止（用于 “点头” 式回应）


#### （2）交互移动动作&#xA;



*   避让：碰到障碍物（超声波传感器检测距离 < 10cm），自动后退 10cm + 右转 90°


*   庆祝转圈：原地顺时针旋转 360°（双电机差速，右电机速度是左电机 1.5 倍），同时播放欢快音效


*   示好靠近：接收到 “过来” 指令时，以 50% 速度前进，距离用户 30cm 时减速至 20%，最终轻触用户后停止


*   害羞后退：用户做出 “推开” 手势（需配合红外传感器），电机反转后退 15cm，同时减速至停止（模拟 “害羞躲闪”）


#### （3）人物跟随动作&#xA;



*   稳定跟随：根据毫米波雷达检测到的人体目标距离（1.5-3 米为最佳跟随距离），自动调整速度，保持与目标的相对距离稳定


*   方向修正：当雷达检测到人体目标向左 / 右偏移（角度 < 30°），电机对应向左 / 右微调转向，使目标保持在正前方


*   加速追赶：当人体目标远离（距离 > 3 米），电机以 80% 速度前进追赶，距离回归 2 米时减速至 50%


*   减速靠近：当人体目标靠近（距离 < 1.5 米），电机减速至 20% 或停止，避免碰撞


*   丢失目标应对：当雷达 1-2 秒未检测到人体目标，电机减速至停止并原地小幅度旋转（左 / 右各转 15°），同时雷达扩大检测范围，重新锁定目标后恢复跟随


### 4. LD2402 毫米波雷达（人物跟随）&#xA;



*   目标检测与锁定：实时检测并锁定距离最近的动态人体目标，输出目标距离、角度等信息


*   目标过滤：过滤非人体目标（如宠物、静态物体），优先跟踪人体特征明显的目标


*   参数配置：可通过指令设置最大检测距离（如 5 米）、灵敏度（如 80%），适配不同跟随场景


*   状态反馈：向主控反馈目标是否有效、是否在检测范围内等状态信息，辅助跟随逻辑判断


### 5. 超声波传感器（避障）&#xA;



*   障碍物检测：实时检测前方及两侧障碍物距离，当距离 < 10cm 时触发避障信号


*   距离反馈：向主控输出障碍物距离数据，支持连续检测（检测周期 50ms）


*   多方向检测：左右传感器分别检测两侧障碍物，为主控提供避让方向决策依据


### 6. 红外传感器（避险）&#xA;



*   悬崖检测：实时检测设备底部是否有地面，当检测到悬空（反射信号弱）时输出避险信号


*   边缘识别：通过两侧传感器信号差异判断设备是否靠近边缘，提前触发减速或转向


*   状态反馈：向主控输出 “有地面 / 无地面” 状态，确保设备远离悬崖等危险区域


### 7. W25Q64 存储模块（数据存储）&#xA;



*   存储表情资源：将双屏所需的各类表情帧数据（如开心、疑问等表情的图像数据）存储在该模块中，便于系统通过 MCP 快速读取调用


*   保存交互记录：记录用户与玩具的交互信息，如常用指令、交互时间等，通过 MCP 与核心任务层交互，为后续个性化交互提供数据支持


*   存储系统配置：保存系统的各项配置参数，如舵机角度校准值、电机速度参数、雷达跟随参数（最佳距离、灵敏度）等，确保系统重启后能恢复之前的设置


### 8. 硬件联动组合动作（多模块协同）&#xA;



*   “欢迎” 场景组合：电机前进 20cm（30% 速度），舵机双臂同时挥手（30°-60° 上下摆动），屏幕显示开心表情 + 快速眨眼


*   “游戏” 场景组合：电机左右小幅晃动（5cm 幅度，1 秒 / 次），舵机双臂交替抬臂（左臂举 90° 时右臂下垂，反之亦然），屏幕瞳孔随晃动节奏左右移动，模拟 “玩闹时的眼神”


*   “安抚” 场景组合：电机静止不动，舵机双臂缓慢抬至 45°（半抱姿势），保持 3 秒后缓慢放下，屏幕显示专注表情，暖光稳定常亮


*   “好奇探索” 场景组合：屏幕显示疑问表情（瞳孔左右移动），电机以 20% 速度缓慢转向声音来源方向，舵机单臂抬至 45°（抬臂指物姿势）


*   “告别” 场景组合：电机缓慢后退 10cm，舵机双臂同时挥手（30°-60° 上下摆动），屏幕瞳孔显示波浪线（模拟 “再见”），1 秒后停止所有动作


*   “人物跟随” 场景组合：电机执行稳定跟随动作（根据雷达数据调整速度和方向），双屏显示专注跟随表情（瞳孔随目标方向偏移），舵机双臂自然下垂并小幅度摆动，模拟自然跟随状态


*   “避障避险” 场景组合：超声波检测到障碍物或红外检测到悬崖时，电机立即停止并后退 5cm，屏幕显示惊讶表情，舵机双臂抬至 90°（警戒姿势），随后转向无障碍物 / 安全方向


四、技术方案


### 1. 软件开发框架搭建&#xA;



*   基于 ESP-IDF v5.0 及以上版本搭建开发环境，集成 MCP 驱动库和毫米波雷达驱动库，实现通过 MCP 与硬件及雷达的通信。利用 FreeRTOS 实时操作系统进行多任务管理，确保各硬件模块的控制任务能够通过 MCP 高效、有序地进行通信。


*   集成 LVGL 图形库，通过配置 LVGL 的显示缓冲区和刷新回调函数，实现双屏的图形渲染和表情显示，图形渲染指令通过 MCP 传输。同时，将 LVGL 的任务优先级设置为低于语音交互任务和人物跟随任务，以保证核心功能的实时性。


*   移植小智 AI 的本地化模型，通过调用模型提供的 API 实现语音识别、对话生成等功能，模型生成的硬件控制指令（包括人物跟随指令）通过 MCP 通信接口发送，将模型的运行任务设置为最高优先级，确保语音交互的响应速度。


### 2. 硬件驱动实现（基于 MCP 通信）&#xA;



*   MCP 驱动：实现 MCP23017 芯片的初始化和数据收发功能，通过 I2C 接口与 MCP 芯片通信，配置 MCP 的 GPIO 方向（输入 / 输出），实现对扩展 GPIO 的读写控制，作为小智 AI 与硬件之间的数据中转枢纽。


*   屏幕驱动：基于`esp_lcd_gc9a01`组件实现 GC9A01 屏幕的初始化和基本操作，屏幕的 SPI 信号通过 MCP 扩展 GPIO 传输。通过 MCP 接收来自核心任务层的控制指令，利用片选信号区分左右两块屏幕，实现双屏的独立控制。结合 LVGL 库，实现复杂的表情显示和动画效果，包括人物跟随状态下的专注眼神。


*   舵机驱动：利用 ESP32-S3 R16N8 的 LEDC 硬件 PWM 功能生成 PWM 信号，通过 MCP 扩展 GPIO 传输至舵机。生成符合 SG90 180 度舵机要求的 PWM 信号（频率 50Hz，脉冲宽度 0.5ms-2.5ms 对应 0°-180° 上下运动范围）。通过 MCP 接收角度控制指令，实现对左右双臂舵机的独立控制，包括人物跟随状态下的伴随动作。


*   电机驱动：通过 MCP 扩展 GPIO 接收核心任务层的控制信号（包括人物跟随控制指令），控制 L298N 电机驱动板的方向控制引脚（IN1、IN2、IN3、IN4），实现电机的正反转、停止控制以及方向修正。利用 LEDC 硬件 PWM 功能控制驱动板的使能引脚（ENA、ENB），PWM 信号通过 MCP 传输，通过调整 PWM 占空比实现电机的转速调节，满足跟随过程中的速度变化需求。


*   雷达驱动：实现 LD2402 毫米波雷达的初始化和数据收发功能，通过 UART 接口经 MCP 扩展 GPIO 与雷达通信，配置雷达的检测参数（如最大距离、灵敏度、目标过滤模式），实时读取雷达输出的人体目标信息（距离、角度等），并将数据传输至人物跟随控制任务。


*   超声波驱动：通过 MCP 扩展 GPIO 向 HC-SR04 传感器发送触发信号（10us 高电平），并通过 MCP 接收回声信号，计算障碍物距离（距离 = 回声时间 ×0.034/2），当距离 < 阈值时向主控发送避障信号。


*   红外驱动：通过 MCP 扩展 GPIO 读取 TCRT5000 传感器的输出信号，当检测到低电平（无地面反射）时，向主控发送避险信号，触发悬崖规避逻辑。


*   存储模块驱动：基于 SPI 接口实现 W25Q64 存储模块的驱动，SPI 信号通过 MCP 扩展 GPIO 传输，通过 MCP 接收数据读写指令，实现对表情资源、交互记录、系统配置（包括人物跟随参数）等数据的存储和读取。通过对存储模块进行分区管理，提高数据存储的效率和安全性。


### 3. 多任务调度与 MCP 通信策略&#xA;



*   任务优先级划分：将语音交互任务设置为最高优先级，确保能够及时通过 MCP 响应用户的语音指令；人物跟随控制任务设置为次高优先级，保证跟随动作的实时性；避障避险任务优先级高于普通电机驱动任务，确保安全机制优先触发；屏幕渲染任务和舵机控制任务设置为中等优先级；电机驱动任务（非跟随 / 避障状态）设置为较低优先级；系统管理任务（如数据存储、故障检测等）设置为最低优先级。


*   MCP 数据传输协议：定义统一的数据帧格式，包含设备地址（区分屏幕、舵机、电机、雷达、超声波、红外等）、指令类型（控制指令、状态查询指令等）、数据长度、数据内容和校验位，确保通过 MCP 传输的数据准确无误，特别是人物跟随和避障避险相关的实时数据。


*   任务间通信：采用 FreeRTOS 的消息队列结合 MCP 通信实现任务间的数据交互，语音交互任务解析用户指令后生成的硬件控制指令（包括人物跟随指令）通过消息队列发送至 MCP 驱动任务，由 MCP 驱动任务通过 MCP 芯片传输到硬件。雷达、超声波、红外等传感器的数据处理任务将解析后的信息通过消息队列发送至对应控制任务（人物跟随控制任务、避障避险任务），各控制任务生成的执行指令通过 MCP 传输至硬件驱动模块。


### 4. 数据交互流程（基于 MCP）&#xA;



*   语音数据流程：麦克风采集用户的语音信号，通过 I2S 接口传输到 ESP32-S3 R16N8，经过预处理后送入小智 AI 模型进行语音识别。模型识别出用户的意图（包括 “跟着我” 等人物跟随指令）后生成相应的对话回复和硬件控制指令，对话回复通过 I2S 接口传输到功放模块进行播放，硬件控制指令按照 MCP 数据帧格式打包，通过 MCP 芯片发送给相应的硬件驱动。


*   表情数据流程：当需要显示某种表情时（包括人物跟随状态下的专注表情），核心任务层生成表情显示指令，通过 MCP 传输到屏幕驱动。系统从 W25Q64 存储模块中读取相应的表情帧数据（数据读取通过 MCP 进行），经过解析后送入 LVGL 图形库，LVGL 将表情帧数据转换为屏幕可显示的像素数据，通过 MCP 控制屏幕进行显示。


*   传感器数据流程：LD2402 毫米波雷达、HC-SR04 超声波传感器、TCRT5000 红外传感器实时检测目标信息，通过 MCP 扩展 GPIO 将数据传输至 ESP32-S3 R16N8，各传感器数据处理任务解析数据后，将目标距离、角度、状态等信息通过消息队列发送至对应控制任务（人物跟随控制任务、避障避险任务），为控制动作提供决策依据。


*   控制指令流程：语音交互任务生成的硬件控制指令（包括人物跟随指令）、人物跟随控制任务和避障避险任务生成的电机控制指令按照 MCP 通信协议打包，通过 MCP 芯片发送到相应的硬件控制任务（如舵机控制任务、电机驱动任务）对应的 MCP 扩展 GPIO。硬件控制任务从 MCP 扩展 GPIO 读取指令并解析执行相应的操作，执行完成后将执行结果通过 MCP 反馈给语音交互任务、人物跟随控制任务或避障避险任务。


五、开发步骤拆分


### 1. 小智部署及语音模块调试&#xA;



*   **目标**：实现小智 AI 的本地化部署，确保麦克风收音、音频播放正常，配网及对话功能稳定。


*   **具体步骤**：



    *   搭建 ESP-IDF 开发环境，移植小智 AI 本地化模型，配置模型运行所需的内存和算力资源。


    *   连接 INMP441 麦克风和 MAX98357A 功放模块，编写音频采集和播放驱动，测试麦克风收音是否清晰（无杂音、音量适中），功放播放是否正常（音质清晰、无失真）。


    *   进行配网测试，通过小智 AI 的配网指令完成设备联网，验证联网稳定性（连续 3 次配网成功率≥90%）。


    *   测试基础对话功能，下发简单指令（如 “你好”“今天天气怎么样”），验证小智 AI 的响应速度（延迟≤1 秒）和回复准确性（语义理解正确率≥85%）。


*   **验收标准**：麦克风收音清晰，功放播放正常，配网成功，对话响应及时且准确。


### 2. 表情功能实现&#xA;



*   **目标**：实现双屏的模拟双眼效果，结合 W25Q64 存储模块完成表情开发及与小智 AI 的互动。


*   **具体步骤**：



    *   连接 GC9A01 双屏和 W25Q64 存储模块，编写屏幕驱动和存储模块驱动，实现屏幕初始化、像素绘制及数据读写功能。


    *   在 W25Q64 中存储预设表情帧数据（开心、疑问、专注等），编写数据读取接口，确保屏幕能正确调用并显示表情。


    *   集成 LVGL 图形库，开发表情动画效果（如眨眼、瞳孔移动），实现表情的平滑切换（切换时间≤0.5 秒）。


    *   关联小智 AI 交互，根据对话内容触发相应表情（如收到 “开心的事” 时显示开心表情，收到 “疑问指令” 时显示疑问表情），测试表情与语义的匹配度。


*   **验收标准**：双屏显示清晰无花屏，表情切换流畅，能根据小智 AI 的交互内容正确触发对应表情。


### 3. 舵机模拟双臂功能开发&#xA;



*   **目标**：实现舵机的单臂、复合动作控制，并与小智 AI 交互关联，支持通过语音指令触发动作。


*   **具体步骤**：



    *   连接 2 个 SG90 舵机，编写舵机驱动，通过 LEDC 硬件 PWM 生成控制信号，校准舵机角度（0°-180° 对应准确的上下运动范围）。


    *   开发单臂动作（举手、挥手等）和双臂复合动作（拥抱、鼓掌等）的控制函数，确保动作平滑无抖动（摆动幅度误差≤5°）。


    *   关联小智 AI 交互，将语音指令（如 “举手”“抱抱我”）与相应动作绑定，测试指令识别与动作执行的一致性（指令触发动作成功率≥90%）。


    *   优化动作与表情的协同，如执行 “挥手” 动作时屏幕显示开心表情，增强交互的协调性。


*   **验收标准**：舵机动作准确平滑，能响应小智 AI 的语音指令并执行对应动作，动作与表情协同自然。


### 4. 履带底盘功能实现&#xA;



*   **目标**：实现履带底盘的基本移动功能（前进、后退、转向等），并与小智 AI 交互关联。


*   **具体步骤**：



    *   连接 L298N 驱动板和电机，编写电机驱动程序，实现前进、后退、左右转向、加速、减速等基本动作，校准各动作的速度和角度（如 90° 转向误差≤5°）。


    *   测试底盘的移动稳定性，连续执行 10 次前进 - 后退 - 转向循环，验证电机运行是否顺畅（无卡顿、无异响）。


    *   关联小智 AI 交互，将语音指令（如 “前进”“向左转”）与底盘动作绑定，测试指令响应的及时性（指令下发后动作启动延迟≤0.5 秒）。


    *   优化移动过程中的动力输出，确保在不同地面（如木地板、地毯）上的移动一致性。


*   **验收标准**：底盘各动作执行准确，响应语音指令及时，移动稳定无异常。


### 5. 避障、避险及人物跟随功能开发&#xA;



*   **目标**：添加超声波、红外、毫米波雷达传感器，实现避障、避险和人物跟随功能，并与现有系统融合。


*   **具体步骤**：



    *   连接 HC-SR04 超声波传感器，编写避障逻辑，当检测到障碍物时触发后退 + 转向动作，测试避障准确性（障碍物识别距离误差≤10%，避障成功率≥95%）。


    *   连接 TCRT5000 红外传感器，编写避险逻辑，当检测到悬崖时立即停止并后退，测试避险可靠性（悬崖识别准确率≥98%，无误判）。


    *   连接 LD2402 毫米波雷达，编写人物跟随算法，实现稳定跟随、方向修正、加速追赶等功能，测试跟随效果（跟随距离误差≤0.3 米，方向修正响应时间≤0.5 秒）。


    *   关联小智 AI 交互，通过语音指令（如 “开始跟随”“停止跟随”）控制功能启停，测试多传感器数据的协同处理（避障 / 避险优先于跟随，确保安全）。


*   **验收标准**：避障、避险功能可靠，人物跟随稳定，能通过语音指令控制功能启停，多传感器协同工作正常。


六、关键技术约束与优化




1.  **MCP 通信可靠性**：


*   由于小智 AI 与硬件通过 MCP 进行通信，需保证 MCP 通信的稳定性，采用数据校验（如 CRC 校验）机制，避免数据传输错误导致硬件误动作，特别是人物跟随过程中的方向和速度控制指令。


*   合理设置 MCP 的 I2C 通信速率（建议 400kHz），在保证通信速度的同时，确保数据传输的稳定性，避免因速率过高导致数据丢失，影响人物跟随的实时性。


1.  **电源管理**：


*   舵机、电机、毫米波雷达需外接独立电源（5V/7.4V），避免与屏幕、语音模块、MCP 芯片共用电源导致电压波动，影响 MCP 通信稳定性和雷达检测精度。


*   电源接口处并联电容滤波（100μF），减少电机启动时的干扰，为 MCP 芯片、雷达和各硬件提供稳定的电源环境，保证人物跟随过程中的设备稳定运行。


1.  **资源优化**：


*   屏幕表情帧存储在 W25Q64 存储模块（压缩为 RGB565 格式），单帧约 112KB，双屏共 224KB，通过 MCP 进行数据读取，减少对 ESP32-S3 R16N8 内置 Flash 的占用，充分利用 MCP 扩展的存储资源。


*   小智 AI 模型量化为 int8 精度，降低内存占用，适配 ESP32-S3 R16N8 的 8MB PSRAM，模型生成的控制指令（包括人物跟随指令）通过 MCP 传输，减少对核心控制器资源的占用。


*   人物跟随算法优化：采用简化的比例控制算法（P 控制），根据目标距离和角度偏差直接计算电机速度和转向调整量，减少 CPU 算力消耗，确保实时响应。


1.  **实时性保障**：


*   语音任务和人物跟随任务优先级最高，MCP 通信任务优先处理这两类任务的数据，避免被其他任务阻塞（如电机控制通过 MCP 传输的指令延时不超过 50ms）。


*   屏幕采用 LVGL 部分刷新（仅更新变化区域），减少通过 MCP 传输的数据量，降低 MCP 总线占用，为人物跟随数据传输腾出带宽。


*   雷达数据采样率设置为 50Hz（每 20ms 一次），既能保证人物跟随的实时性，又不会因数据量过大占用过多系统资源。


六、开发与测试要点




1.  **MCP 通信测试**：


*   首先单独测试 MCP 芯片的通信功能，验证通过 MCP 扩展 GPIO 的读写控制是否正常，确保 MCP 能够准确传输数据，特别是雷达数据和人物跟随控制指令的传输准确性。可通过向 MCP 扩展 GPIO 写入数据，再读取回来进行比对，检查通信是否可靠。


1.  **分步验证**：


*   在 MCP 通信测试通过后，先测试语音模块（录音 + 播放）与 MCP 的协同工作，再验证屏幕显示（表情渲染）通过 MCP 控制的效果，然后单独测试毫米波雷达的目标检测功能和电机的基本移动功能，最后集成所有模块，重点测试人物跟随功能的稳定性和准确性。


1.  **故障排查**：


*   屏幕花屏：除检查 SPI 时钟（40MHz）、电源滤波外，需检查 MCP 与屏幕的连接是否松动，MCP 传输的 SPI 信号是否正常。


*   舵机抖动：除校准 PWM 占空比（确保 0.5ms-2.5ms 对应 0°-180° 上下运动范围）外，检查 MCP 传输的 PWM 信号是否稳定，MCP 的电源是否正常。


*   电机不动或跟随异常：除检查 L298N 电源、INx 引脚电平逻辑外，检查 MCP 传输到电机驱动板的控制信号是否正确，MCP 扩展 GPIO 的输出是否正常；若人物跟随异常，还需检查雷达是否正确检测到目标、雷达数据传输是否正常、跟随算法参数是否合理。


*   雷达检测异常：检查雷达电源是否稳定（5V）、雷达与 MCP 的连接是否松动、雷达参数配置是否正确（如最大距离、灵敏度）、是否存在电磁干扰（可远离电机等强干扰源测试）。


*   MCP 通信故障：检查 MCP 的 I2C 地址是否正确，I2C 总线是否有短路或断路，MCP 芯片是否损坏。


通过以上架构与硬件设计，可实现低成本（核心硬件 < 100 元）、本地化运行的 AI 玩具，支持情感化交互、避障避险、人物跟随等多模态反馈，小智 AI 与硬件通过 MCP 通信的方式，有效扩展了 GPIO 资源，LD2402 毫米波雷达的加入使人物跟随功能稳定可靠，esp32-s3 r16n8 开发板的 16MB Flash 和 8MB PSRAM 为系统的稳定运行和功能扩展提供了充足的资源保障。


> （注：文档部分内容可能由 AI 生成）
>