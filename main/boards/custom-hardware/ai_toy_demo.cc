/**
 * @file ai_toy_demo.cc
 * @brief AI玩具系统演示程序
 * 
 * 演示各个子系统的基本功能，包括表情显示、语音交互、运动控制等
 */

#include "system_controller.h"
#include "expression_system/expression_manager.h"
#include "motion_system/motion_controller.h"
#include "sensor_system/sensor_manager.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

static const char* TAG = "AI_TOY_DEMO";

namespace ai_toy_demo {

/**
 * @brief 演示回调类
 */
class DemoCallback : public system_controller::SystemCallback {
public:
    void OnSystemStateChanged(system_controller::SystemState old_state, 
                            system_controller::SystemState new_state) override {
        ESP_LOGI(TAG, "System state changed: %s -> %s", 
                 system_controller::SystemStateToString(old_state).c_str(),
                 system_controller::SystemStateToString(new_state).c_str());
    }
    
    void OnInteractionModeChanged(system_controller::InteractionMode old_mode, 
                                system_controller::InteractionMode new_mode) override {
        ESP_LOGI(TAG, "Interaction mode changed: %s -> %s", 
                 system_controller::InteractionModeToString(old_mode).c_str(),
                 system_controller::InteractionModeToString(new_mode).c_str());
    }
    
    void OnUserInteraction(const std::string& interaction_type, const std::string& data) override {
        ESP_LOGI(TAG, "User interaction: %s - %s", interaction_type.c_str(), data.c_str());
    }
    
    void OnSystemEvent(const system_controller::SystemEvent& event) override {
        ESP_LOGI(TAG, "System event: %s from %s", event.event_type.c_str(), event.source.c_str());
    }
    
    void OnSystemError(const std::string& error_message) override {
        ESP_LOGE(TAG, "System error: %s", error_message.c_str());
    }
};

/**
 * @brief 表情演示任务
 */
void ExpressionDemoTask(void* parameter) {
    ESP_LOGI(TAG, "Starting expression demo...");
    
    auto& expr_manager = expression_system::ExpressionManager::GetInstance();
    
    // 演示不同表情
    expression_system::EmotionType emotions[] = {
        expression_system::EmotionType::NEUTRAL,
        expression_system::EmotionType::HAPPY,
        expression_system::EmotionType::SAD,
        expression_system::EmotionType::SURPRISED,
        expression_system::EmotionType::FOLLOWING
    };
    
    int emotion_count = sizeof(emotions) / sizeof(emotions[0]);
    int current_emotion = 0;
    
    while (true) {
        // 切换表情
        expr_manager.SetExpression(emotions[current_emotion]);
        ESP_LOGI(TAG, "Showing expression: %s", 
                 expression_system::EmotionTypeToString(emotions[current_emotion]).c_str());
        
        // 等待3秒
        vTaskDelay(pdMS_TO_TICKS(3000));
        
        // 眨眼演示
        expr_manager.Blink(expression_system::EyePosition::BOTH, 2, 200);
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        current_emotion = (current_emotion + 1) % emotion_count;
    }
}

/**
 * @brief 语音交互演示任务
 */
void VoiceInteractionDemoTask(void* parameter) {
    ESP_LOGI(TAG, "Starting voice interaction demo...");
    
    auto& system_controller = system_controller::MainSystemController::GetInstance();
    
    // 模拟语音指令
    const char* voice_commands[] = {
        "你好",
        "跟着我",
        "挥手",
        "停止",
        "开心一点",
        "再见"
    };
    
    int command_count = sizeof(voice_commands) / sizeof(voice_commands[0]);
    int current_command = 0;
    
    while (true) {
        // 等待10秒
        vTaskDelay(pdMS_TO_TICKS(10000));
        
        // 处理语音指令
        ESP_LOGI(TAG, "Processing voice command: %s", voice_commands[current_command]);
        system_controller.ProcessVoiceCommand(voice_commands[current_command]);
        
        current_command = (current_command + 1) % command_count;
    }
}

/**
 * @brief 运动控制演示任务
 */
void MotionDemoTask(void* parameter) {
    ESP_LOGI(TAG, "Starting motion demo...");
    
    // 等待系统初始化完成
    vTaskDelay(pdMS_TO_TICKS(5000));
    
    while (true) {
        // 等待15秒
        vTaskDelay(pdMS_TO_TICKS(15000));
        
        ESP_LOGI(TAG, "Demonstrating motion control...");
        
        // 这里可以添加具体的运动控制演示
        // 由于是存根实现，只是打印日志
        ESP_LOGI(TAG, "Motion demo: Wave action");
        ESP_LOGI(TAG, "Motion demo: Turn left");
        ESP_LOGI(TAG, "Motion demo: Move forward");
        ESP_LOGI(TAG, "Motion demo: Stop");
    }
}

/**
 * @brief 传感器监控演示任务
 */
void SensorMonitorDemoTask(void* parameter) {
    ESP_LOGI(TAG, "Starting sensor monitor demo...");
    
    // 等待系统初始化完成
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    while (true) {
        // 等待8秒
        vTaskDelay(pdMS_TO_TICKS(8000));
        
        ESP_LOGI(TAG, "Monitoring sensors...");
        
        // 模拟传感器数据
        ESP_LOGI(TAG, "Sensor demo: Ultrasonic distance = 1.5m");
        ESP_LOGI(TAG, "Sensor demo: Person detected at 2.0m");
        ESP_LOGI(TAG, "Sensor demo: No obstacles detected");
    }
}

/**
 * @brief 系统状态监控任务
 */
void SystemStatusDemoTask(void* parameter) {
    ESP_LOGI(TAG, "Starting system status demo...");
    
    auto& system_controller = system_controller::MainSystemController::GetInstance();
    
    while (true) {
        // 等待30秒
        vTaskDelay(pdMS_TO_TICKS(30000));
        
        // 打印系统统计信息
        std::string stats = system_controller.GetSystemStatistics();
        ESP_LOGI(TAG, "System Statistics:\n%s", stats.c_str());
        
        // 打印当前状态
        ESP_LOGI(TAG, "Current State: %s", 
                 system_controller::SystemStateToString(system_controller.GetSystemState()).c_str());
        ESP_LOGI(TAG, "Current Mode: %s", 
                 system_controller::InteractionModeToString(system_controller.GetInteractionMode()).c_str());
    }
}

/**
 * @brief 启动AI玩具演示
 */
bool StartAIToyDemo() {
    ESP_LOGI(TAG, "Starting AI Toy Demo...");
    
    // 获取系统控制器实例
    auto& system_controller = system_controller::MainSystemController::GetInstance();
    
    // 设置演示回调
    auto demo_callback = std::make_shared<DemoCallback>();
    system_controller.SetCallback(demo_callback);
    
    // 创建演示任务
    BaseType_t ret;
    
    // 表情演示任务
    ret = xTaskCreate(ExpressionDemoTask, "expr_demo", 4096, nullptr, 2, nullptr);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create expression demo task");
        return false;
    }
    
    // 语音交互演示任务
    ret = xTaskCreate(VoiceInteractionDemoTask, "voice_demo", 4096, nullptr, 2, nullptr);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create voice interaction demo task");
        return false;
    }
    
    // 运动控制演示任务
    ret = xTaskCreate(MotionDemoTask, "motion_demo", 4096, nullptr, 2, nullptr);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create motion demo task");
        return false;
    }
    
    // 传感器监控演示任务
    ret = xTaskCreate(SensorMonitorDemoTask, "sensor_demo", 4096, nullptr, 2, nullptr);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create sensor monitor demo task");
        return false;
    }
    
    // 系统状态监控任务
    ret = xTaskCreate(SystemStatusDemoTask, "status_demo", 4096, nullptr, 1, nullptr);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create system status demo task");
        return false;
    }
    
    ESP_LOGI(TAG, "AI Toy Demo started successfully");
    return true;
}

/**
 * @brief 停止AI玩具演示
 */
void StopAIToyDemo() {
    ESP_LOGI(TAG, "Stopping AI Toy Demo...");
    
    // 这里可以添加停止演示的逻辑
    // 由于任务是无限循环，实际项目中可能需要使用任务通知或标志位来优雅停止
    
    ESP_LOGI(TAG, "AI Toy Demo stopped");
}

/**
 * @brief 演示特定功能
 */
void DemonstrateFeature(const std::string& feature_name) {
    ESP_LOGI(TAG, "Demonstrating feature: %s", feature_name.c_str());
    
    auto& system_controller = system_controller::MainSystemController::GetInstance();
    auto& expr_manager = expression_system::ExpressionManager::GetInstance();
    
    if (feature_name == "happy_expression") {
        expr_manager.SetExpression(expression_system::EmotionType::HAPPY);
        vTaskDelay(pdMS_TO_TICKS(2000));
        expr_manager.Blink(expression_system::EyePosition::BOTH, 3, 150);
        
    } else if (feature_name == "follow_mode") {
        system_controller.StartFollowMode();
        expr_manager.FollowTarget(150, 100);
        vTaskDelay(pdMS_TO_TICKS(5000));
        system_controller.StopFollowMode();
        
    } else if (feature_name == "voice_interaction") {
        system_controller.ProcessVoiceCommand("你好，小智");
        vTaskDelay(pdMS_TO_TICKS(1000));
        system_controller.ProcessVoiceCommand("挥挥手");
        vTaskDelay(pdMS_TO_TICKS(2000));
        system_controller.ProcessVoiceCommand("再见");
        
    } else if (feature_name == "emergency_stop") {
        ESP_LOGW(TAG, "Testing emergency stop...");
        system_controller.EmergencyStop();
        vTaskDelay(pdMS_TO_TICKS(1000));
        
    } else {
        ESP_LOGW(TAG, "Unknown feature: %s", feature_name.c_str());
    }
    
    ESP_LOGI(TAG, "Feature demonstration completed: %s", feature_name.c_str());
}

} // namespace ai_toy_demo
