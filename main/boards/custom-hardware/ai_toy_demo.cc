/**
 * @file ai_toy_demo.cc
 * @brief AI玩具系统演示程序
 * 
 * 展示AI玩具的完整功能，包括语音交互、表情控制、运动控制等
 */

#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include "system_controller.h"
#include "motion_system/voice_servo_integration.h"
#include "expression_system/expression_integration.h"

static const char* TAG = "AIToyDemo";

namespace ai_toy_demo {

/**
 * @brief AI玩具演示类
 */
class AIToyDemo {
public:
    AIToyDemo() : initialized_(false) {}
    
    /**
     * @brief 初始化演示系统
     */
    bool Initialize() {
        if (initialized_) {
            ESP_LOGW(TAG, "演示系统已初始化");
            return true;
        }
        
        ESP_LOGI(TAG, "初始化AI玩具演示系统...");
        
        // 获取系统控制器
        system_controller_ = &system_controller::MainSystemController::GetInstance();
        
        if (!system_controller_->Initialize()) {
            ESP_LOGE(TAG, "系统控制器初始化失败");
            return false;
        }
        
        initialized_ = true;
        ESP_LOGI(TAG, "AI玩具演示系统初始化成功");
        
        return true;
    }
    
    /**
     * @brief 运行基础功能演示
     */
    void RunBasicDemo() {
        if (!initialized_) {
            ESP_LOGE(TAG, "演示系统未初始化");
            return;
        }
        
        ESP_LOGI(TAG, "=== 开始基础功能演示 ===");
        
        // 启动系统
        if (!system_controller_->Start()) {
            ESP_LOGE(TAG, "系统启动失败");
            return;
        }
        
        // 等待系统稳定
        vTaskDelay(pdMS_TO_TICKS(2000));
        
        // 1. 问候演示
        ESP_LOGI(TAG, "1. 问候功能演示");
        system_controller_->ProcessVoiceCommand("你好");
        vTaskDelay(pdMS_TO_TICKS(3000));
        
        // 2. 表情演示
        ESP_LOGI(TAG, "2. 表情系统演示");
        DemonstrateExpressions();
        
        // 3. 动作演示
        ESP_LOGI(TAG, "3. 动作系统演示");
        DemonstrateMotions();
        
        // 4. 语音交互演示
        ESP_LOGI(TAG, "4. 语音交互演示");
        DemonstrateVoiceInteraction();
        
        ESP_LOGI(TAG, "=== 基础功能演示完成 ===");
    }
    
    /**
     * @brief 运行高级功能演示
     */
    void RunAdvancedDemo() {
        if (!initialized_) {
            ESP_LOGE(TAG, "演示系统未初始化");
            return;
        }
        
        ESP_LOGI(TAG, "=== 开始高级功能演示 ===");
        
        // 1. 复合指令演示
        ESP_LOGI(TAG, "1. 复合指令演示");
        DemonstrateComplexCommands();
        
        // 2. 情感交互演示
        ESP_LOGI(TAG, "2. 情感交互演示");
        DemonstrateEmotionalInteraction();
        
        // 3. 连续对话演示
        ESP_LOGI(TAG, "3. 连续对话演示");
        DemonstrateContinuousConversation();
        
        ESP_LOGI(TAG, "=== 高级功能演示完成 ===");
    }
    
    /**
     * @brief 获取系统状态
     */
    void ShowSystemStatus() {
        if (!system_controller_) {
            ESP_LOGE(TAG, "系统控制器未初始化");
            return;
        }
        
        auto status = system_controller_->GetSystemStatus();
        
        ESP_LOGI(TAG, "=== 系统状态 ===");
        ESP_LOGI(TAG, "运行状态: %s", status.is_running ? "运行中" : "已停止");
        ESP_LOGI(TAG, "运行时间: %llu ms", status.uptime_ms);
        ESP_LOGI(TAG, "系统健康度: %.2f", status.overall_health);
        ESP_LOGI(TAG, "表情系统: %s", status.expression_system_healthy ? "正常" : "异常");
        ESP_LOGI(TAG, "AI系统: %s", status.ai_system_healthy ? "正常" : "异常");
        ESP_LOGI(TAG, "运动系统: %s", status.motion_system_healthy ? "正常" : "异常");
        ESP_LOGI(TAG, "传感器系统: %s", status.sensor_system_healthy ? "正常" : "异常");
        ESP_LOGI(TAG, "================");
    }

private:
    bool initialized_;
    system_controller::MainSystemController* system_controller_;
    
    /**
     * @brief 演示表情功能
     */
    void DemonstrateExpressions() {
        const char* expressions[] = {"happy", "surprised", "love", "thinking", "excited"};
        const int num_expressions = sizeof(expressions) / sizeof(expressions[0]);
        
        for (int i = 0; i < num_expressions; i++) {
            ESP_LOGI(TAG, "设置表情: %s", expressions[i]);
            system_controller_->SetExpression(expressions[i]);
            vTaskDelay(pdMS_TO_TICKS(2000));
        }
        
        // 回到中性表情
        system_controller_->SetExpression("neutral");
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    
    /**
     * @brief 演示动作功能
     */
    void DemonstrateMotions() {
        const char* motions[] = {"挥手", "举手", "敬礼", "跳舞"};
        const int num_motions = sizeof(motions) / sizeof(motions[0]);
        
        for (int i = 0; i < num_motions; i++) {
            ESP_LOGI(TAG, "执行动作: %s", motions[i]);
            system_controller_->ProcessVoiceCommand(motions[i]);
            vTaskDelay(pdMS_TO_TICKS(4000));
        }
    }
    
    /**
     * @brief 演示语音交互
     */
    void DemonstrateVoiceInteraction() {
        const char* voice_commands[] = {
            "你好小智",
            "你今天怎么样",
            "给我跳个舞",
            "我很开心",
            "再见"
        };
        const int num_commands = sizeof(voice_commands) / sizeof(voice_commands[0]);
        
        for (int i = 0; i < num_commands; i++) {
            ESP_LOGI(TAG, "语音指令: %s", voice_commands[i]);
            system_controller_->ProcessVoiceCommand(voice_commands[i]);
            vTaskDelay(pdMS_TO_TICKS(3000));
        }
    }
    
    /**
     * @brief 演示复合指令
     */
    void DemonstrateComplexCommands() {
        const char* complex_commands[] = {
            "左手挥手三次",
            "快速举手",
            "慢慢放下手",
            "双手敬礼"
        };
        const int num_commands = sizeof(complex_commands) / sizeof(complex_commands[0]);
        
        for (int i = 0; i < num_commands; i++) {
            ESP_LOGI(TAG, "复合指令: %s", complex_commands[i]);
            system_controller_->ProcessVoiceCommand(complex_commands[i]);
            vTaskDelay(pdMS_TO_TICKS(5000));
        }
    }
    
    /**
     * @brief 演示情感交互
     */
    void DemonstrateEmotionalInteraction() {
        const struct {
            const char* input;
            const char* expected_emotion;
        } emotional_tests[] = {
            {"我今天很开心", "happy"},
            {"我有点难过", "sad"},
            {"这太令人惊讶了", "surprised"},
            {"我爱你", "love"},
            {"让我想想", "thinking"}
        };
        
        const int num_tests = sizeof(emotional_tests) / sizeof(emotional_tests[0]);
        
        for (int i = 0; i < num_tests; i++) {
            ESP_LOGI(TAG, "情感输入: %s", emotional_tests[i].input);
            system_controller_->ProcessVoiceCommand(emotional_tests[i].input);
            vTaskDelay(pdMS_TO_TICKS(3000));
            
            ESP_LOGI(TAG, "预期表情: %s", emotional_tests[i].expected_emotion);
            vTaskDelay(pdMS_TO_TICKS(2000));
        }
    }
    
    /**
     * @brief 演示连续对话
     */
    void DemonstrateContinuousConversation() {
        const char* conversation[] = {
            "你好小智",
            "你会做什么",
            "给我表演一个节目",
            "太棒了",
            "我们做朋友吧",
            "谢谢你",
            "再见"
        };
        
        const int num_turns = sizeof(conversation) / sizeof(conversation[0]);
        
        ESP_LOGI(TAG, "开始连续对话演示...");
        
        for (int i = 0; i < num_turns; i++) {
            ESP_LOGI(TAG, "对话轮次 %d: %s", i + 1, conversation[i]);
            system_controller_->ProcessVoiceCommand(conversation[i]);
            vTaskDelay(pdMS_TO_TICKS(4000));
        }
        
        ESP_LOGI(TAG, "连续对话演示完成");
    }
};

// 全局演示实例
static AIToyDemo* g_demo_instance = nullptr;

/**
 * @brief 初始化AI玩具演示
 */
bool InitializeDemo() {
    if (g_demo_instance) {
        ESP_LOGW(TAG, "演示已初始化");
        return true;
    }
    
    g_demo_instance = new AIToyDemo();
    return g_demo_instance->Initialize();
}

/**
 * @brief 运行完整演示
 */
void RunFullDemo() {
    if (!g_demo_instance) {
        ESP_LOGE(TAG, "演示未初始化");
        return;
    }
    
    ESP_LOGI(TAG, "开始完整AI玩具演示...");
    
    // 显示系统状态
    g_demo_instance->ShowSystemStatus();
    
    // 运行基础演示
    g_demo_instance->RunBasicDemo();
    
    // 等待一段时间
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    // 运行高级演示
    g_demo_instance->RunAdvancedDemo();
    
    // 最终状态显示
    g_demo_instance->ShowSystemStatus();
    
    ESP_LOGI(TAG, "完整AI玩具演示结束");
}

/**
 * @brief 运行基础演示
 */
void RunBasicDemo() {
    if (!g_demo_instance) {
        ESP_LOGE(TAG, "演示未初始化");
        return;
    }
    
    g_demo_instance->RunBasicDemo();
}

/**
 * @brief 运行高级演示
 */
void RunAdvancedDemo() {
    if (!g_demo_instance) {
        ESP_LOGE(TAG, "演示未初始化");
        return;
    }
    
    g_demo_instance->RunAdvancedDemo();
}

/**
 * @brief 显示系统状态
 */
void ShowSystemStatus() {
    if (!g_demo_instance) {
        ESP_LOGE(TAG, "演示未初始化");
        return;
    }
    
    g_demo_instance->ShowSystemStatus();
}

/**
 * @brief 启动AI玩具演示（兼容接口）
 */
bool StartAIToyDemo() {
    if (!InitializeDemo()) {
        ESP_LOGE(TAG, "演示初始化失败");
        return false;
    }

    // 创建演示任务
    BaseType_t ret = xTaskCreate([](void* param) {
        vTaskDelay(pdMS_TO_TICKS(5000));  // 等待系统完全启动
        RunFullDemo();
        vTaskDelete(nullptr);
    }, "ai_toy_demo_task", 4096, nullptr, 3, nullptr);

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建演示任务失败");
        return false;
    }

    ESP_LOGI(TAG, "AI玩具演示任务已启动");
    return true;
}

/**
 * @brief 清理演示资源
 */
void CleanupDemo() {
    if (g_demo_instance) {
        delete g_demo_instance;
        g_demo_instance = nullptr;
        ESP_LOGI(TAG, "演示资源已清理");
    }
}

} // namespace ai_toy_demo
