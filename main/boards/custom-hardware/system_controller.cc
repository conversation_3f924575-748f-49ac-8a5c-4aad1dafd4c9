/**
 * @file system_controller.cc
 * @brief 系统主控制器实现
 */

#include "system_controller.h"
#include <esp_log.h>
#include <esp_system.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

static const char* TAG = "SYS_CTRL";

namespace system_controller {

MainSystemController& MainSystemController::GetInstance() {
    static MainSystemController instance;
    return instance;
}

bool MainSystemController::Initialize(const SystemConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        ESP_LOGW(TAG, "System already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing AI Toy System...");
    
    config_ = config;
    current_state_ = SystemState::INITIALIZING;
    current_mode_ = config.default_mode;
    
    // 创建事件组和队列
    event_group_ = xEventGroupCreate();
    if (!event_group_) {
        ESP_LOGE(TAG, "Failed to create event group");
        return false;
    }
    
    event_queue_ = xQueueCreate(32, sizeof(SystemEvent));
    if (!event_queue_) {
        ESP_LOGE(TAG, "Failed to create event queue");
        vEventGroupDelete(event_group_);
        return false;
    }
    
    // 初始化各子系统
    bool init_success = true;
    
    // 1. 初始化硬件层
    if (!InitializeHardwareLayer()) {
        ESP_LOGE(TAG, "Failed to initialize hardware layer");
        init_success = false;
    }
    
    // 2. 初始化设备层
    if (init_success && !InitializeDeviceLayer()) {
        ESP_LOGE(TAG, "Failed to initialize device layer");
        init_success = false;
    }
    
    // 3. 初始化任务管理器
    if (init_success && !InitializeTaskManager()) {
        ESP_LOGE(TAG, "Failed to initialize task manager");
        init_success = false;
    }
    
    // 4. 初始化表情系统
    if (init_success && !InitializeExpressionSystem()) {
        ESP_LOGE(TAG, "Failed to initialize expression system");
        init_success = false;
    }
    
    // 5. 初始化运动系统
    if (init_success && !InitializeMotionSystem()) {
        ESP_LOGE(TAG, "Failed to initialize motion system");
        init_success = false;
    }
    
    // 6. 初始化传感器系统
    if (init_success && !InitializeSensorSystem()) {
        ESP_LOGE(TAG, "Failed to initialize sensor system");
        init_success = false;
    }
    
    // 7. 初始化AI系统
    if (init_success && !InitializeAISystem()) {
        ESP_LOGE(TAG, "Failed to initialize AI system");
        init_success = false;
    }
    
    if (!init_success) {
        ESP_LOGE(TAG, "System initialization failed");
        SetSystemState(SystemState::ERROR);
        return false;
    }
    
    initialized_ = true;
    running_ = false;
    last_interaction_time_ = xTaskGetTickCount();
    last_update_time_ = xTaskGetTickCount();
    
    SetSystemState(SystemState::READY);
    
    ESP_LOGI(TAG, "AI Toy System initialized successfully");
    return true;
}

bool MainSystemController::Start() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        ESP_LOGE(TAG, "System not initialized");
        return false;
    }
    
    if (running_) {
        ESP_LOGW(TAG, "System already running");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting AI Toy System...");
    
    // 创建主控制任务
    BaseType_t ret = xTaskCreate(
        MainControlTask,
        "main_control",
        8192,
        this,
        5,
        &main_task_
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create main control task");
        return false;
    }
    
    running_ = true;
    SetSystemState(SystemState::ACTIVE);
    
    ESP_LOGI(TAG, "AI Toy System started successfully");
    return true;
}

bool MainSystemController::Stop() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!running_) {
        ESP_LOGW(TAG, "System not running");
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping AI Toy System...");
    
    running_ = false;
    
    // 停止主控制任务
    if (main_task_) {
        vTaskDelete(main_task_);
        main_task_ = nullptr;
    }
    
    // 停止所有子系统
    // TODO: 实现子系统停止逻辑
    
    SetSystemState(SystemState::READY);
    
    ESP_LOGI(TAG, "AI Toy System stopped");
    return true;
}

bool MainSystemController::SetInteractionMode(InteractionMode mode) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (current_mode_ == mode) {
        return true;
    }
    
    InteractionMode old_mode = current_mode_;
    current_mode_ = mode;
    
    ESP_LOGI(TAG, "Interaction mode changed: %s -> %s", 
             InteractionModeToString(old_mode).c_str(),
             InteractionModeToString(mode).c_str());
    
    if (callback_) {
        callback_->OnInteractionModeChanged(old_mode, mode);
    }
    
    return true;
}

bool MainSystemController::ProcessVoiceCommand(const std::string& command) {
    ESP_LOGI(TAG, "Processing voice command: %s", command.c_str());
    
    // 更新最后交互时间
    last_interaction_time_ = xTaskGetTickCount();
    
    // 简单的命令解析示例
    if (command.find("跟着我") != std::string::npos || 
        command.find("follow") != std::string::npos) {
        return StartFollowMode();
    } else if (command.find("停止") != std::string::npos || 
               command.find("stop") != std::string::npos) {
        return StopFollowMode();
    } else if (command.find("挥手") != std::string::npos || 
               command.find("wave") != std::string::npos) {
        // TODO: 调用运动系统执行挥手动作
        ESP_LOGI(TAG, "Executing wave action");
        return true;
    } else if (command.find("开心") != std::string::npos || 
               command.find("happy") != std::string::npos) {
        // TODO: 调用表情系统显示开心表情
        ESP_LOGI(TAG, "Showing happy expression");
        return true;
    }
    
    ESP_LOGW(TAG, "Unknown voice command: %s", command.c_str());
    return false;
}

bool MainSystemController::StartFollowMode() {
    ESP_LOGI(TAG, "Starting follow mode");
    
    SetInteractionMode(InteractionMode::FOLLOW);
    SetSystemState(SystemState::FOLLOWING);
    
    // TODO: 启动人物跟随控制器
    // TODO: 设置跟随表情
    
    return true;
}

bool MainSystemController::StopFollowMode() {
    ESP_LOGI(TAG, "Stopping follow mode");
    
    // TODO: 停止人物跟随控制器
    // TODO: 恢复默认表情
    
    SetInteractionMode(InteractionMode::PASSIVE);
    SetSystemState(SystemState::ACTIVE);
    
    return true;
}

bool MainSystemController::EmergencyStop() {
    ESP_LOGI(TAG, "Emergency stop activated");
    
    // TODO: 停止所有运动
    // TODO: 显示警告表情
    
    SetSystemState(SystemState::READY);
    
    return true;
}

SystemState MainSystemController::GetSystemState() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return current_state_;
}

InteractionMode MainSystemController::GetInteractionMode() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return current_mode_;
}

void MainSystemController::SetCallback(std::shared_ptr<SystemCallback> callback) {
    callback_ = callback;
}

void MainSystemController::Update() {
    uint32_t current_time = xTaskGetTickCount();
    
    // 检查交互超时
    if (current_mode_ != InteractionMode::SLEEP) {
        uint32_t time_since_interaction = current_time - last_interaction_time_;
        if (time_since_interaction > pdMS_TO_TICKS(config_.interaction_timeout)) {
            CheckInteractionTimeout();
        }
    }
    
    // 更新表情系统
    // TODO: 调用表情管理器更新
    
    // 处理事件队列
    SystemEvent event;
    while (xQueueReceive(event_queue_, &event, 0) == pdTRUE) {
        if (callback_) {
            callback_->OnSystemEvent(event);
        }
    }
    
    last_update_time_ = current_time;
}

// 私有方法实现
void MainSystemController::SetSystemState(SystemState new_state) {
    if (current_state_ == new_state) {
        return;
    }
    
    SystemState old_state = current_state_;
    current_state_ = new_state;
    
    ESP_LOGI(TAG, "System state changed: %s -> %s", 
             SystemStateToString(old_state).c_str(),
             SystemStateToString(new_state).c_str());
    
    if (callback_) {
        callback_->OnSystemStateChanged(old_state, new_state);
    }
    
    HandleStateTransition(old_state, new_state);
}

void MainSystemController::HandleStateTransition(SystemState old_state, SystemState new_state) {
    // 处理状态转换逻辑
    switch (new_state) {
        case SystemState::ACTIVE:
            // 激活所有传感器
            break;
        case SystemState::FOLLOWING:
            // 启动跟随相关传感器
            break;
        case SystemState::SLEEPING:
            // 关闭非必要传感器
            break;
        case SystemState::ERROR:
            // 执行错误处理
            break;
        default:
            break;
    }
}

void MainSystemController::CheckInteractionTimeout() {
    ESP_LOGI(TAG, "Interaction timeout detected");
    
    if (current_mode_ != InteractionMode::SLEEP) {
        SetInteractionMode(InteractionMode::SLEEP);
        SetSystemState(SystemState::SLEEPING);
    }
}

// 子系统初始化方法（简化实现）
bool MainSystemController::InitializeHardwareLayer() {
    ESP_LOGI(TAG, "Initializing hardware layer...");
    
    // TODO: 暂时注释掉，解决链接问题
    /*
    // 初始化MCP通信管理器
    auto& mcp_manager = hardware_layer::MCPCommunicationManager::GetInstance();
    if (!mcp_manager.Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize MCP communication manager");
        return false;
    }
    */
    
    ESP_LOGI(TAG, "Hardware layer initialized");
    return true;
}

bool MainSystemController::InitializeDeviceLayer() {
    ESP_LOGI(TAG, "Initializing device layer...");
    
    // TODO: 暂时注释掉，解决链接问题
    /*
    // 初始化设备管理器
    auto& device_manager = device_layer::DeviceManager::GetInstance();
    if (!device_manager.Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize device manager");
        return false;
    }
    */
    
    ESP_LOGI(TAG, "Device layer initialized");
    return true;
}

bool MainSystemController::InitializeTaskManager() {
    ESP_LOGI(TAG, "Initializing task manager...");
    
    // TODO: 暂时注释掉，解决链接问题
    /*
    // 初始化任务管理器
    auto& task_manager = core_layer::TaskManager::GetInstance();
    if (!task_manager.Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize task manager");
        return false;
    }
    */
    
    ESP_LOGI(TAG, "Task manager initialized");
    return true;
}

bool MainSystemController::InitializeExpressionSystem() {
    ESP_LOGI(TAG, "Initializing expression system...");
    
    // TODO: 初始化表情管理器
    ESP_LOGI(TAG, "Expression system initialized");
    return true;
}

bool MainSystemController::InitializeMotionSystem() {
    ESP_LOGI(TAG, "Initializing motion system...");
    
    // TODO: 初始化运动控制器
    ESP_LOGI(TAG, "Motion system initialized");
    return true;
}

bool MainSystemController::InitializeSensorSystem() {
    ESP_LOGI(TAG, "Initializing sensor system...");
    
    // TODO: 初始化传感器管理器
    ESP_LOGI(TAG, "Sensor system initialized");
    return true;
}

bool MainSystemController::InitializeAISystem() {
    ESP_LOGI(TAG, "Initializing AI system...");
    
    // TODO: 初始化小智AI
    ESP_LOGI(TAG, "AI system initialized");
    return true;
}

// 主控制任务
void MainSystemController::MainControlTask(void* parameter) {
    MainSystemController* controller = static_cast<MainSystemController*>(parameter);
    
    ESP_LOGI(TAG, "Main control task started");
    
    while (controller->running_) {
        controller->Update();
        vTaskDelay(pdMS_TO_TICKS(50)); // 20Hz更新频率
    }
    
    ESP_LOGI(TAG, "Main control task stopped");
    vTaskDelete(nullptr);
}

// 回调接口实现（简化版本）
void MainSystemController::OnVoiceRecognized(const ai_layer::VoiceRecognitionResult& result) {
    ESP_LOGI(TAG, "Voice recognized: %s (confidence: %.2f)", 
             result.text.c_str(), result.confidence);
    
    if (result.is_valid && result.confidence > 0.7f) {
        ProcessVoiceCommand(result.text);
    }
}

void MainSystemController::OnAIResponse(const ai_layer::AIResponse& response) {
    ESP_LOGI(TAG, "AI response: %s", response.text_response.c_str());
    
    // TODO: 处理AI响应
}

// 其他回调接口的空实现
void MainSystemController::OnIntentRecognized(const ai_layer::IntentResult& intent) {}
void MainSystemController::OnError(const std::string& error_message) {}
void MainSystemController::OnTaskExecute() {}
void MainSystemController::OnMessageReceived(const core_layer::TaskMessage& message) {}
void MainSystemController::OnTaskStateChanged(core_layer::TaskState old_state, core_layer::TaskState new_state) {}
void MainSystemController::OnTaskError(const std::string& error_message) {}
void MainSystemController::OnDeviceStateChanged(const std::string& device_id, device_layer::DeviceState old_state, device_layer::DeviceState new_state) {}
void MainSystemController::OnDeviceDataReceived(const std::string& device_id, const std::string& data) {}
void MainSystemController::OnDeviceError(const std::string& device_id, const std::string& error_message) {}
void MainSystemController::OnGPIOStateChanged(uint8_t pin, hardware_layer::GPIOState state) {}
void MainSystemController::OnInterrupt(uint8_t pin) {}
void MainSystemController::OnCommunicationError(const std::string& error_message) {}
void MainSystemController::OnDeviceConnectionChanged(uint8_t address, bool connected) {}
void MainSystemController::OnExpressionChanged(expression_system::EmotionType old_emotion, expression_system::EmotionType new_emotion) {}
void MainSystemController::OnAnimationStarted(expression_system::AnimationType animation, expression_system::EyePosition eye) {}
void MainSystemController::OnAnimationFinished(expression_system::AnimationType animation, expression_system::EyePosition eye) {}
void MainSystemController::OnRenderCompleted(expression_system::EyePosition eye) {}
void MainSystemController::OnServoActionCompleted(const std::string& servo_id, motion_system::ServoAction action) {}
void MainSystemController::OnMotorMotionCompleted(const std::string& motor_id, motion_system::MotorDirection direction) {}
void MainSystemController::OnFollowStateChanged(motion_system::FollowState old_state, motion_system::FollowState new_state) {}
void MainSystemController::OnTargetDetected(const motion_system::TargetInfo& target) {}
void MainSystemController::OnTargetLost() {}
void MainSystemController::OnObstacleDetected(float distance, float angle) {}
void MainSystemController::OnMotionError(const std::string& error_message) {}
void MainSystemController::OnSensorDataUpdated(const sensor_system::SensorData& data) {}
void MainSystemController::OnObstacleDetected(const sensor_system::SensorData& data) {}
void MainSystemController::OnCliffDetected(const sensor_system::SensorData& data) {}
void MainSystemController::OnPersonDetected(const sensor_system::SensorData& data) {}
void MainSystemController::OnPersonLost(const std::string& sensor_id) {}
void MainSystemController::OnSensorStateChanged(const std::string& sensor_id, sensor_system::SensorState old_state, sensor_system::SensorState new_state) {}
void MainSystemController::OnSensorError(const std::string& sensor_id, const std::string& error_message) {}

// 添加缺失的函数实现
bool MainSystemController::Restart() {
    ESP_LOGI(TAG, "Restarting AI Toy System...");

    if (Stop()) {
        vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒
        return Start();
    }

    return false;
}

bool MainSystemController::ProcessTextCommand(const std::string& command) {
    ESP_LOGI(TAG, "Processing text command: %s", command.c_str());

    // 更新最后交互时间
    last_interaction_time_ = xTaskGetTickCount();

    // 复用语音命令处理逻辑
    return ProcessVoiceCommand(command);
}

bool MainSystemController::EnterSleepMode() {
    ESP_LOGI(TAG, "Entering sleep mode");

    SetInteractionMode(InteractionMode::SLEEP);
    SetSystemState(SystemState::SLEEPING);

    // TODO: 关闭非必要传感器和功能

    return true;
}

bool MainSystemController::WakeUp() {
    ESP_LOGI(TAG, "Waking up system");

    SetInteractionMode(InteractionMode::PASSIVE);
    SetSystemState(SystemState::ACTIVE);

    // 更新最后交互时间
    last_interaction_time_ = xTaskGetTickCount();

    return true;
}

std::string MainSystemController::GetSystemStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);

    char stats[512];
    snprintf(stats, sizeof(stats),
             "System Statistics:\n"
             "- State: %s\n"
             "- Mode: %s\n"
             "- Uptime: %lu ms\n"
             "- Last Interaction: %lu ms ago\n"
             "- Free Heap: %lu bytes\n",
             SystemStateToString(current_state_).c_str(),
             InteractionModeToString(current_mode_).c_str(),
             xTaskGetTickCount() * portTICK_PERIOD_MS,
             (xTaskGetTickCount() - last_interaction_time_) * portTICK_PERIOD_MS,
             esp_get_free_heap_size());

    return std::string(stats);
}

void MainSystemController::ProcessAICommand(const ai_layer::AIResponse& response) {
    ESP_LOGI(TAG, "Processing AI command: %s", response.text_response.c_str());

    // 解析硬件控制指令
    for (const auto& command : response.hardware_commands) {
        if (command.find("motion:") == 0) {
            std::map<std::string, std::string> params;
            ExecuteMotionCommand(command.substr(7), params);
        } else if (command.find("expression:") == 0) {
            std::map<std::string, std::string> params;
            ExecuteExpressionCommand(command.substr(11), params);
        }
    }
}

void MainSystemController::ExecuteMotionCommand(const std::string& command,
                                               const std::map<std::string, std::string>& params) {
    ESP_LOGI(TAG, "Executing motion command: %s", command.c_str());

    // TODO: 调用运动控制器执行命令
    if (command == "wave") {
        ESP_LOGI(TAG, "Executing wave motion");
    } else if (command == "stop") {
        ESP_LOGI(TAG, "Stopping all motion");
    }
}

void MainSystemController::ExecuteExpressionCommand(const std::string& command,
                                                   const std::map<std::string, std::string>& params) {
    ESP_LOGI(TAG, "Executing expression command: %s", command.c_str());

    // TODO: 调用表情管理器执行命令
    if (command == "happy") {
        ESP_LOGI(TAG, "Showing happy expression");
    } else if (command == "sad") {
        ESP_LOGI(TAG, "Showing sad expression");
    }
}

bool MainSystemController::PerformSafetyCheck() {
    // TODO: 实现安全检查逻辑
    return true;
}

void MainSystemController::HandleEmergencyStop() {
    ESP_LOGI(TAG, "Handling emergency stop");

    // TODO: 停止所有运动
    // TODO: 显示警告表情

    SetSystemState(SystemState::READY);
}

void MainSystemController::HandleObstacleAvoidance(const sensor_system::SensorData& obstacle_data) {
    ESP_LOGI(TAG, "Handling obstacle avoidance - distance: %.2f", obstacle_data.distance);

    // TODO: 实现避障逻辑
}

void MainSystemController::HandleCliffAvoidance(const sensor_system::SensorData& cliff_data) {
    ESP_LOGI(TAG, "Handling cliff avoidance");

    // TODO: 实现避险逻辑
}

void MainSystemController::UpdateFollowLogic() {
    // TODO: 更新跟随逻辑
}

void MainSystemController::ProcessPersonTracking(const sensor_system::SensorData& person_data) {
    ESP_LOGI(TAG, "Processing person tracking - distance: %.2f, angle: %.2f",
             person_data.distance, person_data.angle);

    // TODO: 处理人物跟踪逻辑
}

void MainSystemController::UpdateInteractionLogic() {
    // TODO: 更新交互逻辑
}

void MainSystemController::HandleUserInteraction(const std::string& interaction_type) {
    ESP_LOGI(TAG, "Handling user interaction: %s", interaction_type.c_str());

    last_interaction_time_ = xTaskGetTickCount();

    if (callback_) {
        callback_->OnUserInteraction(interaction_type, "");
    }
}

void MainSystemController::MonitorSystemHealth() {
    // TODO: 监控系统健康状态
}

void MainSystemController::LogSystemEvent(const SystemEvent& event) {
    ESP_LOGI(TAG, "System event: %s from %s", event.event_type.c_str(), event.source.c_str());

    // 将事件放入队列
    if (event_queue_) {
        xQueueSend(event_queue_, &event, 0);
    }
}

void MainSystemController::SystemMonitorTask(void* parameter) {
    MainSystemController* controller = static_cast<MainSystemController*>(parameter);

    ESP_LOGI(TAG, "System monitor task started");

    while (controller->running_) {
        controller->MonitorSystemHealth();
        vTaskDelay(pdMS_TO_TICKS(5000)); // 5秒检查一次
    }

    ESP_LOGI(TAG, "System monitor task stopped");
    vTaskDelete(nullptr);
}

void MainSystemController::EventProcessorTask(void* parameter) {
    MainSystemController* controller = static_cast<MainSystemController*>(parameter);

    ESP_LOGI(TAG, "Event processor task started");

    SystemEvent event;
    while (controller->running_) {
        if (xQueueReceive(controller->event_queue_, &event, pdMS_TO_TICKS(1000)) == pdTRUE) {
            if (controller->callback_) {
                controller->callback_->OnSystemEvent(event);
            }
        }
    }

    ESP_LOGI(TAG, "Event processor task stopped");
    vTaskDelete(nullptr);
}

// 辅助函数实现
std::string SystemStateToString(SystemState state) {
    switch (state) {
        case SystemState::UNINITIALIZED: return "UNINITIALIZED";
        case SystemState::INITIALIZING: return "INITIALIZING";
        case SystemState::READY: return "READY";
        case SystemState::ACTIVE: return "ACTIVE";
        case SystemState::FOLLOWING: return "FOLLOWING";
        case SystemState::INTERACTING: return "INTERACTING";
        case SystemState::SLEEPING: return "SLEEPING";
        case SystemState::ERROR: return "ERROR";
        case SystemState::SHUTDOWN: return "SHUTDOWN";
        default: return "UNKNOWN";
    }
}

std::string InteractionModeToString(InteractionMode mode) {
    switch (mode) {
        case InteractionMode::PASSIVE: return "PASSIVE";
        case InteractionMode::ACTIVE: return "ACTIVE";
        case InteractionMode::FOLLOW: return "FOLLOW";
        case InteractionMode::PLAY: return "PLAY";
        case InteractionMode::LEARN: return "LEARN";
        case InteractionMode::SLEEP: return "SLEEP";
        default: return "UNKNOWN";
    }
}

InteractionMode StringToInteractionMode(const std::string& mode_str) {
    if (mode_str == "PASSIVE") return InteractionMode::PASSIVE;
    if (mode_str == "ACTIVE") return InteractionMode::ACTIVE;
    if (mode_str == "FOLLOW") return InteractionMode::FOLLOW;
    if (mode_str == "PLAY") return InteractionMode::PLAY;
    if (mode_str == "LEARN") return InteractionMode::LEARN;
    if (mode_str == "SLEEP") return InteractionMode::SLEEP;
    return InteractionMode::PASSIVE;
}

SystemConfig CreateDefaultSystemConfig() {
    SystemConfig config;

    // AI配置
    config.ai_model_path = "/spiffs/ai_model.bin";
    config.ai_config_path = "/spiffs/ai_config.json";

    // 显示配置
    config.expression_resource_path = "/spiffs/expressions/";
    config.default_brightness = 80;

    // 交互配置
    config.default_mode = InteractionMode::PASSIVE;
    config.interaction_timeout = 30000; // 30秒
    config.sleep_timeout = 300000;      // 5分钟

    // 跟随配置
    config.follow_min_distance = 1.0f;
    config.follow_max_distance = 3.0f;
    config.follow_speed = 50;

    // 安全配置
    config.safe_distance = 0.3f;
    config.enable_cliff_detection = true;
    config.enable_obstacle_avoidance = true;

    return config;
}

bool LoadSystemConfig(const std::string& config_path, SystemConfig& config) {
    // TODO: 实现配置文件加载
    ESP_LOGI(TAG, "Loading system config from: %s", config_path.c_str());
    config = CreateDefaultSystemConfig();
    return true;
}

bool SaveSystemConfig(const std::string& config_path, const SystemConfig& config) {
    // TODO: 实现配置文件保存
    ESP_LOGI(TAG, "Saving system config to: %s", config_path.c_str());
    return true;
}

SystemStatus MainSystemController::GetSystemStatus() const {
    std::lock_guard<std::mutex> lock(mutex_);

    SystemStatus status;
    status.is_running = (current_state_ == SystemState::ACTIVE ||
                        current_state_ == SystemState::FOLLOWING ||
                        current_state_ == SystemState::INTERACTING);
    status.uptime_ms = esp_timer_get_time() / 1000;

    // 简化的健康状态检查
    status.expression_system_healthy = initialized_;
    status.ai_system_healthy = initialized_;
    status.motion_system_healthy = initialized_;
    status.sensor_system_healthy = initialized_;

    // 计算总体健康度
    int healthy_systems = 0;
    if (status.expression_system_healthy) healthy_systems++;
    if (status.ai_system_healthy) healthy_systems++;
    if (status.motion_system_healthy) healthy_systems++;
    if (status.sensor_system_healthy) healthy_systems++;

    status.overall_health = static_cast<float>(healthy_systems) / 4.0f;

    return status;
}

bool MainSystemController::SetExpression(const std::string& expression) {
    std::lock_guard<std::mutex> lock(mutex_);

    if (!initialized_) {
        ESP_LOGE(TAG, "System not initialized");
        return false;
    }

    ESP_LOGI(TAG, "Setting expression: %s", expression.c_str());

    // 通过表情系统设置表情
    std::map<std::string, std::string> params;
    params["expression"] = expression;

    ExecuteExpressionCommand("set_expression", params);

    return true;
}

} // namespace system_controller
