#include "wifi_board.h"
#include "audio_codecs/no_audio_codec.h"
#include "display/lcd_display.h"
#include "system_reset.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "mcp_server.h"
#include "lamp_controller.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"
// Custom Hardware - 阶段1: 基础功能稳定
// 专注于双屏表情显示系统核心功能
#include "expression_system/simple_dual_screen.h"  // 简化双屏表情系统 (核心)
#include "expression_system/core_test.h"  // 核心功能测试
// 暂时禁用所有其他模块，专注于双屏表情系统:
// #include "motion_system/servo_180.h"
// #include "ai_layer/ai_interface.h"
// #include "system_controller.h"
// #include "motion_system/voice_servo_integration.h"
// #include "expression_system/expression_integration.h"
// #include "sensor_system/sensor_manager.h"
// #include "motion_system/motion_controller.h"

#include <wifi_station.h>
#include <esp_log.h>
#include <algorithm>
#include <cctype>
#include <driver/i2c_master.h>
#include <esp_lcd_panel_vendor.h>
#include <esp_lcd_panel_io.h>
#include <esp_lcd_panel_ops.h>
#include <driver/spi_common.h>

#if defined(LCD_TYPE_ILI9341_SERIAL)
#include "esp_lcd_ili9341.h"
#endif

#if defined(LCD_TYPE_GC9A01_SERIAL)
#include "esp_lcd_gc9a01.h"
static const gc9a01_lcd_init_cmd_t gc9107_lcd_init_cmds[] = {
    //  {cmd, { data }, data_size, delay_ms}
    {0xfe, (uint8_t[]){0x00}, 0, 0},
    {0xef, (uint8_t[]){0x00}, 0, 0},
    {0xb0, (uint8_t[]){0xc0}, 1, 0},
    {0xb1, (uint8_t[]){0x80}, 1, 0},
    {0xb2, (uint8_t[]){0x27}, 1, 0},
    {0xb3, (uint8_t[]){0x13}, 1, 0},
    {0xb6, (uint8_t[]){0x19}, 1, 0},
    {0xb7, (uint8_t[]){0x05}, 1, 0},
    {0xac, (uint8_t[]){0xc8}, 1, 0},
    {0xab, (uint8_t[]){0x0f}, 1, 0},
    {0x3a, (uint8_t[]){0x05}, 1, 0},
    {0xb4, (uint8_t[]){0x04}, 1, 0},
    {0xa8, (uint8_t[]){0x08}, 1, 0},
    {0xb8, (uint8_t[]){0x08}, 1, 0},
    {0xea, (uint8_t[]){0x02}, 1, 0},
    {0xe8, (uint8_t[]){0x2A}, 1, 0},
    {0xe9, (uint8_t[]){0x47}, 1, 0},
    {0xe7, (uint8_t[]){0x5f}, 1, 0},
    {0xc6, (uint8_t[]){0x21}, 1, 0},
    {0xc7, (uint8_t[]){0x15}, 1, 0},
    {0xf0,
    (uint8_t[]){0x1D, 0x38, 0x09, 0x4D, 0x92, 0x2F, 0x35, 0x52, 0x1E, 0x0C,
                0x04, 0x12, 0x14, 0x1f},
    14, 0},
    {0xf1,
    (uint8_t[]){0x16, 0x40, 0x1C, 0x54, 0xA9, 0x2D, 0x2E, 0x56, 0x10, 0x0D,
                0x0C, 0x1A, 0x14, 0x1E},
    14, 0},
    {0xf4, (uint8_t[]){0x00, 0x00, 0xFF}, 3, 0},
    {0xba, (uint8_t[]){0xFF, 0xFF}, 2, 0},
};
#endif
 
#ifndef TAG
#define TAG "CompactWifiBoardLCD"
#endif

LV_FONT_DECLARE(font_puhui_16_4);
LV_FONT_DECLARE(font_awesome_16_4);

class CompactWifiBoardLCD : public WifiBoard {
private:

    Button boot_button_;
    LcdDisplay* display_;
    esp_lcd_panel_handle_t panel;  // 添加panel句柄

    // 阶段1: 仅保留双屏表情系统
    std::unique_ptr<simple_dual_screen::SimpleDualScreenManager> simple_dual_screen_manager_;

    // 简化状态标志
    bool dual_screen_initialized_ = false;

    void InitializeSpi() {
        spi_bus_config_t buscfg = {};
        buscfg.mosi_io_num = DISPLAY_MOSI_PIN;
        buscfg.miso_io_num = GPIO_NUM_NC;
        buscfg.sclk_io_num = DISPLAY_CLK_PIN;
        buscfg.quadwp_io_num = GPIO_NUM_NC;
        buscfg.quadhd_io_num = GPIO_NUM_NC;
        buscfg.max_transfer_sz = DISPLAY_WIDTH * DISPLAY_HEIGHT * sizeof(uint16_t);
        ESP_ERROR_CHECK(spi_bus_initialize(SPI3_HOST, &buscfg, SPI_DMA_CH_AUTO));
    }

    void InitializeLcdDisplay() {
        ESP_LOGI(TAG, "Skipping single LCD display initialization - using dual screen expression system");
        ESP_LOGI(TAG, "Dual Screen Configuration:");
        ESP_LOGI(TAG, "  Shared Pins: MOSI=%d, CLK=%d, DC=%d, RST=%d",
                 DISPLAY_MOSI_PIN, DISPLAY_CLK_PIN, DISPLAY_DC_PIN, DISPLAY_RST_PIN);
        ESP_LOGI(TAG, "  CS Pins: LEFT=%d, RIGHT=%d", DISPLAY_CS_LEFT_PIN, DISPLAY_CS_RIGHT_PIN);
        ESP_LOGI(TAG, "  Display Size: %dx%d each, Type: Dual GC9A01", DISPLAY_WIDTH, DISPLAY_HEIGHT);

        // 不再初始化单屏显示，由双屏表情系统接管
        // 设置display_为nullptr，表示使用双屏表情系统
        display_ = nullptr;
    }

    // 测试显示功能
    void TestDisplayFunctionality() {
        ESP_LOGI(TAG, "Testing display functionality immediately...");

        // 强制设置背光
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            auto* backlight = GetBacklight();
            if (backlight) {
                ESP_LOGI(TAG, "Setting backlight to 100%%");
                backlight->SetBrightness(100, false);
                vTaskDelay(pdMS_TO_TICKS(100));
            }
        }

        if (display_) {
            ESP_LOGI(TAG, "Drawing test pattern to screen");

            // 使用简化的显示测试，避免Lock/Unlock访问权限问题
            ESP_LOGI(TAG, "Display test: Setting emotion and icon");
            display_->SetEmotion("happy");
            display_->SetIcon("robot");
            ESP_LOGI(TAG, "Display test: Setting welcome message");
            display_->SetChatMessage("system", "Display Test OK");
            ESP_LOGI(TAG, "Test pattern completed successfully");
        } else {
            ESP_LOGW(TAG, "Display not available for testing");
        }
    }

    // 强制唤醒显示屏
    void ForceDisplayWakeup() {
        ESP_LOGI(TAG, "Force display wakeup sequence...");

        if (panel) {
            // 强制发送显示开启命令
            ESP_LOGI(TAG, "Sending display ON command");
            esp_lcd_panel_disp_on_off(panel, true);
            vTaskDelay(pdMS_TO_TICKS(50));

            // 再次设置显示参数
            ESP_LOGI(TAG, "Re-configuring display parameters");
            esp_lcd_panel_invert_color(panel, DISPLAY_INVERT_COLOR);
            vTaskDelay(pdMS_TO_TICKS(50));

            // 强制刷新显示 - 使用简化方法
            ESP_LOGI(TAG, "Force refresh display");
            if (display_) {
                display_->SetEmotion("happy");
                display_->SetChatMessage("system", "Screen Test");
                ESP_LOGI(TAG, "Screen test content set");
            }
        }

        // 强制设置背光到最大
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            ESP_LOGI(TAG, "Force backlight to maximum");
            auto* backlight = GetBacklight();
            if (backlight) {
                backlight->SetBrightness(100, false);
                vTaskDelay(pdMS_TO_TICKS(100));
            }
        }

        ESP_LOGI(TAG, "Force wakeup sequence completed");
    }



    void InitializeButtons() {
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ResetWifiConfiguration();
            }
            app.ToggleChatState();
        });
    }

    // 物联网初始化 (阶段1暂时禁用)
    void InitializeIot() {
        ESP_LOGI(TAG, "IoT initialization skipped in Phase 1");
        // 阶段1专注于双屏表情系统，暂时禁用IoT功能
        // #if CONFIG_IOT_PROTOCOL_XIAOZHI
        //     auto& thing_manager = iot::ThingManager::GetInstance();
        //     thing_manager.AddThing(iot::CreateThing("Speaker"));
        //     thing_manager.AddThing(iot::CreateThing("Screen"));
        //     thing_manager.AddThing(iot::CreateThing("Lamp"));
        // #elif CONFIG_IOT_PROTOCOL_MCP
        //     static LampController lamp(LAMP_GPIO);
        // #endif
    }

    // 舵机控制器初始化 (阶段1暂时禁用)
    void InitializeServoController() {
        ESP_LOGI(TAG, "Servo controller initialization skipped in Phase 1");
        // 阶段1专注于双屏表情系统，后续阶段再启用舵机功能
    }

    // 表情系统初始化 (阶段1暂时禁用，在InitializeDualScreenTest中处理)
    void InitializeExpressionSystem() {
        ESP_LOGI(TAG, "Expression system initialization moved to InitializeDualScreenTest in Phase 1");
        // 阶段1中，表情系统初始化已移至InitializeDualScreenTest方法中
    }
                ESP_LOGE(TAG, "Failed to initialize simple dual screen expression system");
                simple_dual_screen_manager_.reset();
            }
        } else {
            ESP_LOGE(TAG, "Failed to create simple dual screen expression manager");
        }
    }

    // 阶段1: 双屏表情系统测试
    void InitializeDualScreenTest() {
        ESP_LOGI(TAG, "=== 阶段1: 双屏表情系统初始化 ===");

        // 初始化双屏表情系统
        simple_dual_screen_manager_ = std::make_unique<simple_dual_screen::SimpleDualScreenManager>();

        if (simple_dual_screen_manager_->Initialize()) {
            ESP_LOGI(TAG, "✅ 双屏表情系统初始化成功");
            dual_screen_initialized_ = true;

            // 运行基础表情测试
            ESP_LOGI(TAG, "🎭 开始基础表情测试...");
            if (core_test::InitializeCoreTest()) {
                core_test::RunBasicTest();
                ESP_LOGI(TAG, "✅ 基础表情测试完成");
            }
        } else {
            ESP_LOGE(TAG, "❌ 双屏表情系统初始化失败");
        }
    }

public:
    CompactWifiBoardLCD()
        : boot_button_(BOOT_BUTTON_GPIO)
    {
        ESP_LOGI(TAG, "=== Custom Hardware 阶段1: 基础功能稳定 ===");

        // 阶段1: 仅初始化双屏表情系统必需的组件
        InitializeSpi();
        InitializeLcdDisplay();
        InitializeButtons();

        // 阶段1暂时跳过的初始化 (后续阶段启用)
        // InitializeIot();
        // InitializeServoController();
        // InitializeExpressionSystem();

        // 阶段1核心功能测试
        InitializeDualScreenTest();

        // 强制设置背光亮度
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            ESP_LOGI(TAG, "Initializing backlight...");
            auto* backlight = GetBacklight();
            if (backlight) {
                // 先设置高亮度确保屏幕可见
                backlight->SetBrightness(90, false);
                ESP_LOGI(TAG, "Backlight set to 90%%");
                vTaskDelay(pdMS_TO_TICKS(100));

                // 然后恢复保存的亮度
                backlight->RestoreBrightness();
                ESP_LOGI(TAG, "Backlight restored to saved brightness");
            } else {
                ESP_LOGW(TAG, "Backlight not available");
            }
        } else {
            ESP_LOGW(TAG, "Backlight pin not configured");
        }
    }

    virtual Led* GetLed() override {
        static SingleLed led(BUILTIN_LED_GPIO);
        return &led;
    }

    virtual AudioCodec* GetAudioCodec() override {
#ifdef AUDIO_I2S_METHOD_SIMPLEX
        static NoAudioCodecSimplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_SPK_GPIO_BCLK, AUDIO_I2S_SPK_GPIO_LRCK, AUDIO_I2S_SPK_GPIO_DOUT, AUDIO_I2S_MIC_GPIO_SCK, AUDIO_I2S_MIC_GPIO_WS, AUDIO_I2S_MIC_GPIO_DIN);
#else
        static NoAudioCodecDuplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN);
#endif
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        // 双屏表情系统模式下，返回nullptr
        // 表情显示由dual_screen_expression_manager_管理
        return nullptr;
    }

    virtual Backlight* GetBacklight() override {
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            static PwmBacklight backlight(DISPLAY_BACKLIGHT_PIN, DISPLAY_BACKLIGHT_OUTPUT_INVERT);
            return &backlight;
        }
        return nullptr;
    }

    // 处理语音控制命令
    bool ProcessVoiceCommand(const std::string& command) {
        ESP_LOGI(TAG, "Processing voice command: %s", command.c_str());

        if (!voice_servo_initialized_) {
            ESP_LOGW(TAG, "Voice servo controller not initialized");
            return false;
        }

        // 转换为小写以便匹配
        std::string lower_command = command;
        std::transform(lower_command.begin(), lower_command.end(), lower_command.begin(), ::tolower);

        // 检查是否包含舵机控制相关的关键词
        bool is_servo_command = false;
        if (lower_command.find("挥手") != std::string::npos ||
            lower_command.find("举手") != std::string::npos ||
            lower_command.find("敬礼") != std::string::npos ||
            lower_command.find("跳舞") != std::string::npos ||
            lower_command.find("停止") != std::string::npos ||
            lower_command.find("重置") != std::string::npos ||
            lower_command.find("wave") != std::string::npos ||
            lower_command.find("dance") != std::string::npos ||
            lower_command.find("stop") != std::string::npos ||
            lower_command.find("reset") != std::string::npos) {
            is_servo_command = true;
        }

        if (is_servo_command) {
            ESP_LOGI(TAG, "✅ Servo command detected: %s", command.c_str());

            // 执行舵机控制动作
            bool action_success = false;
            if (lower_command.find("挥手") != std::string::npos || lower_command.find("wave") != std::string::npos) {
                ESP_LOGI(TAG, "🤚 执行挥手动作");
                action_success = ExecuteServoAction("wave");
            } else if (lower_command.find("举手") != std::string::npos) {
                ESP_LOGI(TAG, "🙋 执行举手动作");
                action_success = ExecuteServoAction("raise_hand");
            } else if (lower_command.find("敬礼") != std::string::npos) {
                ESP_LOGI(TAG, "🫡 执行敬礼动作");
                action_success = ExecuteServoAction("salute");
            } else if (lower_command.find("跳舞") != std::string::npos || lower_command.find("dance") != std::string::npos) {
                ESP_LOGI(TAG, "💃 执行跳舞动作");
                action_success = ExecuteServoAction("dance");
            } else if (lower_command.find("停止") != std::string::npos || lower_command.find("stop") != std::string::npos) {
                ESP_LOGI(TAG, "⏹️ 停止所有动作");
                action_success = ExecuteServoAction("stop");
            } else if (lower_command.find("重置") != std::string::npos || lower_command.find("reset") != std::string::npos) {
                ESP_LOGI(TAG, "🔄 重置舵机位置");
                action_success = ExecuteServoAction("reset");
            }

            if (action_success) {
                ESP_LOGI(TAG, "✅ 动作执行成功");
            } else {
                ESP_LOGW(TAG, "⚠️ 动作执行失败");
            }

            return true; // 表示命令已被处理
        }

        return false; // 表示命令未被处理，可以继续传递给其他处理器
    }



    // 执行舵机动作
    bool ExecuteServoAction(const std::string& action) {
        if (!servo_initialized_) {
            ESP_LOGW(TAG, "Servo not initialized");
            return false;
        }

        ESP_LOGI(TAG, "Executing servo action: %s", action.c_str());

        // 模拟舵机动作执行
        if (action == "wave") {
            // 挥手动作：左右摆动
            ESP_LOGI(TAG, "Servo: Moving to wave position 1");
            vTaskDelay(pdMS_TO_TICKS(500));
            ESP_LOGI(TAG, "Servo: Moving to wave position 2");
            vTaskDelay(pdMS_TO_TICKS(500));
            ESP_LOGI(TAG, "Servo: Moving to wave position 3");
            vTaskDelay(pdMS_TO_TICKS(500));
            ESP_LOGI(TAG, "Servo: Wave action completed");
        } else if (action == "raise_hand") {
            // 举手动作：向上举起
            ESP_LOGI(TAG, "Servo: Raising hand to 90 degrees");
            vTaskDelay(pdMS_TO_TICKS(1000));
            ESP_LOGI(TAG, "Servo: Hand raised");
        } else if (action == "salute") {
            // 敬礼动作：标准敬礼姿势
            ESP_LOGI(TAG, "Servo: Moving to salute position");
            vTaskDelay(pdMS_TO_TICKS(800));
            ESP_LOGI(TAG, "Servo: Salute completed");
        } else if (action == "dance") {
            // 跳舞动作：连续动作
            ESP_LOGI(TAG, "Servo: Starting dance sequence");
            for (int i = 0; i < 3; i++) {
                ESP_LOGI(TAG, "Servo: Dance move %d", i + 1);
                vTaskDelay(pdMS_TO_TICKS(600));
            }
            ESP_LOGI(TAG, "Servo: Dance completed");
        } else if (action == "stop") {
            // 停止动作：保持当前位置
            ESP_LOGI(TAG, "Servo: Stopping all movements");
        } else if (action == "reset") {
            // 重置动作：回到初始位置
            ESP_LOGI(TAG, "Servo: Resetting to initial position (90 degrees)");
            vTaskDelay(pdMS_TO_TICKS(800));
            ESP_LOGI(TAG, "Servo: Reset completed");
        } else {
            ESP_LOGW(TAG, "Unknown servo action: %s", action.c_str());
            return false;
        }

        return true;
    }

    // 处理MCP指令
    bool ProcessMCPCommand(const std::string& tool_name, const std::string& parameters) {
        ESP_LOGI(TAG, "🔧 Processing MCP command: %s with params: %s", tool_name.c_str(), parameters.c_str());

        if (tool_name == "self.screen.set_brightness") {
            // 设置屏幕亮度
            int brightness = 75; // 默认亮度
            if (!parameters.empty()) {
                brightness = std::stoi(parameters);
                brightness = std::max(0, std::min(100, brightness)); // 限制范围0-100
            }

            auto* backlight = GetBacklight();
            if (backlight) {
                backlight->SetBrightness(brightness);
                ESP_LOGI(TAG, "💡 Screen brightness set to %d%%", brightness);
                return true;
            } else {
                ESP_LOGW(TAG, "⚠️ Backlight not available");
                return false;
            }
        } else if (tool_name == "self.screen.set_theme") {
            // 设置屏幕主题
            std::string theme = parameters.empty() ? "light" : parameters;

            auto* display = GetDisplay();
            if (display) {
                display->SetTheme(theme);
                ESP_LOGI(TAG, "🎨 Screen theme set to: %s", theme.c_str());
                return true;
            } else {
                ESP_LOGW(TAG, "⚠️ Display not available");
                return false;
            }
        } else if (tool_name == "self.audio_speaker.set_volume") {
            // 设置音量
            int volume = 50; // 默认音量
            if (!parameters.empty()) {
                volume = std::stoi(parameters);
                volume = std::max(0, std::min(100, volume)); // 限制范围0-100
            }

            ESP_LOGI(TAG, "🔊 Audio volume set to %d%% (simulated)", volume);
            return true;
        } else if (tool_name == "self.get_device_status") {
            // 获取设备状态
            ESP_LOGI(TAG, "📊 Device Status Report:");
            ESP_LOGI(TAG, "  - System: %s", IsSystemReady() ? "Ready" : "Not Ready");
            ESP_LOGI(TAG, "  - Servo: %s", servo_initialized_ ? "Initialized" : "Not Initialized");
            ESP_LOGI(TAG, "  - Voice Control: %s", voice_servo_initialized_ ? "Ready" : "Not Ready");
            ESP_LOGI(TAG, "  - Display: %s", GetDisplay() ? "Available" : "Not Available");
            ESP_LOGI(TAG, "  - Backlight: %s", GetBacklight() ? "Available" : "Not Available");
            return true;
        } else if (tool_name == "self.lamp.turn_on" || tool_name == "self.lamp.turn_off" || tool_name == "self.lamp.get_state") {
            // 灯光控制
            if (tool_name == "self.lamp.turn_on") {
                ESP_LOGI(TAG, "💡 Lamp turned ON (simulated)");
            } else if (tool_name == "self.lamp.turn_off") {
                ESP_LOGI(TAG, "💡 Lamp turned OFF (simulated)");
            } else {
                ESP_LOGI(TAG, "💡 Lamp state: ON (simulated)");
            }
            return true;
        } else {
            ESP_LOGW(TAG, "❌ Unknown MCP command: %s", tool_name.c_str());
            return false;
        }
    }

    // 启动系统监控
    void StartSystemMonitoring() {
        ESP_LOGI(TAG, "📊 Starting system monitoring...");

        // 创建监控任务
        xTaskCreate([](void* param) {
            auto* self = static_cast<CompactWifiBoardLCD*>(param);

            while (true) {
                // 每30秒输出一次系统状态
                vTaskDelay(pdMS_TO_TICKS(30000));

                ESP_LOGI(TAG, "📊 System Status Report:");
                ESP_LOGI(TAG, "  🤖 System Ready: %s", self->IsSystemReady() ? "✅ Yes" : "❌ No");
                ESP_LOGI(TAG, "  🎭 Dual Screen: %s", self->IsSystemReady() ? "✅ Ready" : "❌ Not Ready");
                ESP_LOGI(TAG, "  🖥️ Display: %s", self->GetDisplay() ? "✅ Available" : "❌ Not Available");
                ESP_LOGI(TAG, "  💡 Backlight: %s", self->GetBacklight() ? "✅ Available" : "❌ Not Available");

                // 显示内存使用情况
                size_t free_heap = esp_get_free_heap_size();
                size_t min_free_heap = esp_get_minimum_free_heap_size();
                ESP_LOGI(TAG, "  💾 Memory: Free=%zu KB, Min=%zu KB", free_heap/1024, min_free_heap/1024);
            }
        }, "system_monitor", 4096, nullptr, 1, nullptr);

        ESP_LOGI(TAG, "✅ System monitoring started");
    }

    // 检查GPIO引脚状态
    void CheckGPIOStatus() {
        ESP_LOGI(TAG, "Checking GPIO pin status:");

        // 检查双屏显示相关引脚
        ESP_LOGI(TAG, "  MOSI (GPIO%d): %s", DISPLAY_MOSI_PIN,
                 gpio_get_level((gpio_num_t)DISPLAY_MOSI_PIN) ? "HIGH" : "LOW");
        ESP_LOGI(TAG, "  CLK (GPIO%d): %s", DISPLAY_CLK_PIN,
                 gpio_get_level((gpio_num_t)DISPLAY_CLK_PIN) ? "HIGH" : "LOW");
        ESP_LOGI(TAG, "  DC (GPIO%d): %s", DISPLAY_DC_PIN,
                 gpio_get_level((gpio_num_t)DISPLAY_DC_PIN) ? "HIGH" : "LOW");
        ESP_LOGI(TAG, "  RST (GPIO%d): %s", DISPLAY_RST_PIN,
                 gpio_get_level((gpio_num_t)DISPLAY_RST_PIN) ? "HIGH" : "LOW");
        ESP_LOGI(TAG, "  CS_LEFT (GPIO%d): %s", DISPLAY_CS_LEFT_PIN,
                 gpio_get_level((gpio_num_t)DISPLAY_CS_LEFT_PIN) ? "HIGH" : "LOW");
        ESP_LOGI(TAG, "  CS_RIGHT (GPIO%d): %s", DISPLAY_CS_RIGHT_PIN,
                 gpio_get_level((gpio_num_t)DISPLAY_CS_RIGHT_PIN) ? "HIGH" : "LOW");
        ESP_LOGI(TAG, "  BL (GPIO%d): %s", DISPLAY_BACKLIGHT_PIN,
                 gpio_get_level((gpio_num_t)DISPLAY_BACKLIGHT_PIN) ? "HIGH" : "LOW");
    }

    // 获取系统状态
    bool IsSystemReady() {
        return dual_screen_initialized_;
    }

    // 处理语音命令 (阶段1简化实现)
    bool ProcessVoiceCommand(const std::string& command) {
        ESP_LOGI(TAG, "Voice command received: %s", command.c_str());

        if (simple_dual_screen_manager_) {
            // 简单的表情控制命令
            if (command.find("happy") != std::string::npos || command.find("开心") != std::string::npos) {
                simple_dual_screen_manager_->ShowExpression(simple_dual_screen::ExpressionType::HAPPY);
                return true;
            } else if (command.find("sad") != std::string::npos || command.find("伤心") != std::string::npos) {
                simple_dual_screen_manager_->ShowExpression(simple_dual_screen::ExpressionType::SAD);
                return true;
            } else if (command.find("angry") != std::string::npos || command.find("生气") != std::string::npos) {
                simple_dual_screen_manager_->ShowExpression(simple_dual_screen::ExpressionType::ANGRY);
                return true;
            } else if (command.find("blink") != std::string::npos || command.find("眨眼") != std::string::npos) {
                simple_dual_screen_manager_->BlinkEyes();
                return true;
            }
        }

        ESP_LOGW(TAG, "Voice command not recognized in Phase 1: %s", command.c_str());
        return false;
    }

    // 处理MCP命令 (阶段1简化实现)
    bool ProcessMCPCommand(const std::string& tool, const std::string& params) {
        ESP_LOGI(TAG, "MCP command received - Tool: %s, Params: %s", tool.c_str(), params.c_str());
        ESP_LOGW(TAG, "MCP commands not implemented in Phase 1");
        return false;
    }
};

DECLARE_BOARD(CompactWifiBoardLCD);

// 全局语音控制接口
extern "C" {
    // 处理语音控制命令的全局函数
    bool xiaozhi_process_voice_command(const char* command) {
        if (!command) {
            return false;
        }

        // 获取板子实例
        auto* board = static_cast<CompactWifiBoardLCD*>(&Board::GetInstance());
        if (!board) {
            ESP_LOGW(TAG, "Board instance not available");
            return false;
        }

        // 检查系统是否准备就绪
        if (!board->IsSystemReady()) {
            ESP_LOGW(TAG, "System not ready for voice commands");
            return false;
        }

        // 处理语音命令
        std::string cmd_str(command);
        return board->ProcessVoiceCommand(cmd_str);
    }

    // 检查系统是否准备就绪
    bool xiaozhi_is_system_ready() {
        auto* board = static_cast<CompactWifiBoardLCD*>(&Board::GetInstance());
        return board ? board->IsSystemReady() : false;
    }

    // 处理MCP指令的全局函数
    bool xiaozhi_process_mcp_command(const char* tool_name, const char* parameters) {
        if (!tool_name) {
            return false;
        }

        // 获取板子实例
        auto* board = static_cast<CompactWifiBoardLCD*>(&Board::GetInstance());
        if (!board) {
            ESP_LOGW(TAG, "Board instance not available for MCP command");
            return false;
        }

        // 检查系统是否准备就绪
        if (!board->IsSystemReady()) {
            ESP_LOGW(TAG, "System not ready for MCP commands");
            return false;
        }

        // 处理MCP命令
        std::string tool_str(tool_name);
        std::string params_str(parameters ? parameters : "");
        return board->ProcessMCPCommand(tool_str, params_str);
    }
}
