#include "wifi_board.h"
#include "audio_codecs/no_audio_codec.h"
#include "display/lcd_display.h"
#include "system_reset.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "mcp_server.h"
#include "lamp_controller.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"
// AI玩具系统完整集成
#include "system_controller.h"
#include "motion_system/servo_180.h"  // 180度舵机控制器
#include "motion_system/voice_servo_integration.h"  // 语音舵机集成控制器
#include "expression_system/expression_integration.h"  // 表情系统集成
#include "ai_layer/ai_interface.h"  // AI接口
#include "sensor_system/sensor_manager.h"  // 传感器管理器
#include "motion_system/motion_controller.h"  // 运动控制器
// AI玩具演示程序已移除

#include <wifi_station.h>
#include <esp_log.h>
#include <algorithm>
#include <cctype>
#include <driver/i2c_master.h>
#include <esp_lcd_panel_vendor.h>
#include <esp_lcd_panel_io.h>
#include <esp_lcd_panel_ops.h>
#include <driver/spi_common.h>

#if defined(LCD_TYPE_ILI9341_SERIAL)
#include "esp_lcd_ili9341.h"
#endif

#if defined(LCD_TYPE_GC9A01_SERIAL)
#include "esp_lcd_gc9a01.h"
static const gc9a01_lcd_init_cmd_t gc9107_lcd_init_cmds[] = {
    //  {cmd, { data }, data_size, delay_ms}
    {0xfe, (uint8_t[]){0x00}, 0, 0},
    {0xef, (uint8_t[]){0x00}, 0, 0},
    {0xb0, (uint8_t[]){0xc0}, 1, 0},
    {0xb1, (uint8_t[]){0x80}, 1, 0},
    {0xb2, (uint8_t[]){0x27}, 1, 0},
    {0xb3, (uint8_t[]){0x13}, 1, 0},
    {0xb6, (uint8_t[]){0x19}, 1, 0},
    {0xb7, (uint8_t[]){0x05}, 1, 0},
    {0xac, (uint8_t[]){0xc8}, 1, 0},
    {0xab, (uint8_t[]){0x0f}, 1, 0},
    {0x3a, (uint8_t[]){0x05}, 1, 0},
    {0xb4, (uint8_t[]){0x04}, 1, 0},
    {0xa8, (uint8_t[]){0x08}, 1, 0},
    {0xb8, (uint8_t[]){0x08}, 1, 0},
    {0xea, (uint8_t[]){0x02}, 1, 0},
    {0xe8, (uint8_t[]){0x2A}, 1, 0},
    {0xe9, (uint8_t[]){0x47}, 1, 0},
    {0xe7, (uint8_t[]){0x5f}, 1, 0},
    {0xc6, (uint8_t[]){0x21}, 1, 0},
    {0xc7, (uint8_t[]){0x15}, 1, 0},
    {0xf0,
    (uint8_t[]){0x1D, 0x38, 0x09, 0x4D, 0x92, 0x2F, 0x35, 0x52, 0x1E, 0x0C,
                0x04, 0x12, 0x14, 0x1f},
    14, 0},
    {0xf1,
    (uint8_t[]){0x16, 0x40, 0x1C, 0x54, 0xA9, 0x2D, 0x2E, 0x56, 0x10, 0x0D,
                0x0C, 0x1A, 0x14, 0x1E},
    14, 0},
    {0xf4, (uint8_t[]){0x00, 0x00, 0xFF}, 3, 0},
    {0xba, (uint8_t[]){0xFF, 0xFF}, 2, 0},
};
#endif
 
#ifndef TAG
#define TAG "CompactWifiBoardLCD"
#endif

LV_FONT_DECLARE(font_puhui_16_4);
LV_FONT_DECLARE(font_awesome_16_4);

class CompactWifiBoardLCD : public WifiBoard {
private:

    Button boot_button_;
    LcdDisplay* display_;

    // AI玩具系统组件 (暂时注释掉，等待实现完成)
    // std::unique_ptr<servo_180::Servo180Controller> servo_ctrl_;
    // std::unique_ptr<voice_servo_integration::VoiceServoController> voice_servo_ctrl_;

    // 主系统控制器
    system_controller::MainSystemController* system_controller_;

    void InitializeSpi() {
        spi_bus_config_t buscfg = {};
        buscfg.mosi_io_num = DISPLAY_MOSI_PIN;
        buscfg.miso_io_num = GPIO_NUM_NC;
        buscfg.sclk_io_num = DISPLAY_CLK_PIN;
        buscfg.quadwp_io_num = GPIO_NUM_NC;
        buscfg.quadhd_io_num = GPIO_NUM_NC;
        buscfg.max_transfer_sz = DISPLAY_WIDTH * DISPLAY_HEIGHT * sizeof(uint16_t);
        ESP_ERROR_CHECK(spi_bus_initialize(SPI3_HOST, &buscfg, SPI_DMA_CH_AUTO));
    }

    void InitializeLcdDisplay() {
        esp_lcd_panel_io_handle_t panel_io = nullptr;
        esp_lcd_panel_handle_t panel = nullptr;
        // 液晶屏控制IO初始化
        ESP_LOGD(TAG, "Install panel IO");
        esp_lcd_panel_io_spi_config_t io_config = {};
        io_config.cs_gpio_num = DISPLAY_CS_PIN;
        io_config.dc_gpio_num = DISPLAY_DC_PIN;
        io_config.spi_mode = DISPLAY_SPI_MODE;
        io_config.pclk_hz = 40 * 1000 * 1000;
        io_config.trans_queue_depth = 10;
        io_config.lcd_cmd_bits = 8;
        io_config.lcd_param_bits = 8;
        ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi(SPI3_HOST, &io_config, &panel_io));

        // 初始化液晶屏驱动芯片
        ESP_LOGD(TAG, "Install LCD driver");
        esp_lcd_panel_dev_config_t panel_config = {};
        panel_config.reset_gpio_num = DISPLAY_RST_PIN;
        panel_config.rgb_ele_order = DISPLAY_RGB_ORDER;
        panel_config.bits_per_pixel = 16;
#if defined(LCD_TYPE_ILI9341_SERIAL)
        ESP_ERROR_CHECK(esp_lcd_new_panel_ili9341(panel_io, &panel_config, &panel));
#elif defined(LCD_TYPE_GC9A01_SERIAL)
        ESP_ERROR_CHECK(esp_lcd_new_panel_gc9a01(panel_io, &panel_config, &panel));
        gc9a01_vendor_config_t gc9107_vendor_config = {
            .init_cmds = gc9107_lcd_init_cmds,
            .init_cmds_size = sizeof(gc9107_lcd_init_cmds) / sizeof(gc9a01_lcd_init_cmd_t),
        };        
#else
        ESP_ERROR_CHECK(esp_lcd_new_panel_st7789(panel_io, &panel_config, &panel));
#endif
        
        esp_lcd_panel_reset(panel);
 

        esp_lcd_panel_init(panel);
        esp_lcd_panel_invert_color(panel, DISPLAY_INVERT_COLOR);
        esp_lcd_panel_swap_xy(panel, DISPLAY_SWAP_XY);
        esp_lcd_panel_mirror(panel, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y);
#ifdef  LCD_TYPE_GC9A01_SERIAL
        panel_config.vendor_config = &gc9107_vendor_config;
#endif
        display_ = new SpiLcdDisplay(panel_io, panel,
                                    DISPLAY_WIDTH, DISPLAY_HEIGHT, DISPLAY_OFFSET_X, DISPLAY_OFFSET_Y, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y, DISPLAY_SWAP_XY,
                                    {
                                        .text_font = &font_puhui_16_4,
                                        .icon_font = &font_awesome_16_4,
#if CONFIG_USE_WECHAT_MESSAGE_STYLE
                                        .emoji_font = font_emoji_32_init(),
#else
                                        .emoji_font = DISPLAY_HEIGHT >= 240 ? font_emoji_64_init() : font_emoji_32_init(),
#endif
                                    });
    }


 
    void InitializeButtons() {
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ResetWifiConfiguration();
            }
            app.ToggleChatState();
        });
    }

    // 物联网初始化，添加对 AI 可见设备
    void InitializeIot() {
#if CONFIG_IOT_PROTOCOL_XIAOZHI
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
        thing_manager.AddThing(iot::CreateThing("Screen"));
        thing_manager.AddThing(iot::CreateThing("Lamp"));
#elif CONFIG_IOT_PROTOCOL_MCP
        static LampController lamp(LAMP_GPIO);
#endif
    }

    // 180度舵机控制器初始化
    void InitializeServoController() {
        ESP_LOGI(TAG, "Initializing 180-degree servo controller...");

        // TODO: 暂时注释掉，等待实现完成
        // servo_ctrl_ = std::make_unique<servo_180::Servo180Controller>(GPIO_NUM_9, GPIO_NUM_10);

        if (false) { // servo_ctrl_) {
            /*
            ESP_LOGI(TAG, "180-degree servo controller initialized successfully");

            // 创建语音舵机集成控制器
            voice_servo_ctrl_ = std::make_unique<voice_servo_integration::VoiceServoController>(GPIO_NUM_9, GPIO_NUM_10);

            if (voice_servo_ctrl_->Initialize()) {
                ESP_LOGI(TAG, "Voice servo integration controller initialized successfully");

                // 设置语音指令回调
                voice_servo_ctrl_->SetActionCompleteCallback([](voice_servo_integration::VoiceCommandType cmd_type, bool success) {
                    ESP_LOGI(TAG, "语音指令执行: %s, 结果: %s",
                             voice_servo_integration::VoiceCommandTypeToString(cmd_type).c_str(),
                             success ? "成功" : "失败");
                });

                ESP_LOGI(TAG, "Voice servo integration ready for commands");
            } else {
                ESP_LOGE(TAG, "Failed to initialize voice servo integration controller");
            }
            */
        } else {
            ESP_LOGI(TAG, "Servo controller initialization skipped (implementation pending)");
        }
    }

    // 表情系统初始化
    void InitializeExpressionSystem() {
        ESP_LOGI(TAG, "Initializing expression system...");

        // TODO: 暂时注释掉，解决链接问题
        // expression_ctrl_ = expression_integration::ExpressionSystemFactory::CreateController();

        if (false) { // expression_ctrl_) {
            /*
            ESP_LOGI(TAG, "Expression system initialized successfully");

            // 设置表情变化回调
            expression_ctrl_->SetExpressionChangeCallback([](expression_system::EmotionType emotion, bool success) {
                ESP_LOGI(TAG, "表情变化: %s, 结果: %s",
                         expression_system::EmotionTypeToString(emotion).c_str(),
                         success ? "成功" : "失败");
            });

            // 演示表情功能
            ESP_LOGI(TAG, "Testing expression system...");
            expression_ctrl_->SetEmotion(expression_system::EmotionType::HAPPY);
            vTaskDelay(pdMS_TO_TICKS(2000));

            expression_ctrl_->Blink(true);
            vTaskDelay(pdMS_TO_TICKS(1000));

            expression_ctrl_->SetEmotion(expression_system::EmotionType::NEUTRAL);
            ESP_LOGI(TAG, "Expression test completed");
            */
        } else {
            ESP_LOGE(TAG, "Failed to initialize expression system");
        }
    }

    // AI玩具系统初始化
    void InitializeAIToySystem() {
        ESP_LOGI(TAG, "Initializing AI Toy System...");

        // 创建默认系统配置
        system_controller::SystemConfig config = system_controller::CreateDefaultSystemConfig();

        // 配置舵机
        motion_system::ServoConfig left_servo;
        left_servo.id = "left_arm";
        left_servo.gpio_pin = 9;
        left_servo.description = "Left arm servo";
        config.servo_configs.push_back(left_servo);

        motion_system::ServoConfig right_servo;
        right_servo.id = "right_arm";
        right_servo.gpio_pin = 10;
        right_servo.description = "Right arm servo";
        config.servo_configs.push_back(right_servo);

        // 初始化系统控制器
        if (system_controller_->Initialize(config)) {
            ESP_LOGI(TAG, "AI Toy System initialized successfully");

            // 启动系统
            if (system_controller_->Start()) {
                ESP_LOGI(TAG, "AI Toy System started successfully");

                // 系统已启动，准备接收语音控制
                ESP_LOGI(TAG, "System ready for voice control");
            } else {
                ESP_LOGE(TAG, "Failed to start AI Toy System");
            }
        } else {
            ESP_LOGE(TAG, "Failed to initialize AI Toy System");
        }
    }

public:
    CompactWifiBoardLCD()
        : boot_button_(BOOT_BUTTON_GPIO),
          system_controller_(&system_controller::MainSystemController::GetInstance())
    {
        InitializeSpi();
        InitializeLcdDisplay();
        InitializeButtons();
        InitializeIot();
        InitializeServoController();
        InitializeExpressionSystem();
        InitializeAIToySystem();
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            GetBacklight()->RestoreBrightness();
        }
    }

    virtual Led* GetLed() override {
        static SingleLed led(BUILTIN_LED_GPIO);
        return &led;
    }

    virtual AudioCodec* GetAudioCodec() override {
#ifdef AUDIO_I2S_METHOD_SIMPLEX
        static NoAudioCodecSimplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_SPK_GPIO_BCLK, AUDIO_I2S_SPK_GPIO_LRCK, AUDIO_I2S_SPK_GPIO_DOUT, AUDIO_I2S_MIC_GPIO_SCK, AUDIO_I2S_MIC_GPIO_WS, AUDIO_I2S_MIC_GPIO_DIN);
#else
        static NoAudioCodecDuplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN);
#endif
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        return display_;
    }

    virtual Backlight* GetBacklight() override {
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            static PwmBacklight backlight(DISPLAY_BACKLIGHT_PIN, DISPLAY_BACKLIGHT_OUTPUT_INVERT);
            return &backlight;
        }
        return nullptr;
    }

    // 处理语音控制命令
    bool ProcessVoiceCommand(const std::string& command) {
        ESP_LOGI(TAG, "Processing voice command: %s", command.c_str());

        // 转换为小写以便匹配
        std::string lower_command = command;
        std::transform(lower_command.begin(), lower_command.end(), lower_command.begin(), ::tolower);

        // 检查是否包含舵机控制相关的关键词
        bool is_servo_command = false;
        if (lower_command.find("挥手") != std::string::npos ||
            lower_command.find("举手") != std::string::npos ||
            lower_command.find("敬礼") != std::string::npos ||
            lower_command.find("跳舞") != std::string::npos ||
            lower_command.find("停止") != std::string::npos ||
            lower_command.find("重置") != std::string::npos ||
            lower_command.find("wave") != std::string::npos ||
            lower_command.find("dance") != std::string::npos ||
            lower_command.find("stop") != std::string::npos ||
            lower_command.find("reset") != std::string::npos) {
            is_servo_command = true;
        }

        if (is_servo_command) {
            ESP_LOGI(TAG, "Servo command detected: %s", command.c_str());

            // 模拟舵机控制（实际实现时会调用真正的舵机控制器）
            if (lower_command.find("挥手") != std::string::npos || lower_command.find("wave") != std::string::npos) {
                ESP_LOGI(TAG, "执行挥手动作");
                // TODO: 调用舵机控制器执行挥手动作
            } else if (lower_command.find("举手") != std::string::npos) {
                ESP_LOGI(TAG, "执行举手动作");
                // TODO: 调用舵机控制器执行举手动作
            } else if (lower_command.find("敬礼") != std::string::npos) {
                ESP_LOGI(TAG, "执行敬礼动作");
                // TODO: 调用舵机控制器执行敬礼动作
            } else if (lower_command.find("跳舞") != std::string::npos || lower_command.find("dance") != std::string::npos) {
                ESP_LOGI(TAG, "执行跳舞动作");
                // TODO: 调用舵机控制器执行跳舞动作
            } else if (lower_command.find("停止") != std::string::npos || lower_command.find("stop") != std::string::npos) {
                ESP_LOGI(TAG, "停止所有动作");
                // TODO: 调用舵机控制器停止动作
            } else if (lower_command.find("重置") != std::string::npos || lower_command.find("reset") != std::string::npos) {
                ESP_LOGI(TAG, "重置舵机位置");
                // TODO: 调用舵机控制器重置位置
            }

            return true; // 表示命令已被处理
        }

        return false; // 表示命令未被处理，可以继续传递给其他处理器
    }

    // 获取系统状态
    bool IsSystemReady() {
        return system_controller_ != nullptr;
    }
};

DECLARE_BOARD(CompactWifiBoardLCD);

// 全局语音控制接口
extern "C" {
    // 处理语音控制命令的全局函数
    bool xiaozhi_process_voice_command(const char* command) {
        if (!command) {
            return false;
        }

        // 获取板子实例
        auto* board = static_cast<CompactWifiBoardLCD*>(&Board::GetInstance());
        if (!board) {
            ESP_LOGW(TAG, "Board instance not available");
            return false;
        }

        // 检查系统是否准备就绪
        if (!board->IsSystemReady()) {
            ESP_LOGW(TAG, "System not ready for voice commands");
            return false;
        }

        // 处理语音命令
        std::string cmd_str(command);
        return board->ProcessVoiceCommand(cmd_str);
    }

    // 检查系统是否准备就绪
    bool xiaozhi_is_system_ready() {
        auto* board = static_cast<CompactWifiBoardLCD*>(&Board::GetInstance());
        return board ? board->IsSystemReady() : false;
    }
}
