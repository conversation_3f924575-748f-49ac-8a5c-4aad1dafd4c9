// Custom Hardware - 阶段1: 基础功能稳定
// 专注于双屏表情显示系统核心功能

#include "wifi_board.h"
#include "display/lcd_display.h"
#include "button.h"
#include "config.h"

// 阶段1: 仅包含核心表情系统
#include "expression_system/simple_dual_screen.h"
#include "expression_system/core_test.h"

#include <memory>
#include <string>

#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "driver/spi_master.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_ops.h"
#include "esp_lcd_gc9a01.h"

static const char* TAG = "CustomHardware";

// 阶段1: 简化的硬件配置
#define BOOT_BUTTON_GPIO GPIO_NUM_0

// 双屏GC9A01配置 (阶段1核心功能)
#define LEFT_SCREEN_CS_PIN   GPIO_NUM_2
#define RIGHT_SCREEN_CS_PIN  GPIO_NUM_45
#define SHARED_SCK_PIN       GPIO_NUM_19
#define SHARED_MOSI_PIN      GPIO_NUM_20
#define SHARED_DC_PIN        GPIO_NUM_21
#define SHARED_RST_PIN       GPIO_NUM_1

class CompactWifiBoardLCD : public WifiBoard {
private:
    // 阶段1: 仅保留双屏表情系统
    Button boot_button_;
    std::unique_ptr<simple_dual_screen::SimpleDualScreenManager> simple_dual_screen_manager_;
    bool dual_screen_initialized_ = false;

    // 阶段1: 双屏表情系统测试
    void InitializeDualScreenTest() {
        ESP_LOGI(TAG, "=== 阶段1: 双屏表情系统初始化 ===");
        
        // 初始化双屏表情系统
        simple_dual_screen_manager_ = std::make_unique<simple_dual_screen::SimpleDualScreenManager>();
        
        if (simple_dual_screen_manager_->Initialize()) {
            ESP_LOGI(TAG, "✅ 双屏表情系统初始化成功");
            dual_screen_initialized_ = true;
            
            // 运行基础表情测试
            ESP_LOGI(TAG, "🎭 开始基础表情测试...");
            if (core_test::InitializeCoreTest()) {
                core_test::RunBasicTest();
                ESP_LOGI(TAG, "✅ 基础表情测试完成");
            }
        } else {
            ESP_LOGE(TAG, "❌ 双屏表情系统初始化失败");
        }
    }

    // 简化的SPI初始化
    void InitializeSpi() {
        ESP_LOGI(TAG, "Initializing SPI for dual screens...");
        
        spi_bus_config_t bus_cfg = {
            .mosi_io_num = SHARED_MOSI_PIN,
            .miso_io_num = -1,
            .sclk_io_num = SHARED_SCK_PIN,
            .quadwp_io_num = -1,
            .quadhd_io_num = -1,
            .max_transfer_sz = 240 * 240 * 2 + 8
        };
        
        ESP_ERROR_CHECK(spi_bus_initialize(SPI2_HOST, &bus_cfg, SPI_DMA_CH_AUTO));
        ESP_LOGI(TAG, "SPI initialized successfully");
    }

    // 简化的LCD显示初始化
    void InitializeLcdDisplay() {
        ESP_LOGI(TAG, "LCD display initialization skipped in Phase 1");
        // 阶段1专注于双屏表情系统，LCD显示由SimpleDualScreenManager处理
    }

    // 简化的按钮初始化
    void InitializeButtons() {
        ESP_LOGI(TAG, "Initializing buttons...");
        // 阶段1仅初始化启动按钮
        ESP_LOGI(TAG, "Boot button initialized");
    }

public:
    CompactWifiBoardLCD() : boot_button_(BOOT_BUTTON_GPIO) {
        ESP_LOGI(TAG, "=== Custom Hardware 阶段1: 基础功能稳定 ===");
        
        // 阶段1: 仅初始化双屏表情系统必需的组件
        InitializeSpi();
        InitializeLcdDisplay();
        InitializeButtons();
        
        // 阶段1核心功能测试
        InitializeDualScreenTest();
        
        ESP_LOGI(TAG, "=== 阶段1初始化完成 ===");
    }

    // 获取系统状态
    bool IsSystemReady() {
        return dual_screen_initialized_;
    }
    
    // 处理语音命令 (阶段1简化实现)
    bool ProcessVoiceCommand(const std::string& command) {
        ESP_LOGI(TAG, "Voice command received: %s", command.c_str());
        
        if (simple_dual_screen_manager_) {
            // 简单的表情控制命令
            if (command.find("happy") != std::string::npos || command.find("开心") != std::string::npos) {
                simple_dual_screen_manager_->SetExpression(simple_dual_screen::ExpressionType::HAPPY);
                return true;
            } else if (command.find("sad") != std::string::npos || command.find("伤心") != std::string::npos) {
                simple_dual_screen_manager_->SetExpression(simple_dual_screen::ExpressionType::SAD);
                return true;
            } else if (command.find("angry") != std::string::npos || command.find("生气") != std::string::npos) {
                simple_dual_screen_manager_->SetExpression(simple_dual_screen::ExpressionType::ANGRY);
                return true;
            } else if (command.find("blink") != std::string::npos || command.find("眨眼") != std::string::npos) {
                simple_dual_screen_manager_->Blink(true);
                return true;
            }
        }
        
        ESP_LOGW(TAG, "Voice command not recognized in Phase 1: %s", command.c_str());
        return false;
    }
    
    // 处理MCP命令 (阶段1简化实现)
    bool ProcessMCPCommand(const std::string& tool, const std::string& params) {
        ESP_LOGI(TAG, "MCP command received - Tool: %s, Params: %s", tool.c_str(), params.c_str());
        ESP_LOGW(TAG, "MCP commands not implemented in Phase 1");
        return false;
    }

    // 阶段1: 简化的虚拟方法实现
    virtual Led* GetLed() override {
        return nullptr; // 阶段1暂不支持LED
    }

    virtual AudioCodec* GetAudioCodec() override {
        return nullptr; // 阶段1暂不支持音频
    }

    virtual Display* GetDisplay() override {
        return nullptr; // 阶段1使用SimpleDualScreenManager
    }

    virtual Backlight* GetBacklight() override {
        return nullptr; // 阶段1暂不支持背光控制
    }
};

// 全局函数实现
DECLARE_BOARD(CompactWifiBoardLCD);

// 阶段1: 简化的语音控制接口实现
extern "C" bool xiaozhi_process_voice_command(const char* command) {
    if (command == nullptr) {
        return false;
    }

    ESP_LOGI(TAG, "Voice command received: %s", command);

    // 阶段1: 简化的语音命令处理
    std::string cmd_str(command);

    // 简单的表情控制命令
    if (cmd_str.find("happy") != std::string::npos || cmd_str.find("开心") != std::string::npos) {
        ESP_LOGI(TAG, "Processing happy expression command");
        return true;
    } else if (cmd_str.find("sad") != std::string::npos || cmd_str.find("伤心") != std::string::npos) {
        ESP_LOGI(TAG, "Processing sad expression command");
        return true;
    } else if (cmd_str.find("angry") != std::string::npos || cmd_str.find("生气") != std::string::npos) {
        ESP_LOGI(TAG, "Processing angry expression command");
        return true;
    } else if (cmd_str.find("blink") != std::string::npos || cmd_str.find("眨眼") != std::string::npos) {
        ESP_LOGI(TAG, "Processing blink command");
        return true;
    }

    ESP_LOGW(TAG, "Voice command not recognized in Phase 1: %s", command);
    return false;
}

extern "C" bool xiaozhi_is_system_ready() {
    ESP_LOGI(TAG, "System ready check - Phase 1: always ready");
    return true; // 阶段1简化实现，总是返回ready
}

extern "C" bool xiaozhi_process_mcp_command(const char* tool_name, const char* parameters) {
    if (tool_name == nullptr) {
        return false;
    }

    ESP_LOGI(TAG, "MCP command received - Tool: %s, Params: %s",
             tool_name, parameters ? parameters : "null");
    ESP_LOGW(TAG, "MCP commands not implemented in Phase 1");
    return false;
}
