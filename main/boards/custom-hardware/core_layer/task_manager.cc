/**
 * @file task_manager.cc
 * @brief 任务管理器实现
 */

#include "task_manager.h"
#include <esp_log.h>

static const char* TAG = "TASK_MGR";

namespace core_layer {

TaskManager& TaskManager::GetInstance() {
    static TaskManager instance;
    return instance;
}

bool TaskManager::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        ESP_LOGW(TAG, "Task manager already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing task manager...");
    
    // 创建消息队列
    message_queue_ = xQueueCreate(32, sizeof(TaskMessage));
    if (!message_queue_) {
        ESP_LOGE(TAG, "Failed to create message queue");
        return false;
    }
    
    // 创建事件组
    event_group_ = xEventGroupCreate();
    if (!event_group_) {
        ESP_LOGE(TAG, "Failed to create event group");
        xQueueDelete(message_queue_);
        return false;
    }
    
    initialized_ = true;
    monitor_running_ = false;
    message_sequence_ = 0;
    
    ESP_LOGI(TAG, "Task manager initialized successfully");
    return true;
}

bool TaskManager::CreateTask(TaskType type, const std::string& name, 
                           std::shared_ptr<TaskCallback> callback,
                           TaskPriority priority, uint32_t stack_size) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        ESP_LOGE(TAG, "Task manager not initialized");
        return false;
    }
    
    if (tasks_.find(type) != tasks_.end()) {
        ESP_LOGW(TAG, "Task type %d already exists", static_cast<int>(type));
        return false;
    }
    
    TaskInfo task_info;
    task_info.type = type;
    task_info.name = name;
    task_info.priority = priority;
    task_info.state = TaskState::CREATED;
    task_info.stack_size = stack_size;
    task_info.created_time = xTaskGetTickCount();
    
    tasks_[type] = task_info;
    callbacks_[type] = callback;
    
    ESP_LOGI(TAG, "Created task: %s (type: %d)", name.c_str(), static_cast<int>(type));
    return true;
}

bool TaskManager::StartTask(TaskType type) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = tasks_.find(type);
    if (it == tasks_.end()) {
        ESP_LOGE(TAG, "Task type %d not found", static_cast<int>(type));
        return false;
    }
    
    TaskInfo& task_info = it->second;
    if (task_info.state == TaskState::RUNNING) {
        ESP_LOGW(TAG, "Task %s already running", task_info.name.c_str());
        return true;
    }
    
    // 创建FreeRTOS任务
    BaseType_t ret = xTaskCreate(
        TaskWrapper,
        task_info.name.c_str(),
        task_info.stack_size,
        &task_info,
        static_cast<UBaseType_t>(task_info.priority),
        &task_info.handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create FreeRTOS task for %s", task_info.name.c_str());
        return false;
    }
    
    task_info.state = TaskState::RUNNING;
    task_info.last_run_time = xTaskGetTickCount();
    
    ESP_LOGI(TAG, "Started task: %s", task_info.name.c_str());
    return true;
}

bool TaskManager::StopTask(TaskType type) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = tasks_.find(type);
    if (it == tasks_.end()) {
        ESP_LOGE(TAG, "Task type %d not found", static_cast<int>(type));
        return false;
    }
    
    TaskInfo& task_info = it->second;
    if (task_info.state != TaskState::RUNNING) {
        ESP_LOGW(TAG, "Task %s not running", task_info.name.c_str());
        return true;
    }
    
    if (task_info.handle) {
        vTaskDelete(task_info.handle);
        task_info.handle = nullptr;
    }
    
    task_info.state = TaskState::STOPPED;
    
    ESP_LOGI(TAG, "Stopped task: %s", task_info.name.c_str());
    return true;
}

bool TaskManager::SendMessage(const TaskMessage& message, uint32_t timeout_ms) {
    if (!message_queue_) {
        ESP_LOGE(TAG, "Message queue not initialized");
        return false;
    }
    
    TaskMessage msg = message;
    msg.timestamp = xTaskGetTickCount();
    msg.sequence_id = ++message_sequence_;
    
    BaseType_t ret = xQueueSend(message_queue_, &msg, pdMS_TO_TICKS(timeout_ms));
    if (ret != pdTRUE) {
        ESP_LOGW(TAG, "Failed to send message (queue full?)");
        return false;
    }
    
    return true;
}

TaskInfo TaskManager::GetTaskInfo(TaskType type) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = tasks_.find(type);
    if (it != tasks_.end()) {
        return it->second;
    }
    
    return TaskInfo(); // 返回默认构造的TaskInfo
}

std::map<TaskType, TaskInfo> TaskManager::GetAllTaskInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return tasks_;
}

bool TaskManager::IsTaskHealthy(TaskType type) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = tasks_.find(type);
    if (it == tasks_.end()) {
        return false;
    }
    
    const TaskInfo& task_info = it->second;
    return task_info.state == TaskState::RUNNING;
}

std::string TaskManager::GetSystemLoad() const {
    // 简化实现，返回基本的系统负载信息
    return "System load: Normal";
}

std::string TaskManager::GetMemoryUsage() const {
    // 简化实现，返回基本的内存使用信息
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free_heap = esp_get_minimum_free_heap_size();
    
    char buffer[128];
    snprintf(buffer, sizeof(buffer),
             "Free heap: %u bytes, Min free: %u bytes",
             free_heap, min_free_heap);
    
    return std::string(buffer);
}

void TaskManager::StartSystemMonitor() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (monitor_running_) {
        ESP_LOGW(TAG, "System monitor already running");
        return;
    }
    
    BaseType_t ret = xTaskCreate(
        SystemMonitorTask,
        "sys_monitor",
        4096,
        this,
        1, // 低优先级
        &system_monitor_
    );
    
    if (ret == pdPASS) {
        monitor_running_ = true;
        ESP_LOGI(TAG, "System monitor started");
    } else {
        ESP_LOGE(TAG, "Failed to start system monitor");
    }
}

void TaskManager::StopSystemMonitor() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!monitor_running_) {
        return;
    }
    
    monitor_running_ = false;
    
    if (system_monitor_) {
        vTaskDelete(system_monitor_);
        system_monitor_ = nullptr;
    }
    
    ESP_LOGI(TAG, "System monitor stopped");
}

// 静态方法实现
void TaskManager::TaskWrapper(void* parameter) {
    TaskInfo* task_info = static_cast<TaskInfo*>(parameter);
    
    ESP_LOGI(TAG, "Task wrapper started for: %s", task_info->name.c_str());
    
    // 简化的任务执行循环
    while (task_info->state == TaskState::RUNNING) {
        task_info->run_count++;
        task_info->last_run_time = xTaskGetTickCount();
        
        // 这里应该调用具体的任务回调
        // 由于是简化实现，我们只是延时
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    ESP_LOGI(TAG, "Task wrapper ended for: %s", task_info->name.c_str());
    vTaskDelete(nullptr);
}

void TaskManager::SystemMonitorTask(void* parameter) {
    TaskManager* manager = static_cast<TaskManager*>(parameter);
    
    ESP_LOGI(TAG, "System monitor task started");
    
    while (manager->monitor_running_) {
        // 监控系统健康状态
        // 简化实现，只是定期打印状态
        ESP_LOGD(TAG, "System monitor check - %s", 
                 manager->GetMemoryUsage().c_str());
        
        vTaskDelay(pdMS_TO_TICKS(5000)); // 5秒检查一次
    }
    
    ESP_LOGI(TAG, "System monitor task stopped");
    vTaskDelete(nullptr);
}

// 辅助函数实现
std::string TaskTypeToString(TaskType type) {
    switch (type) {
        case TaskType::VOICE_INTERACTION: return "VOICE_INTERACTION";
        case TaskType::SCREEN_RENDERING: return "SCREEN_RENDERING";
        case TaskType::SERVO_CONTROL: return "SERVO_CONTROL";
        case TaskType::MOTOR_CONTROL: return "MOTOR_CONTROL";
        case TaskType::PERSON_FOLLOWING: return "PERSON_FOLLOWING";
        case TaskType::OBSTACLE_AVOIDANCE: return "OBSTACLE_AVOIDANCE";
        case TaskType::SENSOR_MONITORING: return "SENSOR_MONITORING";
        case TaskType::DATA_STORAGE: return "DATA_STORAGE";
        case TaskType::SYSTEM_MONITOR: return "SYSTEM_MONITOR";
        case TaskType::CUSTOM: return "CUSTOM";
        default: return "UNKNOWN";
    }
}

std::string TaskPriorityToString(TaskPriority priority) {
    switch (priority) {
        case TaskPriority::CRITICAL: return "CRITICAL";
        case TaskPriority::HIGH: return "HIGH";
        case TaskPriority::NORMAL: return "NORMAL";
        case TaskPriority::LOW: return "LOW";
        case TaskPriority::BACKGROUND: return "BACKGROUND";
        default: return "UNKNOWN";
    }
}

std::string TaskStateToString(TaskState state) {
    switch (state) {
        case TaskState::CREATED: return "CREATED";
        case TaskState::RUNNING: return "RUNNING";
        case TaskState::SUSPENDED: return "SUSPENDED";
        case TaskState::STOPPED: return "STOPPED";
        case TaskState::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

} // namespace core_layer
