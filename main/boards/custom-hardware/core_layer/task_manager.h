/**
 * @file task_manager.h
 * @brief 核心任务层 - 多任务并发处理管理器
 * 
 * 负责管理系统中的各种任务，包括语音交互、屏幕渲染、舵机控制、
 * 电机驱动、人物跟随等任务的调度和协同工作
 */

#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include <memory>
#include <string>
#include <functional>
#include <map>
#include <queue>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"

namespace core_layer {

/**
 * @brief 任务优先级定义
 */
enum class TaskPriority {
    CRITICAL = 5,      // 关键任务 (语音交互、安全相关)
    HIGH = 4,          // 高优先级 (人物跟随、避障)
    NORMAL = 3,        // 普通优先级 (表情显示、舵机控制)
    LOW = 2,           // 低优先级 (数据存储、统计)
    BACKGROUND = 1     // 后台任务 (系统监控)
};

/**
 * @brief 任务状态
 */
enum class TaskState {
    CREATED,           // 已创建
    RUNNING,           // 运行中
    SUSPENDED,         // 暂停
    STOPPED,           // 已停止
    ERROR              // 错误状态
};

/**
 * @brief 任务类型
 */
enum class TaskType {
    VOICE_INTERACTION,     // 语音交互任务
    SCREEN_RENDERING,      // 屏幕渲染任务
    SERVO_CONTROL,         // 舵机控制任务
    MOTOR_CONTROL,         // 电机控制任务
    PERSON_FOLLOWING,      // 人物跟随任务
    OBSTACLE_AVOIDANCE,    // 避障任务
    SENSOR_MONITORING,     // 传感器监控任务
    DATA_STORAGE,          // 数据存储任务
    SYSTEM_MONITOR,        // 系统监控任务
    CUSTOM                 // 自定义任务
};

/**
 * @brief 消息类型
 */
enum class MessageType {
    COMMAND,           // 命令消息
    DATA,              // 数据消息
    STATUS,            // 状态消息
    ERROR,             // 错误消息
    EVENT              // 事件消息
};

/**
 * @brief 任务间消息结构
 */
struct TaskMessage {
    MessageType type;
    TaskType sender;
    TaskType receiver;
    std::string command;
    std::string data;
    uint32_t timestamp;
    uint32_t sequence_id;
    
    TaskMessage() : type(MessageType::COMMAND), sender(TaskType::CUSTOM), 
                   receiver(TaskType::CUSTOM), timestamp(0), sequence_id(0) {}
};

/**
 * @brief 任务信息结构
 */
struct TaskInfo {
    TaskType type;
    std::string name;
    TaskPriority priority;
    TaskState state;
    TaskHandle_t handle;
    uint32_t stack_size;
    uint32_t created_time;
    uint32_t last_run_time;
    uint32_t run_count;
    
    TaskInfo() : type(TaskType::CUSTOM), priority(TaskPriority::NORMAL), 
                state(TaskState::CREATED), handle(nullptr), stack_size(4096),
                created_time(0), last_run_time(0), run_count(0) {}
};

/**
 * @brief 任务回调接口
 */
class TaskCallback {
public:
    virtual ~TaskCallback() = default;
    
    // 任务执行回调
    virtual void OnTaskExecute() = 0;
    
    // 消息处理回调
    virtual void OnMessageReceived(const TaskMessage& message) = 0;
    
    // 任务状态变化回调
    virtual void OnTaskStateChanged(TaskState old_state, TaskState new_state) = 0;
    
    // 错误处理回调
    virtual void OnTaskError(const std::string& error_message) = 0;
};

/**
 * @brief 任务管理器类
 */
class TaskManager {
public:
    static TaskManager& GetInstance();
    
    /**
     * @brief 初始化任务管理器
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 创建任务
     * @param type 任务类型
     * @param name 任务名称
     * @param callback 任务回调
     * @param priority 任务优先级
     * @param stack_size 栈大小
     * @return 是否成功创建
     */
    bool CreateTask(TaskType type, const std::string& name, 
                   std::shared_ptr<TaskCallback> callback,
                   TaskPriority priority = TaskPriority::NORMAL,
                   uint32_t stack_size = 4096);
    
    /**
     * @brief 启动任务
     * @param type 任务类型
     * @return 是否成功启动
     */
    bool StartTask(TaskType type);
    
    /**
     * @brief 停止任务
     * @param type 任务类型
     * @return 是否成功停止
     */
    bool StopTask(TaskType type);
    
    /**
     * @brief 暂停任务
     * @param type 任务类型
     * @return 是否成功暂停
     */
    bool SuspendTask(TaskType type);
    
    /**
     * @brief 恢复任务
     * @param type 任务类型
     * @return 是否成功恢复
     */
    bool ResumeTask(TaskType type);
    
    /**
     * @brief 发送消息
     * @param message 消息内容
     * @param timeout_ms 超时时间(毫秒)
     * @return 是否成功发送
     */
    bool SendMessage(const TaskMessage& message, uint32_t timeout_ms = 1000);
    
    /**
     * @brief 广播消息
     * @param message 消息内容
     * @param exclude_sender 是否排除发送者
     * @return 成功发送的任务数量
     */
    int BroadcastMessage(const TaskMessage& message, bool exclude_sender = true);
    
    /**
     * @brief 获取任务信息
     * @param type 任务类型
     * @return 任务信息
     */
    TaskInfo GetTaskInfo(TaskType type) const;
    
    /**
     * @brief 获取所有任务信息
     * @return 任务信息映射
     */
    std::map<TaskType, TaskInfo> GetAllTaskInfo() const;
    
    /**
     * @brief 设置任务优先级
     * @param type 任务类型
     * @param priority 新优先级
     * @return 是否成功设置
     */
    bool SetTaskPriority(TaskType type, TaskPriority priority);
    
    /**
     * @brief 检查任务健康状态
     * @param type 任务类型
     * @return 是否健康
     */
    bool IsTaskHealthy(TaskType type) const;
    
    /**
     * @brief 获取系统负载信息
     * @return 负载信息字符串
     */
    std::string GetSystemLoad() const;
    
    /**
     * @brief 获取内存使用情况
     * @return 内存使用信息
     */
    std::string GetMemoryUsage() const;
    
    /**
     * @brief 启动系统监控
     */
    void StartSystemMonitor();
    
    /**
     * @brief 停止系统监控
     */
    void StopSystemMonitor();

private:
    TaskManager() = default;
    ~TaskManager() = default;
    TaskManager(const TaskManager&) = delete;
    TaskManager& operator=(const TaskManager&) = delete;
    
    // 任务包装函数
    static void TaskWrapper(void* parameter);
    
    // 消息处理任务
    static void MessageHandlerTask(void* parameter);
    
    // 系统监控任务
    static void SystemMonitorTask(void* parameter);
    
    std::map<TaskType, TaskInfo> tasks_;
    std::map<TaskType, std::shared_ptr<TaskCallback>> callbacks_;
    QueueHandle_t message_queue_;
    EventGroupHandle_t event_group_;
    mutable std::mutex mutex_;
    bool initialized_;
    bool monitor_running_;
    TaskHandle_t message_handler_;
    TaskHandle_t system_monitor_;
    uint32_t message_sequence_;
};

/**
 * @brief 任务类型转字符串
 */
std::string TaskTypeToString(TaskType type);

/**
 * @brief 任务优先级转字符串
 */
std::string TaskPriorityToString(TaskPriority priority);

/**
 * @brief 任务状态转字符串
 */
std::string TaskStateToString(TaskState state);

} // namespace core_layer

#endif // TASK_MANAGER_H
