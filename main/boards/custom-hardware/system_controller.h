/**
 * @file system_controller.h
 * @brief 系统主控制器
 * 
 * 负责协调各个子系统的工作，实现多模态交互和智能决策
 * 是整个AI玩具系统的核心控制中心
 */

#ifndef SYSTEM_CONTROLLER_H
#define SYSTEM_CONTROLLER_H

#include <string>
#include <memory>
#include <map>
#include <vector>
#include <functional>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"

// 引入各子系统头文件
#include "ai_layer/ai_interface.h"
#include "core_layer/task_manager.h"
#include "device_layer/device_interface.h"
#include "hardware_layer/mcp_communication.h"
#include "expression_system/expression_manager.h"
#include "motion_system/motion_controller.h"
#include "sensor_system/sensor_manager.h"

namespace system_controller {

/**
 * @brief 系统状态枚举
 */
enum class SystemState {
    UNINITIALIZED,     // 未初始化
    INITIALIZING,      // 初始化中
    READY,             // 就绪
    ACTIVE,            // 活跃状态
    FOLLOWING,         // 跟随模式
    INTERACTING,       // 交互模式
    SLEEPING,          // 休眠模式
    ERROR,             // 错误状态
    SHUTDOWN           // 关闭状态
};

/**
 * @brief 交互模式枚举
 */
enum class InteractionMode {
    PASSIVE,           // 被动模式
    ACTIVE,            // 主动模式
    FOLLOW,            // 跟随模式
    PLAY,              // 游戏模式
    LEARN,             // 学习模式
    SLEEP              // 休眠模式
};

/**
 * @brief 系统配置结构
 */
struct SystemConfig {
    // AI配置
    std::string ai_model_path;
    std::string ai_config_path;
    
    // 硬件配置
    std::vector<motion_system::ServoConfig> servo_configs;
    std::vector<motion_system::MotorConfig> motor_configs;
    std::vector<sensor_system::SensorConfig> sensor_configs;
    
    // 显示配置
    std::string expression_resource_path;
    uint8_t default_brightness;
    
    // 交互配置
    InteractionMode default_mode;
    uint32_t interaction_timeout;
    uint32_t sleep_timeout;
    
    // 跟随配置
    float follow_min_distance;
    float follow_max_distance;
    uint8_t follow_speed;
    
    // 安全配置
    float safe_distance;
    bool enable_cliff_detection;
    bool enable_obstacle_avoidance;
    
    SystemConfig() : default_brightness(80), default_mode(InteractionMode::PASSIVE),
                    interaction_timeout(30000), sleep_timeout(300000),
                    follow_min_distance(1.0f), follow_max_distance(3.0f),
                    follow_speed(50), safe_distance(0.3f),
                    enable_cliff_detection(true), enable_obstacle_avoidance(true) {}
};

/**
 * @brief 系统状态信息结构
 */
struct SystemStatus {
    bool is_running;                    // 是否运行中
    uint64_t uptime_ms;                // 运行时间（毫秒）
    float overall_health;              // 总体健康度（0.0-1.0）
    bool expression_system_healthy;    // 表情系统健康状态
    bool ai_system_healthy;           // AI系统健康状态
    bool motion_system_healthy;       // 运动系统健康状态
    bool sensor_system_healthy;       // 传感器系统健康状态

    SystemStatus() : is_running(false), uptime_ms(0), overall_health(0.0f),
                    expression_system_healthy(false), ai_system_healthy(false),
                    motion_system_healthy(false), sensor_system_healthy(false) {}
};

/**
 * @brief 系统事件结构
 */
struct SystemEvent {
    std::string event_type;        // 事件类型
    std::string source;            // 事件源
    std::string data;              // 事件数据
    uint32_t timestamp;            // 时间戳
    int priority;                  // 优先级
    
    SystemEvent() : timestamp(0), priority(0) {}
};

/**
 * @brief 系统回调接口
 */
class SystemCallback {
public:
    virtual ~SystemCallback() = default;
    
    // 系统状态变化回调
    virtual void OnSystemStateChanged(SystemState old_state, SystemState new_state) = 0;
    
    // 交互模式变化回调
    virtual void OnInteractionModeChanged(InteractionMode old_mode, InteractionMode new_mode) = 0;
    
    // 用户交互回调
    virtual void OnUserInteraction(const std::string& interaction_type, const std::string& data) = 0;
    
    // 系统事件回调
    virtual void OnSystemEvent(const SystemEvent& event) = 0;
    
    // 系统错误回调
    virtual void OnSystemError(const std::string& error_message) = 0;
};

/**
 * @brief 主系统控制器类
 */
class MainSystemController : 
    public ai_layer::AICallback,
    public core_layer::TaskCallback,
    public device_layer::DeviceCallback,
    public hardware_layer::MCPCallback,
    public expression_system::ExpressionCallback,
    public motion_system::MotionCallback,
    public sensor_system::SensorCallback {

public:
    static MainSystemController& GetInstance();
    
    /**
     * @brief 初始化系统
     * @param config 系统配置
     * @return 是否成功
     */
    bool Initialize(const SystemConfig& config);
    
    /**
     * @brief 启动系统
     * @return 是否成功
     */
    bool Start();
    
    /**
     * @brief 停止系统
     * @return 是否成功
     */
    bool Stop();
    
    /**
     * @brief 重启系统
     * @return 是否成功
     */
    bool Restart();
    
    /**
     * @brief 设置交互模式
     * @param mode 交互模式
     * @return 是否成功
     */
    bool SetInteractionMode(InteractionMode mode);
    
    /**
     * @brief 处理语音指令
     * @param command 语音指令
     * @return 是否成功处理
     */
    bool ProcessVoiceCommand(const std::string& command);
    
    /**
     * @brief 处理文本指令
     * @param command 文本指令
     * @return 是否成功处理
     */
    bool ProcessTextCommand(const std::string& command);
    
    /**
     * @brief 开始跟随模式
     * @return 是否成功
     */
    bool StartFollowMode();
    
    /**
     * @brief 停止跟随模式
     * @return 是否成功
     */
    bool StopFollowMode();
    
    /**
     * @brief 紧急停止
     * @return 是否成功
     */
    bool EmergencyStop();
    
    /**
     * @brief 进入休眠模式
     * @return 是否成功
     */
    bool EnterSleepMode();
    
    /**
     * @brief 唤醒系统
     * @return 是否成功
     */
    bool WakeUp();
    
    /**
     * @brief 获取系统状态
     * @return 系统状态
     */
    SystemState GetSystemState() const;
    
    /**
     * @brief 获取交互模式
     * @return 交互模式
     */
    InteractionMode GetInteractionMode() const;
    
    /**
     * @brief 获取系统统计信息
     * @return 统计信息
     */
    std::string GetSystemStatistics() const;

    /**
     * @brief 获取系统状态信息
     * @return 系统状态信息
     */
    SystemStatus GetSystemStatus() const;

    /**
     * @brief 设置表情
     * @param expression 表情名称
     * @return 是否成功
     */
    bool SetExpression(const std::string& expression);
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    void SetCallback(std::shared_ptr<SystemCallback> callback);
    
    /**
     * @brief 更新系统
     * 需要在主循环中定期调用
     */
    void Update();

    // AI回调接口实现
    void OnVoiceRecognized(const ai_layer::VoiceRecognitionResult& result) override;
    void OnAIResponse(const ai_layer::AIResponse& response) override;
    void OnIntentRecognized(const ai_layer::IntentResult& intent) override;
    void OnError(const std::string& error_message) override;

    // 任务回调接口实现
    void OnTaskExecute() override;
    void OnMessageReceived(const core_layer::TaskMessage& message) override;
    void OnTaskStateChanged(core_layer::TaskState old_state, core_layer::TaskState new_state) override;
    void OnTaskError(const std::string& error_message) override;

    // 设备回调接口实现
    void OnDeviceStateChanged(const std::string& device_id, 
                            device_layer::DeviceState old_state, 
                            device_layer::DeviceState new_state) override;
    void OnDeviceDataReceived(const std::string& device_id, const std::string& data) override;
    void OnDeviceError(const std::string& device_id, const std::string& error_message) override;

    // MCP回调接口实现
    void OnGPIOStateChanged(uint8_t pin, hardware_layer::GPIOState state) override;
    void OnInterrupt(uint8_t pin) override;
    void OnCommunicationError(const std::string& error_message) override;
    void OnDeviceConnectionChanged(uint8_t address, bool connected) override;

    // 表情回调接口实现
    void OnExpressionChanged(expression_system::EmotionType old_emotion, 
                           expression_system::EmotionType new_emotion) override;
    void OnAnimationStarted(expression_system::AnimationType animation, 
                          expression_system::EyePosition eye) override;
    void OnAnimationFinished(expression_system::AnimationType animation, 
                           expression_system::EyePosition eye) override;
    void OnRenderCompleted(expression_system::EyePosition eye) override;

    // 运动回调接口实现
    void OnServoActionCompleted(const std::string& servo_id, 
                              motion_system::ServoAction action) override;
    void OnMotorMotionCompleted(const std::string& motor_id, 
                              motion_system::MotorDirection direction) override;
    void OnFollowStateChanged(motion_system::FollowState old_state, 
                            motion_system::FollowState new_state) override;
    void OnTargetDetected(const motion_system::TargetInfo& target) override;
    void OnTargetLost() override;
    void OnObstacleDetected(float distance, float angle) override;
    void OnMotionError(const std::string& error_message) override;

    // 传感器回调接口实现
    void OnSensorDataUpdated(const sensor_system::SensorData& data) override;
    void OnObstacleDetected(const sensor_system::SensorData& data) override;
    void OnCliffDetected(const sensor_system::SensorData& data) override;
    void OnPersonDetected(const sensor_system::SensorData& data) override;
    void OnPersonLost(const std::string& sensor_id) override;
    void OnSensorStateChanged(const std::string& sensor_id, 
                            sensor_system::SensorState old_state, 
                            sensor_system::SensorState new_state) override;
    void OnSensorError(const std::string& sensor_id, const std::string& error_message) override;

private:
    MainSystemController() = default;
    ~MainSystemController() = default;
    MainSystemController(const MainSystemController&) = delete;
    MainSystemController& operator=(const MainSystemController&) = delete;
    
    // 初始化各子系统
    bool InitializeAISystem();
    bool InitializeTaskManager();
    bool InitializeDeviceLayer();
    bool InitializeHardwareLayer();
    bool InitializeExpressionSystem();
    bool InitializeMotionSystem();
    bool InitializeSensorSystem();
    
    // 系统状态管理
    void SetSystemState(SystemState new_state);
    void HandleStateTransition(SystemState old_state, SystemState new_state);
    
    // 指令处理
    void ProcessAICommand(const ai_layer::AIResponse& response);
    void ExecuteMotionCommand(const std::string& command, const std::map<std::string, std::string>& params);
    void ExecuteExpressionCommand(const std::string& command, const std::map<std::string, std::string>& params);
    
    // 安全检查
    bool PerformSafetyCheck();
    void HandleEmergencyStop();
    void HandleObstacleAvoidance(const sensor_system::SensorData& obstacle_data);
    void HandleCliffAvoidance(const sensor_system::SensorData& cliff_data);
    
    // 跟随逻辑
    void UpdateFollowLogic();
    void ProcessPersonTracking(const sensor_system::SensorData& person_data);
    
    // 交互逻辑
    void UpdateInteractionLogic();
    void HandleUserInteraction(const std::string& interaction_type);
    void CheckInteractionTimeout();
    
    // 系统监控
    void MonitorSystemHealth();
    void LogSystemEvent(const SystemEvent& event);
    
    // 成员变量
    SystemConfig config_;
    SystemState current_state_;
    InteractionMode current_mode_;
    std::shared_ptr<SystemCallback> callback_;
    
    // 子系统实例
    std::unique_ptr<ai_layer::XiaozhiAI> ai_system_;
    
    // 系统状态
    bool initialized_;
    bool running_;
    uint32_t last_interaction_time_;
    uint32_t last_update_time_;
    
    // 同步对象
    mutable std::mutex mutex_;
    EventGroupHandle_t event_group_;
    TaskHandle_t main_task_;
    
    // 事件队列
    QueueHandle_t event_queue_;
    
    // 系统监控任务
    static void MainControlTask(void* parameter);
    static void SystemMonitorTask(void* parameter);
    static void EventProcessorTask(void* parameter);
};

/**
 * @brief 系统状态转字符串
 */
std::string SystemStateToString(SystemState state);

/**
 * @brief 交互模式转字符串
 */
std::string InteractionModeToString(InteractionMode mode);

/**
 * @brief 字符串转交互模式
 */
InteractionMode StringToInteractionMode(const std::string& mode_str);

/**
 * @brief 创建默认系统配置
 */
SystemConfig CreateDefaultSystemConfig();

/**
 * @brief 加载系统配置
 */
bool LoadSystemConfig(const std::string& config_path, SystemConfig& config);

/**
 * @brief 保存系统配置
 */
bool SaveSystemConfig(const std::string& config_path, const SystemConfig& config);

} // namespace system_controller

#endif // SYSTEM_CONTROLLER_H
