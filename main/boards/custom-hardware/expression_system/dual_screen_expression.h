/**
 * @file dual_screen_expression.h
 * @brief 双屏表情系统 - 基于main_lvgl_proper的优化方案
 * 
 * 解决双屏幕针脚共用问题，采用共享SPI总线 + 独立CS引脚的方案
 * 参考main_lvgl_proper.c的成功实现，优化针脚冲突和初始化顺序
 */

#pragma once
#include <string>
#include <map>
#include <vector>
#include <functional>
#include <memory>
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "freertos/task.h"
#include "esp_timer.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "driver/gpio.h"
#include "driver/spi_master.h"
#include "lvgl.h"
#include "esp_lcd_gc9a01.h"

namespace dual_screen_expression {

// 硬件配置 - 基于main_lvgl_proper的成功配置
#define LCD_HOST SPI2_HOST
#define SCREEN_WIDTH  240
#define SCREEN_HEIGHT 240

// 针脚配置 - 解决共用问题的关键
#define PIN_SCK       19    // SPI时钟 (共享)
#define PIN_MOSI      20    // SPI数据输出 (共享)
#define PIN_DC        21    // 数据/命令控制 (共享)
#define PIN_RST       1     // 复位引脚 (共享)
#define PIN_CS_LEFT   2     // 左屏片选 (独立)
#define PIN_CS_RIGHT  45    // 右屏片选 (独立)

// LVGL配置
#define LVGL_TICK_PERIOD_MS     2
#define LVGL_TASK_MAX_DELAY_MS  500
#define LVGL_TASK_MIN_DELAY_MS  1
#define LVGL_TASK_STACK_SIZE    (6 * 1024)
#define LVGL_TASK_PRIORITY      2
#define LVGL_BUFFER_HEIGHT      60

/**
 * @brief 表情类型枚举
 */
enum class ExpressionType {
    NORMAL,            // 正常状态
    HAPPY,             // 开心
    SAD,               // 伤心
    ANGRY,             // 生气
    SURPRISED,         // 惊讶
    CONFUSED,          // 困惑
    SLEEPY,            // 困倦
    FOCUSED,           // 专注
    BLINKING,          // 眨眼
    LOOKING,           // 眼球移动
    LOVE,              // 喜爱
    THINKING,          // 思考
    EXCITED,           // 兴奋
    WORRIED,           // 担心
    CUSTOM             // 自定义
};

/**
 * @brief 眼睛结构体
 */
typedef struct {
    // LVGL对象
    lv_obj_t *eye_container;    // 眼球容器
    lv_obj_t *pupil;           // 瞳孔
    lv_obj_t *highlight;       // 高光
    lv_obj_t *small_highlight; // 小高光
    lv_obj_t *eyelid_top;      // 上眼睑
    lv_obj_t *eyelid_bottom;   // 下眼睑
    
    // 状态信息
    ExpressionType state;       // 当前状态
    int32_t pupil_x;           // 瞳孔X位置
    int32_t pupil_y;           // 瞳孔Y位置
    
    // 动画状态跟踪
    int32_t start_x;           // 动画起始X
    int32_t start_y;           // 动画起始Y
    int32_t target_x;          // 动画目标X
    int32_t target_y;          // 动画目标Y
    bool is_left_eye;          // 是否为左眼
} eye_t;

/**
 * @brief 表情配置结构体
 */
struct ExpressionConfig {
    int pupil_size;            // 瞳孔大小
    int eye_width;             // 眼睛宽度
    int eye_height;            // 眼睛高度
    lv_color_t pupil_color;    // 瞳孔颜色
    lv_color_t eye_color;      // 眼球颜色
    int blink_duration;        // 眨眼持续时间
    int animation_speed;       // 动画速度
    bool has_highlight;        // 是否有高光
    int highlight_size;        // 高光大小
    
    ExpressionConfig() : 
        pupil_size(80), eye_width(200), eye_height(200),
        pupil_color(lv_color_black()), eye_color(lv_color_white()),
        blink_duration(150), animation_speed(300),
        has_highlight(true), highlight_size(25) {}
};

/**
 * @brief 双屏表情管理器
 */
class DualScreenExpressionManager {
public:
    /**
     * @brief 构造函数
     */
    DualScreenExpressionManager();
    
    /**
     * @brief 析构函数
     */
    ~DualScreenExpressionManager();
    
    /**
     * @brief 初始化双屏表情系统
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 设置表情
     * @param expression 表情类型
     * @param config 表情配置（可选）
     * @return 是否成功
     */
    bool SetExpression(ExpressionType expression, const ExpressionConfig* config = nullptr);
    
    /**
     * @brief 设置单眼表情
     * @param is_left_eye 是否为左眼
     * @param expression 表情类型
     * @param config 表情配置（可选）
     * @return 是否成功
     */
    bool SetSingleEyeExpression(bool is_left_eye, ExpressionType expression, 
                               const ExpressionConfig* config = nullptr);
    
    /**
     * @brief 眨眼动画
     * @param both_eyes 是否双眼同时眨眼
     * @return 是否成功
     */
    bool Blink(bool both_eyes = true);
    
    /**
     * @brief 眼球移动
     * @param target_x 目标X位置
     * @param target_y 目标Y位置
     * @param both_eyes 是否双眼同时移动
     * @return 是否成功
     */
    bool LookAt(int32_t target_x, int32_t target_y, bool both_eyes = true);
    
    /**
     * @brief 跟随目标
     * @param target_x 目标X位置
     * @param target_y 目标Y位置
     * @return 是否成功
     */
    bool FollowTarget(int32_t target_x, int32_t target_y);
    
    /**
     * @brief 开始自动动画
     * @param enable 是否启用
     */
    void EnableAutoAnimation(bool enable);
    
    /**
     * @brief 设置表情变化回调
     * @param callback 回调函数
     */
    void SetExpressionChangeCallback(std::function<void(ExpressionType, bool)> callback);
    
    /**
     * @brief 获取当前表情
     * @return 当前表情类型
     */
    ExpressionType GetCurrentExpression() const;
    
    /**
     * @brief 检查是否初始化
     * @return 是否已初始化
     */
    bool IsInitialized() const { return initialized_; }
    
    /**
     * @brief 获取左眼显示器
     * @return 左眼显示器句柄
     */
    lv_disp_t* GetLeftDisplay() { return left_disp_; }
    
    /**
     * @brief 获取右眼显示器
     * @return 右眼显示器句柄
     */
    lv_disp_t* GetRightDisplay() { return right_disp_; }

private:
    // 硬件相关
    esp_lcd_panel_handle_t left_panel_handle_;
    esp_lcd_panel_handle_t right_panel_handle_;
    lv_disp_t *left_disp_;
    lv_disp_t *right_disp_;
    
    // 眼睛对象
    eye_t left_eye_;
    eye_t right_eye_;
    
    // 状态管理
    bool initialized_;
    ExpressionType current_expression_;
    bool auto_animation_enabled_;
    
    // 同步和任务
    SemaphoreHandle_t lvgl_mux_;
    lv_timer_t *animation_timer_;
    esp_timer_handle_t lvgl_tick_timer_;
    
    // 回调函数
    std::function<void(ExpressionType, bool)> expression_callback_;
    
    // 私有方法
    esp_err_t InitializeSPIBus();
    esp_err_t InitializeScreen(int cs_pin, esp_lcd_panel_handle_t *panel_handle, 
                              lv_disp_t **disp, const char* screen_name);
    void CreateEyeUI(lv_disp_t *disp, eye_t *eye, const char* eye_name);
    void SetupLVGLTimer();
    void StartLVGLTask();
    
    // 动画相关
    void StartBlinkAnimation(eye_t *eye);
    void StartLookAnimation(eye_t *eye, int32_t target_x, int32_t target_y);
    void SetEyeEmotion(eye_t *eye, ExpressionType emotion);
    void UpdateAutoAnimation();
    
    // LVGL回调
    static bool NotifyFlushReady(esp_lcd_panel_io_handle_t panel_io, 
                                esp_lcd_panel_io_event_data_t *edata, void *user_ctx);
    static void FlushCallback(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map);
    static void IncreaseLVGLTick(void *arg);
    static void LVGLTask(void *arg);
    static void AnimationTimerCallback(lv_timer_t *timer);
    
    // 互斥锁操作
    bool LockLVGL(int timeout_ms = -1);
    void UnlockLVGL();
};

/**
 * @brief 表情类型转字符串
 */
std::string ExpressionTypeToString(ExpressionType expression);

/**
 * @brief 字符串转表情类型
 */
ExpressionType StringToExpressionType(const std::string& expression_str);

/**
 * @brief 创建默认表情配置
 */
ExpressionConfig CreateDefaultExpressionConfig(ExpressionType expression);

/**
 * @brief 创建双屏表情管理器实例
 */
std::unique_ptr<DualScreenExpressionManager> CreateDualScreenExpressionManager();

} // namespace dual_screen_expression
