# 双屏表情系统 CMakeLists.txt

# 设置组件名称
set(COMPONENT_NAME "dual_screen_expression")

# 设置源文件
set(COMPONENT_SRCS
    "dual_screen_expression.cc"
    "emotion_config.cc"
    "expression_integration.cc"
    "expression_manager.cc"
    "eye_renderer.cc"
)

# 设置头文件目录
set(COMPONENT_ADD_INCLUDEDIRS
    "."
    ".."
)

# 设置依赖组件
set(COMPONENT_REQUIRES
    "esp_lcd"
    "esp_driver_spi"
    "esp_driver_gpio"
    "esp_timer"
    "freertos"
    "lvgl__lvgl"
    "espressif__esp_lcd_gc9a01"
    "log"
)

# 注册组件
register_component()

# 设置编译选项
target_compile_options(${COMPONENT_LIB} PRIVATE
    -Wno-unused-parameter
    -Wno-unused-variable
    -Wno-missing-field-initializers
)

# 设置C++标准
set_property(TARGET ${COMPONENT_LIB} PROPERTY CXX_STANDARD 17)
set_property(TARGET ${COMPONENT_LIB} PROPERTY CXX_STANDARD_REQUIRED ON)
