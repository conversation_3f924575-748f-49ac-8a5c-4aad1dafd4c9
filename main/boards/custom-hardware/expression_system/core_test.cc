/**
 * @file core_test.cc
 * @brief 核心功能测试程序 - 双屏表情系统
 */

#include "simple_dual_screen.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <memory>

static const char* TAG = "CORE_TEST";

namespace core_test {

/**
 * @brief 核心功能测试类
 */
class CoreFunctionTest {
public:
    CoreFunctionTest() = default;
    ~CoreFunctionTest() = default;

    /**
     * @brief 初始化测试
     */
    bool Initialize() {
        ESP_LOGI(TAG, "=== 核心功能测试开始 ===");
        
        // 初始化双屏表情系统
        if (!screen_manager_.Initialize()) {
            ESP_LOGE(TAG, "双屏表情系统初始化失败");
            return false;
        }
        
        ESP_LOGI(TAG, "双屏表情系统初始化成功");
        return true;
    }

    /**
     * @brief 运行表情测试
     */
    void RunExpressionTest() {
        ESP_LOGI(TAG, "=== 开始表情测试 ===");
        
        // 测试各种表情
        TestExpression(simple_dual_screen::ExpressionType::NORMAL, "正常表情", 2000);
        TestExpression(simple_dual_screen::ExpressionType::HAPPY, "开心表情", 2000);
        TestExpression(simple_dual_screen::ExpressionType::SAD, "伤心表情", 2000);
        TestExpression(simple_dual_screen::ExpressionType::ANGRY, "生气表情", 2000);
        TestExpression(simple_dual_screen::ExpressionType::SURPRISED, "惊讶表情", 2000);
        TestExpression(simple_dual_screen::ExpressionType::SLEEPY, "困倦表情", 2000);
        
        ESP_LOGI(TAG, "=== 表情测试完成 ===");
    }

    /**
     * @brief 运行眨眼测试
     */
    void RunBlinkTest() {
        ESP_LOGI(TAG, "=== 开始眨眼测试 ===");
        
        // 显示正常表情
        screen_manager_.SetExpression(simple_dual_screen::ExpressionType::NORMAL);
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 测试眨眼动画
        for (int i = 0; i < 5; i++) {
            ESP_LOGI(TAG, "眨眼测试 %d/5", i + 1);
            screen_manager_.Blink(true);
            vTaskDelay(pdMS_TO_TICKS(1500));
        }
        
        ESP_LOGI(TAG, "=== 眨眼测试完成 ===");
    }

    /**
     * @brief 运行循环测试
     */
    void RunContinuousTest() {
        ESP_LOGI(TAG, "=== 开始循环测试 ===");
        
        const simple_dual_screen::ExpressionType expressions[] = {
            simple_dual_screen::ExpressionType::NORMAL,
            simple_dual_screen::ExpressionType::HAPPY,
            simple_dual_screen::ExpressionType::SAD,
            simple_dual_screen::ExpressionType::ANGRY,
            simple_dual_screen::ExpressionType::SURPRISED,
            simple_dual_screen::ExpressionType::SLEEPY
        };
        
        const char* expression_names[] = {
            "正常", "开心", "伤心", "生气", "惊讶", "困倦"
        };
        
        int expression_count = sizeof(expressions) / sizeof(expressions[0]);
        
        for (int cycle = 0; cycle < 3; cycle++) {
            ESP_LOGI(TAG, "循环测试 第%d轮", cycle + 1);
            
            for (int i = 0; i < expression_count; i++) {
                ESP_LOGI(TAG, "显示%s表情", expression_names[i]);
                screen_manager_.SetExpression(expressions[i]);
                vTaskDelay(pdMS_TO_TICKS(1500));
                
                // 随机眨眼
                if (i % 2 == 0) {
                    screen_manager_.Blink(true);
                    vTaskDelay(pdMS_TO_TICKS(500));
                }
            }
        }
        
        ESP_LOGI(TAG, "=== 循环测试完成 ===");
    }

private:
    simple_dual_screen::SimpleDualScreenManager screen_manager_;

    /**
     * @brief 测试单个表情
     */
    void TestExpression(simple_dual_screen::ExpressionType expression, const char* name, uint32_t duration_ms) {
        ESP_LOGI(TAG, "测试%s", name);
        screen_manager_.SetExpression(expression);
        vTaskDelay(pdMS_TO_TICKS(duration_ms));
    }
};

// 全局测试实例
static std::unique_ptr<CoreFunctionTest> g_test_instance = nullptr;

/**
 * @brief 初始化核心功能测试
 */
bool InitializeCoreTest() {
    g_test_instance = std::make_unique<CoreFunctionTest>();
    return g_test_instance->Initialize();
}

/**
 * @brief 运行基础测试
 */
void RunBasicTest() {
    if (g_test_instance) {
        g_test_instance->RunExpressionTest();
    }
}

/**
 * @brief 运行眨眼测试
 */
void RunBlinkTest() {
    if (g_test_instance) {
        g_test_instance->RunBlinkTest();
    }
}

/**
 * @brief 运行循环测试
 */
void RunContinuousTest() {
    if (g_test_instance) {
        g_test_instance->RunContinuousTest();
    }
}

/**
 * @brief 运行完整测试套件
 */
void RunFullTestSuite() {
    ESP_LOGI(TAG, "=== 开始完整测试套件 ===");
    
    RunBasicTest();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    RunBlinkTest();
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    RunContinuousTest();
    
    ESP_LOGI(TAG, "=== 完整测试套件完成 ===");
}

} // namespace core_test
