#include "simple_dual_screen.h"
#include "esp_lcd_gc9a01.h"
#include "driver/spi_master.h"
#include "driver/gpio.h"
#include "esp_heap_caps.h"

namespace simple_dual_screen {

static const char* TAG = "SimpleDualScreen";

SimpleDualScreenManager::SimpleDualScreenManager()
    : left_io_handle_(nullptr)
    , right_io_handle_(nullptr)
    , left_panel_handle_(nullptr)
    , right_panel_handle_(nullptr)
    , left_display_(nullptr)
    , right_display_(nullptr)
    , left_buf1_(nullptr)
    , left_buf2_(nullptr)
    , right_buf1_(nullptr)
    , right_buf2_(nullptr)
    , initialized_(false)
    , current_expression_(ExpressionType::NORMAL)
    , current_brightness_(255)
    , animation_timer_(nullptr)
    , blink_timer_(nullptr)
    , animation_running_(false)
    , current_animation_(AnimationType::BLINK)
    , left_eye_container_(nullptr)
    , right_eye_container_(nullptr)
    , left_pupil_(nullptr)
    , right_pupil_(nullptr) {
}

SimpleDualScreenManager::~SimpleDualScreenManager() {
    if (initialized_) {
        EmergencyStop();
        
        // 清理LVGL对象
        if (left_display_) lv_disp_remove(left_display_);
        if (right_display_) lv_disp_remove(right_display_);
        
        // 清理显示缓冲区
        if (left_buf1_) heap_caps_free(left_buf1_);
        if (left_buf2_) heap_caps_free(left_buf2_);
        if (right_buf1_) heap_caps_free(right_buf1_);
        if (right_buf2_) heap_caps_free(right_buf2_);
        
        // 清理LCD面板
        if (left_panel_handle_) esp_lcd_panel_del(left_panel_handle_);
        if (right_panel_handle_) esp_lcd_panel_del(right_panel_handle_);
        if (left_io_handle_) esp_lcd_panel_io_del(left_io_handle_);
        if (right_io_handle_) esp_lcd_panel_io_del(right_io_handle_);
    }
}

bool SimpleDualScreenManager::Initialize() {
    ESP_LOGI(TAG, "Initializing simple dual screen system...");
    
    if (initialized_) {
        ESP_LOGW(TAG, "Already initialized");
        return true;
    }
    
    // 初始化SPI总线
    if (!InitializeSPIBus()) {
        ESP_LOGE(TAG, "Failed to initialize SPI bus");
        return false;
    }

    // 先进行全局RST复位，避免两个屏幕初始化时的冲突
    ESP_LOGI(TAG, "Performing global reset for both screens...");
    gpio_config_t rst_config = {};
    rst_config.pin_bit_mask = (1ULL << SIMPLE_PIN_RST);
    rst_config.mode = GPIO_MODE_OUTPUT;
    rst_config.pull_up_en = GPIO_PULLUP_DISABLE;
    rst_config.pull_down_en = GPIO_PULLDOWN_DISABLE;
    rst_config.intr_type = GPIO_INTR_DISABLE;
    gpio_config(&rst_config);

    // 执行全局复位
    gpio_set_level(static_cast<gpio_num_t>(SIMPLE_PIN_RST), 0);
    vTaskDelay(pdMS_TO_TICKS(10));
    gpio_set_level(static_cast<gpio_num_t>(SIMPLE_PIN_RST), 1);
    vTaskDelay(pdMS_TO_TICKS(120));

    // 初始化左屏（不再执行单独的reset）
    if (!InitializeScreen(&left_io_handle_, &left_panel_handle_, &left_display_,
                         SIMPLE_PIN_CS_LEFT, "Left Screen", false)) {
        ESP_LOGE(TAG, "Failed to initialize left screen");
        return false;
    }

    // 初始化右屏（不再执行单独的reset）
    if (!InitializeScreen(&right_io_handle_, &right_panel_handle_, &right_display_,
                         SIMPLE_PIN_CS_RIGHT, "Right Screen", false)) {
        ESP_LOGE(TAG, "Failed to initialize right screen");
        return false;
    }
    
    // 创建眼睛UI
    DrawEye(left_display_, ExpressionType::NORMAL, true);
    DrawEye(right_display_, ExpressionType::NORMAL, false);
    
    // 启动眨眼定时器
    blink_timer_ = lv_timer_create(BlinkTimerCallback, 3000, this);
    
    initialized_ = true;
    ESP_LOGI(TAG, "Simple dual screen system initialized successfully");
    return true;
}

bool SimpleDualScreenManager::InitializeSPIBus() {
    ESP_LOGI(TAG, "Initializing SPI bus...");
    
    spi_bus_config_t bus_config = {};
    bus_config.mosi_io_num = SIMPLE_PIN_MOSI;
    bus_config.miso_io_num = -1;  // 不使用MISO
    bus_config.sclk_io_num = SIMPLE_PIN_SCK;
    bus_config.quadwp_io_num = -1;
    bus_config.quadhd_io_num = -1;
    bus_config.max_transfer_sz = SIMPLE_SCREEN_WIDTH * SIMPLE_BUFFER_HEIGHT * sizeof(uint16_t);
    
    esp_err_t ret = spi_bus_initialize(SPI2_HOST, &bus_config, SPI_DMA_CH_AUTO);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPI bus: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGI(TAG, "SPI bus initialized successfully");
    return true;
}

bool SimpleDualScreenManager::InitializeScreen(esp_lcd_panel_io_handle_t* io_handle,
                                              esp_lcd_panel_handle_t* panel_handle,
                                              lv_disp_t** display,
                                              int cs_pin, const char* name, bool perform_reset) {
    ESP_LOGI(TAG, "Initializing %s (CS: GPIO%d)...", name, cs_pin);
    
    // 配置LCD面板IO
    esp_lcd_panel_io_spi_config_t io_config = {};
    io_config.cs_gpio_num = cs_pin;
    io_config.dc_gpio_num = SIMPLE_PIN_DC;
    io_config.spi_mode = 0;
    io_config.pclk_hz = 40 * 1000 * 1000;  // 40MHz
    io_config.trans_queue_depth = 10;
    io_config.lcd_cmd_bits = 8;
    io_config.lcd_param_bits = 8;
    
    esp_err_t ret = esp_lcd_new_panel_io_spi(SPI2_HOST, &io_config, io_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create panel IO for %s: %s", name, esp_err_to_name(ret));
        return false;
    }
    
    // 配置LCD面板
    esp_lcd_panel_dev_config_t panel_config = {};
    panel_config.reset_gpio_num = perform_reset ? SIMPLE_PIN_RST : -1;  // 如果不执行reset，设为-1
    panel_config.rgb_ele_order = LCD_RGB_ELEMENT_ORDER_BGR;
    panel_config.bits_per_pixel = 16;

    ret = esp_lcd_new_panel_gc9a01(*io_handle, &panel_config, panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create panel for %s: %s", name, esp_err_to_name(ret));
        return false;
    }

    // 初始化面板（跳过reset，因为已经全局reset过了）
    if (perform_reset) {
        esp_lcd_panel_reset(*panel_handle);
    }
    esp_lcd_panel_init(*panel_handle);
    esp_lcd_panel_disp_on_off(*panel_handle, true);
    
    // 创建LVGL显示对象
    *display = lv_display_create(SIMPLE_SCREEN_WIDTH, SIMPLE_SCREEN_HEIGHT);
    if (*display == nullptr) {
        ESP_LOGE(TAG, "Failed to create LVGL display for %s", name);
        return false;
    }
    
    // 创建显示缓冲区
    void* buf1 = nullptr;
    void* buf2 = nullptr;
    if (!CreateDisplayBuffer(*display, &buf1, &buf2)) {
        ESP_LOGE(TAG, "Failed to create display buffer for %s", name);
        return false;
    }
    
    // 保存缓冲区指针
    if (cs_pin == SIMPLE_PIN_CS_LEFT) {
        left_buf1_ = buf1;
        left_buf2_ = buf2;
    } else {
        right_buf1_ = buf1;
        right_buf2_ = buf2;
    }

    // 设置LVGL显示驱动
    // 新版LVGL缓冲区设置
    lv_draw_buf_t* draw_buf1 = lv_draw_buf_create(240, 240, LV_COLOR_FORMAT_RGB565, 0);
    lv_draw_buf_t* draw_buf2 = lv_draw_buf_create(240, 240, LV_COLOR_FORMAT_RGB565, 0);
    lv_display_set_draw_buffers(*display, draw_buf1, draw_buf2);
    lv_display_set_flush_cb(*display, FlushCallback);
    lv_display_set_user_data(*display, *panel_handle);
    
    ESP_LOGI(TAG, "%s initialized successfully", name);
    return true;
}

bool SimpleDualScreenManager::CreateDisplayBuffer(lv_disp_t* display, void** buf1, void** buf2) {
    // 检查可用内存
    size_t free_heap = heap_caps_get_free_size(MALLOC_CAP_DMA | MALLOC_CAP_INTERNAL);
    size_t required_memory = SIMPLE_BUFFER_SIZE * 2;

    ESP_LOGI(TAG, "Creating display buffers: %zu bytes each, %zu total. Free DMA heap: %zu bytes",
             SIMPLE_BUFFER_SIZE, required_memory, free_heap);

    if (free_heap < required_memory + 10240) {  // 保留10KB余量
        ESP_LOGE(TAG, "Insufficient DMA memory. Required: %zu, Available: %zu", required_memory, free_heap);
        return false;
    }

    *buf1 = heap_caps_malloc(SIMPLE_BUFFER_SIZE, MALLOC_CAP_DMA | MALLOC_CAP_INTERNAL);
    if (*buf1 == nullptr) {
        ESP_LOGE(TAG, "Failed to allocate first display buffer");
        return false;
    }

    *buf2 = heap_caps_malloc(SIMPLE_BUFFER_SIZE, MALLOC_CAP_DMA | MALLOC_CAP_INTERNAL);
    if (*buf2 == nullptr) {
        ESP_LOGE(TAG, "Failed to allocate second display buffer");
        heap_caps_free(*buf1);
        *buf1 = nullptr;
        return false;
    }

    ESP_LOGI(TAG, "Display buffers allocated successfully");
    return true;
}

void SimpleDualScreenManager::FlushCallback(lv_display_t* disp, const lv_area_t* area, uint8_t* px_map) {
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t)lv_display_get_user_data(disp);

    int offsetx1 = area->x1;
    int offsety1 = area->y1;
    int offsetx2 = area->x2;
    int offsety2 = area->y2;

    esp_lcd_panel_draw_bitmap(panel_handle, offsetx1, offsety1, offsetx2 + 1, offsety2 + 1, px_map);
    lv_disp_flush_ready(disp);
}

bool SimpleDualScreenManager::SetExpression(ExpressionType expression) {
    if (!initialized_) {
        ESP_LOGW(TAG, "Not initialized");
        return false;
    }
    
    ESP_LOGI(TAG, "Setting expression: %d", (int)expression);
    current_expression_ = expression;
    
    // 重新绘制眼睛
    DrawEye(left_display_, expression, true);
    DrawEye(right_display_, expression, false);
    
    return true;
}

void SimpleDualScreenManager::DrawEye(lv_disp_t* display, ExpressionType expression, bool is_left_eye) {
    // 获取屏幕对象
    lv_obj_t* screen = lv_disp_get_scr_act(display);
    
    // 清除之前的内容
    lv_obj_clean(screen);
    
    // 设置背景色
    lv_obj_set_style_bg_color(screen, lv_color_black(), 0);
    
    // 创建眼睛容器
    lv_obj_t* eye_container = lv_obj_create(screen);
    lv_obj_set_size(eye_container, SIMPLE_SCREEN_WIDTH, SIMPLE_SCREEN_HEIGHT);
    lv_obj_center(eye_container);
    lv_obj_set_style_bg_opa(eye_container, LV_OPA_TRANSP, 0);
    lv_obj_set_style_border_opa(eye_container, LV_OPA_TRANSP, 0);

    // 绘制简单眼睛
    DrawSimpleEye(eye_container, SIMPLE_SCREEN_WIDTH/2, SIMPLE_SCREEN_HEIGHT/2, 80, expression);
    
    // 保存容器引用
    if (is_left_eye) {
        left_eye_container_ = eye_container;
    } else {
        right_eye_container_ = eye_container;
    }
}

void SimpleDualScreenManager::DrawSimpleEye(lv_obj_t* parent, int center_x, int center_y, int radius, ExpressionType expression) {
    // 创建眼白
    lv_obj_t* eyeball = lv_obj_create(parent);
    lv_obj_set_size(eyeball, radius * 2, radius * 2);
    lv_obj_set_pos(eyeball, center_x - radius, center_y - radius);
    lv_obj_set_style_bg_color(eyeball, lv_color_white(), 0);
    lv_obj_set_style_radius(eyeball, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_border_opa(eyeball, LV_OPA_TRANSP, 0);
    
    // 创建瞳孔
    int pupil_radius = radius / 3;
    lv_obj_t* pupil = lv_obj_create(eyeball);
    lv_obj_set_size(pupil, pupil_radius * 2, pupil_radius * 2);
    lv_obj_center(pupil);
    lv_obj_set_style_bg_color(pupil, lv_color_black(), 0);
    lv_obj_set_style_radius(pupil, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_border_opa(pupil, LV_OPA_TRANSP, 0);
    
    // 根据表情调整眼睛形状
    switch (expression) {
        case ExpressionType::HAPPY:
            // 开心时眼睛稍微眯起
            lv_obj_set_height(eyeball, radius * 1.5);
            break;
        case ExpressionType::SAD:
            // 伤心时瞳孔向下
            lv_obj_set_pos(pupil, pupil_radius, pupil_radius + 10);
            break;
        case ExpressionType::SURPRISED:
            // 惊讶时眼睛变大
            lv_obj_set_size(eyeball, radius * 2.2, radius * 2.2);
            lv_obj_set_pos(eyeball, center_x - radius * 1.1, center_y - radius * 1.1);
            break;
        case ExpressionType::SLEEPY:
            // 困倦时眼睛几乎闭合
            lv_obj_set_height(eyeball, radius * 0.3);
            break;
        default:
            // 正常表情
            break;
    }
}

bool SimpleDualScreenManager::Blink(bool force) {
    if (!initialized_) return false;
    
    ESP_LOGI(TAG, "Blinking...");
    
    // 简单的眨眼动画：快速闭眼再睁眼
    SetExpression(ExpressionType::SLEEPY);
    vTaskDelay(pdMS_TO_TICKS(150));
    SetExpression(current_expression_);
    
    return true;
}

void SimpleDualScreenManager::BlinkTimerCallback(lv_timer_t* timer) {
    SimpleDualScreenManager* manager = static_cast<SimpleDualScreenManager*>(lv_timer_get_user_data(timer));
    if (manager && manager->IsInitialized()) {
        manager->Blink(false);
    }
}

bool SimpleDualScreenManager::PlayAnimation(AnimationType animation) {
    if (!initialized_) return false;
    
    ESP_LOGI(TAG, "Playing animation: %d", (int)animation);
    current_animation_ = animation;
    
    // 简化的动画实现
    switch (animation) {
        case AnimationType::BLINK:
            return Blink(true);
        case AnimationType::LOOK_LEFT:
        case AnimationType::LOOK_RIGHT:
        case AnimationType::LOOK_UP:
        case AnimationType::LOOK_DOWN:
            // 简单的看向动画
            vTaskDelay(pdMS_TO_TICKS(500));
            return true;
        default:
            return false;
    }
}

bool SimpleDualScreenManager::LookAt(int32_t x, int32_t y) {
    if (!initialized_) return false;
    ESP_LOGI(TAG, "Looking at (%ld, %ld)", x, y);
    return true;
}

bool SimpleDualScreenManager::SetBrightness(uint8_t brightness) {
    if (!initialized_) return false;
    
    current_brightness_ = brightness;
    ESP_LOGI(TAG, "Setting brightness to %d", brightness);
    
    // 这里可以通过PWM控制背光
    // 暂时只记录亮度值
    return true;
}

void SimpleDualScreenManager::EmergencyStop() {
    ESP_LOGI(TAG, "Emergency stop");
    
    if (animation_timer_) {
        lv_timer_del(animation_timer_);
        animation_timer_ = nullptr;
    }
    
    if (blink_timer_) {
        lv_timer_del(blink_timer_);
        blink_timer_ = nullptr;
    }
    
    animation_running_ = false;
    
    if (initialized_) {
        SetExpression(ExpressionType::NORMAL);
    }
}

std::unique_ptr<SimpleDualScreenManager> CreateSimpleDualScreenManager() {
    return std::make_unique<SimpleDualScreenManager>();
}

} // namespace simple_dual_screen
