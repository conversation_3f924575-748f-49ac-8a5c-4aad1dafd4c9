/**
 * @file core_test.h
 * @brief 核心功能测试程序头文件
 */

#ifndef CORE_TEST_H
#define CORE_TEST_H

namespace core_test {

/**
 * @brief 初始化核心功能测试
 * @return true 初始化成功，false 初始化失败
 */
bool InitializeCoreTest();

/**
 * @brief 运行基础表情测试
 */
void RunBasicTest();

/**
 * @brief 运行眨眼测试
 */
void RunBlinkTest();

/**
 * @brief 运行循环测试
 */
void RunContinuousTest();

/**
 * @brief 运行完整测试套件
 */
void RunFullTestSuite();

} // namespace core_test

#endif // CORE_TEST_H
