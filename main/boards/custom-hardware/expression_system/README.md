# 表情显示系统

## 📖 概述

双屏表情显示系统使用两块GC9A01圆形显示屏，实现机器人的眼部表情显示。

## 🔧 硬件配置

### 显示屏规格
- **型号**: GC9A01 (240x240圆形显示屏)
- **数量**: 2块 (左眼 + 右眼)
- **接口**: SPI

### 引脚连接
```
共享引脚:
- SCK:  GPIO19
- MOSI: GPIO20  
- DC:   GPIO21
- RST:  GPIO1

独立引脚:
- 左屏CS:  GPIO2
- 右屏CS:  GPIO45
- 背光:    GPIO42 (PWM控制)
```

## 💻 软件架构

### 核心类
- `SimpleDualScreenManager`: 双屏管理器
- `ExpressionRenderer`: 表情渲染器

### 主要功能
1. **双屏初始化**: 独立初始化两块显示屏
2. **表情渲染**: 支持基础表情 (开心、伤心、生气等)
3. **同步显示**: 左右眼协调显示
4. **LVGL集成**: 兼容LVGL 9.x版本

## 🎨 表情类型

### 基础表情
- `HAPPY`: 开心 (眯眼笑)
- `SAD`: 伤心 (下垂眼)
- `ANGRY`: 生气 (皱眉)
- `SURPRISED`: 惊讶 (圆眼)
- `SLEEPY`: 困倦 (半闭眼)
- `NORMAL`: 正常 (标准眼型)

### 动画效果
- 眨眼动画
- 眼球转动
- 表情切换过渡

## 🔄 使用示例

```cpp
#include "simple_dual_screen.h"

// 初始化双屏系统
SimpleDualScreenManager screen_manager;
if (!screen_manager.Initialize()) {
    ESP_LOGE(TAG, "双屏初始化失败");
    return;
}

// 显示开心表情
screen_manager.ShowExpression(ExpressionType::HAPPY);

// 显示自定义表情
screen_manager.ShowCustomExpression("左眼眨眼", "右眼正常");
```

## 📊 性能参数

- **刷新率**: 30 FPS
- **缓冲区**: 双缓冲 (60行高度)
- **内存占用**: ~115KB (双屏缓冲区)
- **SPI频率**: 40MHz

## 🐛 故障排除

### 常见问题
1. **显示花屏**: 检查SPI连接和电源
2. **单屏不显示**: 检查对应CS引脚
3. **颜色异常**: 确认RGB顺序配置
4. **闪烁**: 检查背光PWM配置

### 调试方法
```cpp
// 启用调试日志
#define SIMPLE_DUAL_SCREEN_DEBUG 1

// 测试单屏显示
screen_manager.TestSingleScreen(ScreenSide::LEFT);
screen_manager.TestSingleScreen(ScreenSide::RIGHT);
```

## 🔮 未来扩展

- [ ] 更多表情类型
- [ ] 复杂动画序列
- [ ] 表情配置文件
- [ ] 实时表情生成
- [ ] 语音同步表情
