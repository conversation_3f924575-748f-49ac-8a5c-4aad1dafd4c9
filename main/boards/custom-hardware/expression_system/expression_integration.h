/**
 * @file expression_integration.h
 * @brief 表情系统集成 - 整合双屏优化方案与现有系统
 *
 * 提供统一的表情控制接口，内部选择最优的实现方案
 * 解决针脚共用问题，支持平滑的表情切换和动画效果
 * 基于main_lvgl_proper的成功经验，优化双屏初始化和针脚管理
 */

#pragma once
#include "expression_manager.h"
#include "dual_screen_expression.h"
#include <string>
#include <memory>
#include <functional>

namespace expression_integration {

/**
 * @brief 表情系统实现类型
 */
enum class ImplementationType {
    DUAL_SCREEN_OPTIMIZED,  // 双屏优化方案（推荐）
    LEGACY_SYSTEM,          // 原有系统
    AUTO_SELECT             // 自动选择最佳方案
};

/**
 * @brief 表情系统配置
 */
struct ExpressionSystemConfig {
    ImplementationType impl_type;           // 实现类型
    bool enable_pin_sharing_optimization;  // 启用针脚共用优化
    bool enable_auto_animation;            // 启用自动动画
    uint8_t animation_speed;               // 动画速度 (1-100)
    uint8_t brightness;                    // 屏幕亮度 (1-100)
    
    ExpressionSystemConfig() :
        impl_type(ImplementationType::AUTO_SELECT),
        enable_pin_sharing_optimization(true),
        enable_auto_animation(true),
        animation_speed(50),
        brightness(80) {}
};

/**
 * @brief 统一表情控制器
 */
class UnifiedExpressionController {
public:
    /**
     * @brief 构造函数
     */
    UnifiedExpressionController();
    
    /**
     * @brief 析构函数
     */
    ~UnifiedExpressionController();
    
    /**
     * @brief 初始化表情系统
     * @param config 系统配置
     * @return 是否成功
     */
    bool Initialize(const ExpressionSystemConfig& config = ExpressionSystemConfig());
    
    /**
     * @brief 设置表情
     * @param emotion 表情类型
     * @return 是否成功
     */
    bool SetEmotion(expression_system::EmotionType emotion);
    
    /**
     * @brief 播放动画
     * @param animation 动画类型
     * @return 是否成功
     */
    bool PlayAnimation(expression_system::AnimationType animation);
    
    /**
     * @brief 眨眼
     * @param both_eyes 是否双眼同时眨眼
     * @return 是否成功
     */
    bool Blink(bool both_eyes = true);
    
    /**
     * @brief 眼球移动
     * @param target_x 目标X位置
     * @param target_y 目标Y位置
     * @return 是否成功
     */
    bool LookAt(int32_t target_x, int32_t target_y);
    
    /**
     * @brief 跟随目标
     * @param target_x 目标X位置
     * @param target_y 目标Y位置
     * @return 是否成功
     */
    bool FollowTarget(int32_t target_x, int32_t target_y);
    
    /**
     * @brief 设置亮度
     * @param brightness 亮度值 (1-100)
     * @return 是否成功
     */
    bool SetBrightness(uint8_t brightness);
    
    /**
     * @brief 启用/禁用自动动画
     * @param enable 是否启用
     */
    void EnableAutoAnimation(bool enable);
    
    /**
     * @brief 获取当前表情
     * @return 当前表情类型
     */
    expression_system::EmotionType GetCurrentEmotion() const;
    
    /**
     * @brief 获取当前实现类型
     * @return 实现类型
     */
    ImplementationType GetImplementationType() const;
    
    /**
     * @brief 检查是否初始化
     * @return 是否已初始化
     */
    bool IsInitialized() const { return initialized_; }
    
    /**
     * @brief 设置表情变化回调
     * @param callback 回调函数
     */
    void SetExpressionChangeCallback(std::function<void(expression_system::EmotionType, bool)> callback);
    
    /**
     * @brief 执行表情序列
     * @param emotions 表情序列
     * @param durations 每个表情的持续时间（毫秒）
     * @return 是否成功
     */
    bool PlayExpressionSequence(const std::vector<expression_system::EmotionType>& emotions,
                               const std::vector<uint32_t>& durations);
    
    /**
     * @brief 设置表情过渡时间
     * @param transition_ms 过渡时间（毫秒）
     */
    void SetTransitionTime(uint32_t transition_ms);
    
    /**
     * @brief 紧急停止所有动画
     */
    void EmergencyStop();

private:
    // 实现选择
    ImplementationType current_impl_;
    bool initialized_;
    
    // 双屏优化实现
    std::unique_ptr<dual_screen_expression::DualScreenExpressionManager> dual_screen_impl_;
    
    // 原有系统实现
    std::unique_ptr<expression_system::ExpressionManager> legacy_impl_;
    
    // 配置和状态
    ExpressionSystemConfig config_;
    expression_system::EmotionType current_emotion_;
    std::function<void(expression_system::EmotionType, bool)> expression_callback_;
    
    // 私有方法
    ImplementationType SelectBestImplementation();
    bool InitializeDualScreenImpl();
    bool InitializeLegacyImpl();
    bool TestPinSharingCompatibility();
    
    // 类型转换
    dual_screen_expression::ExpressionType ConvertToExpressionType(expression_system::EmotionType emotion);
    expression_system::EmotionType ConvertFromExpressionType(dual_screen_expression::ExpressionType expression);
};

/**
 * @brief 表情系统工厂类
 */
class ExpressionSystemFactory {
public:
    /**
     * @brief 创建统一表情控制器
     * @param config 系统配置
     * @return 控制器实例
     */
    static std::unique_ptr<UnifiedExpressionController> CreateController(
        const ExpressionSystemConfig& config = ExpressionSystemConfig());
    
    /**
     * @brief 检测最佳实现方案
     * @return 推荐的实现类型
     */
    static ImplementationType DetectBestImplementation();
    
    /**
     * @brief 测试针脚兼容性
     * @return 是否支持针脚共用优化
     */
    static bool TestPinSharingSupport();
};

/**
 * @brief 表情系统诊断工具
 */
class ExpressionSystemDiagnostics {
public:
    /**
     * @brief 运行系统诊断
     * @return 诊断结果
     */
    static bool RunDiagnostics();
    
    /**
     * @brief 测试双屏初始化
     * @return 测试结果
     */
    static bool TestDualScreenInit();
    
    /**
     * @brief 测试表情切换
     * @return 测试结果
     */
    static bool TestExpressionSwitching();
    
    /**
     * @brief 测试动画性能
     * @return 测试结果
     */
    static bool TestAnimationPerformance();
    
    /**
     * @brief 生成诊断报告
     * @return 诊断报告字符串
     */
    static std::string GenerateDiagnosticReport();
};

/**
 * @brief 实现类型转字符串
 */
std::string ImplementationTypeToString(ImplementationType type);

/**
 * @brief 字符串转实现类型
 */
ImplementationType StringToImplementationType(const std::string& type_str);

/**
 * @brief 创建默认表情系统配置
 */
ExpressionSystemConfig CreateDefaultConfig();

/**
 * @brief 创建针脚优化配置
 */
ExpressionSystemConfig CreatePinOptimizedConfig();

} // namespace expression_integration
