/**
 * @file expression_manager.cc
 * @brief 表情管理器实现
 */

#include "expression_manager.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <cmath>

static const char* TAG = "EXPR_MGR";

namespace expression_system {

// EyeRenderer实现
EyeRenderer::EyeRenderer(lv_disp_t* display, int width, int height)
    : display_(display), screen_(nullptr), eye_container_(nullptr),
      width_(width), height_(height), initialized_(false) {
}

EyeRenderer::~EyeRenderer() {
    if (eye_container_) {
        lv_obj_del(eye_container_);
    }
}

bool EyeRenderer::Initialize() {
    if (initialized_) {
        return true;
    }

    if (!display_) {
        ESP_LOGE(TAG, "Display not provided");
        return false;
    }

    // 创建屏幕对象
    screen_ = lv_disp_get_scr_act(display_);
    if (!screen_) {
        ESP_LOGE(TAG, "Failed to get active screen");
        return false;
    }

    // 创建眼睛容器
    eye_container_ = lv_obj_create(screen_);
    lv_obj_set_size(eye_container_, width_, height_);
    lv_obj_center(eye_container_);
    lv_obj_set_style_bg_color(eye_container_, lv_color_black(), 0);
    lv_obj_set_style_border_width(eye_container_, 0, 0);

    initialized_ = true;
    ESP_LOGI(TAG, "Eye renderer initialized");

    return true;
}

bool EyeRenderer::RenderExpression(const ExpressionConfig& config, const EyeRenderParams& params) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Eye renderer not initialized");
        return false;
    }

    // 清除之前的内容
    lv_obj_clean(eye_container_);

    // 渲染虹膜
    RenderIris(config, params);

    // 渲染瞳孔
    RenderPupil(config, params);

    // 渲染高光
    RenderHighlight(config, params);

    // 渲染特效
    if (config.has_sparkle || config.has_shadow) {
        RenderSpecialEffects(config, params);
    }

    return true;
}

void EyeRenderer::RenderPupil(const ExpressionConfig& config, const EyeRenderParams& params) {
    lv_obj_t* pupil = lv_obj_create(eye_container_);
    lv_obj_set_size(pupil, params.pupil_radius * 2, params.pupil_radius * 2);
    lv_obj_set_pos(pupil, params.pupil_x - params.pupil_radius, params.pupil_y - params.pupil_radius);
    lv_obj_set_style_bg_color(pupil, config.pupil_color, 0);
    lv_obj_set_style_radius(pupil, params.pupil_radius, 0);
    lv_obj_set_style_border_width(pupil, 0, 0);
    lv_obj_set_style_opa(pupil, config.opacity, 0);
}

void EyeRenderer::RenderIris(const ExpressionConfig& config, const EyeRenderParams& params) {
    lv_obj_t* iris = lv_obj_create(eye_container_);
    lv_obj_set_size(iris, params.iris_radius * 2, params.iris_radius * 2);
    lv_obj_set_pos(iris, params.center_x - params.iris_radius, params.center_y - params.iris_radius);
    lv_obj_set_style_bg_color(iris, config.iris_color, 0);
    lv_obj_set_style_radius(iris, params.iris_radius, 0);
    lv_obj_set_style_border_width(iris, 0, 0);
    lv_obj_set_style_opa(iris, config.opacity, 0);
}

void EyeRenderer::RenderHighlight(const ExpressionConfig& config, const EyeRenderParams& params) {
    // 添加高光效果
    lv_obj_t* highlight = lv_obj_create(eye_container_);
    int highlight_size = params.pupil_radius / 3;
    lv_obj_set_size(highlight, highlight_size * 2, highlight_size * 2);
    lv_obj_set_pos(highlight, params.pupil_x - highlight_size/2, params.pupil_y - highlight_size/2);
    lv_obj_set_style_bg_color(highlight, config.highlight_color, 0);
    lv_obj_set_style_radius(highlight, highlight_size, 0);
    lv_obj_set_style_border_width(highlight, 0, 0);
    lv_obj_set_style_opa(highlight, 200, 0);
}

void EyeRenderer::RenderEyelid(const ExpressionConfig& config, const EyeRenderParams& params) {
    // TODO: 实现眼睑渲染
}

void EyeRenderer::RenderSpecialEffects(const ExpressionConfig& config, const EyeRenderParams& params) {
    if (config.has_sparkle) {
        // TODO: 添加闪烁效果
    }

    if (config.has_shadow) {
        // TODO: 添加阴影效果
    }
}

bool EyeRenderer::Clear(lv_color_t color) {
    if (!initialized_) {
        return false;
    }

    lv_obj_set_style_bg_color(eye_container_, color, 0);
    lv_obj_clean(eye_container_);

    return true;
}

void EyeRenderer::SetBrightness(uint8_t brightness) {
    if (!initialized_) {
        return;
    }

    uint8_t opacity = (brightness * 255) / 100;
    lv_obj_set_style_opa(eye_container_, opacity, 0);
}

// ExpressionManager实现
ExpressionManager& ExpressionManager::GetInstance() {
    static ExpressionManager instance;
    return instance;
}

bool ExpressionManager::Initialize(lv_disp_t* left_display, lv_disp_t* right_display) {
    if (initialized_) {
        ESP_LOGW(TAG, "Expression manager already initialized");
        return true;
    }

    ESP_LOGI(TAG, "Initializing expression manager...");

    // 创建眼睛渲染器
    if (left_display) {
        left_eye_renderer_ = std::make_unique<EyeRenderer>(left_display, 240, 240);
        if (!left_eye_renderer_->Initialize()) {
            ESP_LOGE(TAG, "Failed to initialize left eye renderer");
            return false;
        }
    }

    if (right_display) {
        right_eye_renderer_ = std::make_unique<EyeRenderer>(right_display, 240, 240);
        if (!right_eye_renderer_->Initialize()) {
            ESP_LOGE(TAG, "Failed to initialize right eye renderer");
            return false;
        }
    }

    // 初始化预定义表情
    InitializePredefinedExpressions();

    // 设置默认表情
    current_left_expression_ = predefined_expressions_[EmotionType::NEUTRAL];
    current_right_expression_ = predefined_expressions_[EmotionType::NEUTRAL];

    global_brightness_ = 80;
    last_update_time_ = esp_timer_get_time() / 1000;
    is_following_ = false;
    follow_target_x_ = 120;
    follow_target_y_ = 120;

    initialized_ = true;

    ESP_LOGI(TAG, "Expression manager initialized successfully");
    return true;
}

void ExpressionManager::InitializePredefinedExpressions() {
    // 中性表情
    ExpressionConfig neutral;
    neutral.emotion = EmotionType::NEUTRAL;
    neutral.name = "neutral";
    neutral.pupil_size = 50;
    neutral.eye_width = 80;
    neutral.eye_height = 80;
    neutral.pupil_color = lv_color_black();
    neutral.iris_color = lv_color_make(100, 100, 100);
    neutral.background_color = lv_color_white();
    neutral.highlight_color = lv_color_white();
    predefined_expressions_[EmotionType::NEUTRAL] = neutral;

    // 开心表情
    ExpressionConfig happy;
    happy.emotion = EmotionType::HAPPY;
    happy.name = "happy";
    happy.pupil_size = 60;
    happy.eye_width = 90;
    happy.eye_height = 70;
    happy.pupil_color = lv_color_black();
    happy.iris_color = lv_color_make(50, 150, 50);
    happy.background_color = lv_color_white();
    happy.highlight_color = lv_color_white();
    happy.has_sparkle = true;
    predefined_expressions_[EmotionType::HAPPY] = happy;

    // 伤心表情
    ExpressionConfig sad;
    sad.emotion = EmotionType::SAD;
    sad.name = "sad";
    sad.pupil_size = 40;
    sad.eye_width = 70;
    sad.eye_height = 90;
    sad.eye_offset_y = 10;
    sad.pupil_color = lv_color_black();
    sad.iris_color = lv_color_make(100, 100, 150);
    sad.background_color = lv_color_white();
    sad.highlight_color = lv_color_white();
    predefined_expressions_[EmotionType::SAD] = sad;

    // 跟随表情
    ExpressionConfig following;
    following.emotion = EmotionType::FOLLOWING;
    following.name = "following";
    following.pupil_size = 45;
    following.eye_width = 85;
    following.eye_height = 85;
    following.pupil_color = lv_color_black();
    following.iris_color = lv_color_make(50, 100, 200);
    following.background_color = lv_color_white();
    following.highlight_color = lv_color_white();
    following.animation = AnimationType::FOLLOW_TARGET;
    predefined_expressions_[EmotionType::FOLLOWING] = following;

    // 眨眼表情
    ExpressionConfig blink;
    blink.emotion = EmotionType::BLINK;
    blink.name = "blink";
    blink.pupil_size = 50;
    blink.eye_width = 80;
    blink.eye_height = 10; // 闭眼状态
    blink.pupil_color = lv_color_black();
    blink.iris_color = lv_color_make(100, 100, 100);
    blink.background_color = lv_color_white();
    blink.highlight_color = lv_color_white();
    blink.animation = AnimationType::BLINK;
    blink.animation_duration = 200;
    predefined_expressions_[EmotionType::BLINK] = blink;
}

bool ExpressionManager::SetExpression(EmotionType emotion, EyePosition eye, uint32_t duration) {
    if (!initialized_) {
        ESP_LOGE(TAG, "Expression manager not initialized");
        return false;
    }

    ESP_LOGI(TAG, "Setting expression: %s for %s",
             EmotionTypeToString(emotion).c_str(),
             (eye == EyePosition::LEFT) ? "left eye" :
             (eye == EyePosition::RIGHT) ? "right eye" : "both eyes");

    auto it = predefined_expressions_.find(emotion);
    if (it == predefined_expressions_.end()) {
        ESP_LOGW(TAG, "Expression not found: %s", EmotionTypeToString(emotion).c_str());
        return false;
    }

    const ExpressionConfig& config = it->second;

    if (eye == EyePosition::LEFT || eye == EyePosition::BOTH) {
        current_left_expression_ = config;
        if (config.animation != AnimationType::NONE) {
            left_animation_state_.is_playing = true;
            left_animation_state_.start_time = esp_timer_get_time() / 1000;
            left_animation_state_.duration = config.animation_duration;
            left_animation_state_.is_loop = config.loop_animation;
        }
    }

    if (eye == EyePosition::RIGHT || eye == EyePosition::BOTH) {
        current_right_expression_ = config;
        if (config.animation != AnimationType::NONE) {
            right_animation_state_.is_playing = true;
            right_animation_state_.start_time = esp_timer_get_time() / 1000;
            right_animation_state_.duration = config.animation_duration;
            right_animation_state_.is_loop = config.loop_animation;
        }
    }

    if (callback_) {
        // 假设之前的表情是中性的
        callback_->OnExpressionChanged(EmotionType::NEUTRAL, emotion);
    }

    return true;
}

bool ExpressionManager::PlayAnimation(AnimationType animation, EyePosition eye,
                                    uint32_t duration, bool loop) {
    ESP_LOGI(TAG, "Playing animation: %s", AnimationTypeToString(animation).c_str());

    if (eye == EyePosition::LEFT || eye == EyePosition::BOTH) {
        left_animation_state_.is_playing = true;
        left_animation_state_.start_time = esp_timer_get_time() / 1000;
        left_animation_state_.duration = duration;
        left_animation_state_.is_loop = loop;
        current_left_expression_.animation = animation;
    }

    if (eye == EyePosition::RIGHT || eye == EyePosition::BOTH) {
        right_animation_state_.is_playing = true;
        right_animation_state_.start_time = esp_timer_get_time() / 1000;
        right_animation_state_.duration = duration;
        right_animation_state_.is_loop = loop;
        current_right_expression_.animation = animation;
    }

    return true;
}

bool ExpressionManager::Blink(EyePosition eye, int count, uint32_t speed) {
    ESP_LOGI(TAG, "Blinking %d times", count);

    // 简单的眨眼实现
    return PlayAnimation(AnimationType::BLINK, eye, speed * count, false);
}

bool ExpressionManager::FollowTarget(int target_x, int target_y, EyePosition eye) {
    follow_target_x_ = target_x;
    follow_target_y_ = target_y;
    is_following_ = true;

    ESP_LOGI(TAG, "Following target at (%d, %d)", target_x, target_y);

    return SetExpression(EmotionType::FOLLOWING, eye);
}

void ExpressionManager::Update() {
    if (!initialized_) {
        return;
    }

    uint32_t current_time = esp_timer_get_time() / 1000;

    // 更新动画
    UpdateAnimations();

    // 渲染眼睛
    RenderEyes();

    last_update_time_ = current_time;
}

void ExpressionManager::UpdateAnimations() {
    uint32_t current_time = esp_timer_get_time() / 1000;

    // 更新左眼动画
    if (left_animation_state_.is_playing) {
        left_animation_state_.current_time = current_time;
        uint32_t elapsed = current_time - left_animation_state_.start_time;

        if (elapsed >= left_animation_state_.duration) {
            if (left_animation_state_.is_loop) {
                left_animation_state_.start_time = current_time;
                left_animation_state_.loop_count++;
            } else {
                left_animation_state_.is_playing = false;
                if (callback_) {
                    callback_->OnAnimationFinished(current_left_expression_.animation, EyePosition::LEFT);
                }
            }
        }

        left_animation_state_.progress = (float)elapsed / left_animation_state_.duration;
        if (left_animation_state_.progress > 1.0f) {
            left_animation_state_.progress = 1.0f;
        }
    }

    // 更新右眼动画（类似逻辑）
    if (right_animation_state_.is_playing) {
        right_animation_state_.current_time = current_time;
        uint32_t elapsed = current_time - right_animation_state_.start_time;

        if (elapsed >= right_animation_state_.duration) {
            if (right_animation_state_.is_loop) {
                right_animation_state_.start_time = current_time;
                right_animation_state_.loop_count++;
            } else {
                right_animation_state_.is_playing = false;
                if (callback_) {
                    callback_->OnAnimationFinished(current_right_expression_.animation, EyePosition::RIGHT);
                }
            }
        }

        right_animation_state_.progress = (float)elapsed / right_animation_state_.duration;
        if (right_animation_state_.progress > 1.0f) {
            right_animation_state_.progress = 1.0f;
        }
    }
}

void ExpressionManager::RenderEyes() {
    // 渲染左眼
    if (left_eye_renderer_) {
        EyeRenderParams left_params;
        left_params.center_x = 120;
        left_params.center_y = 120;
        left_params.pupil_radius = current_left_expression_.pupil_size / 2;
        left_params.iris_radius = current_left_expression_.eye_width / 2;

        // 应用动画效果
        if (left_animation_state_.is_playing) {
            left_params = left_eye_renderer_->UpdateAnimation(left_animation_state_, current_left_expression_);
        }

        left_eye_renderer_->RenderExpression(current_left_expression_, left_params);
    }

    // 渲染右眼
    if (right_eye_renderer_) {
        EyeRenderParams right_params;
        right_params.center_x = 120;
        right_params.center_y = 120;
        right_params.pupil_radius = current_right_expression_.pupil_size / 2;
        right_params.iris_radius = current_right_expression_.eye_width / 2;

        // 应用动画效果
        if (right_animation_state_.is_playing) {
            right_params = right_eye_renderer_->UpdateAnimation(right_animation_state_, current_right_expression_);
        }

        right_eye_renderer_->RenderExpression(current_right_expression_, right_params);
    }
}

EyeRenderParams EyeRenderer::UpdateAnimation(const AnimationState& animation_state,
                                           const ExpressionConfig& config) {
    EyeRenderParams params;
    params.center_x = 120;
    params.center_y = 120;
    params.pupil_radius = config.pupil_size / 2;
    params.iris_radius = config.eye_width / 2;

    switch (config.animation) {
        case AnimationType::BLINK:
            params.scale_y = CalculateBlinkAnimation(animation_state.progress);
            break;

        case AnimationType::PULSE:
            params.scale_x = params.scale_y = CalculatePulseAnimation(animation_state.progress);
            break;

        case AnimationType::FOLLOW_TARGET:
            // TODO: 实现跟随动画
            break;

        default:
            break;
    }

    return params;
}

float EyeRenderer::CalculateBlinkAnimation(float progress) {
    // 眨眼动画：0 -> 1 -> 0
    if (progress < 0.5f) {
        return 1.0f - (progress * 2.0f);
    } else {
        return (progress - 0.5f) * 2.0f;
    }
}

float EyeRenderer::CalculatePulseAnimation(float progress) {
    // 脉冲动画：使用正弦波
    return 1.0f + 0.2f * sinf(progress * 2 * M_PI);
}

void ExpressionManager::SetBrightness(uint8_t brightness) {
    global_brightness_ = brightness;

    if (left_eye_renderer_) {
        left_eye_renderer_->SetBrightness(brightness);
    }

    if (right_eye_renderer_) {
        right_eye_renderer_->SetBrightness(brightness);
    }
}

void ExpressionManager::SetCallback(std::shared_ptr<ExpressionCallback> callback) {
    callback_ = callback;
}

EmotionType ExpressionManager::GetCurrentExpression(EyePosition eye) const {
    if (eye == EyePosition::LEFT) {
        return current_left_expression_.emotion;
    } else if (eye == EyePosition::RIGHT) {
        return current_right_expression_.emotion;
    } else {
        return current_left_expression_.emotion; // 默认返回左眼
    }
}

ExpressionConfig ExpressionManager::GetExpressionConfig(EmotionType emotion) const {
    auto it = predefined_expressions_.find(emotion);
    if (it != predefined_expressions_.end()) {
        return it->second;
    }

    return predefined_expressions_.at(EmotionType::NEUTRAL);
}

// 辅助函数实现
std::string EmotionTypeToString(EmotionType emotion) {
    switch (emotion) {
        case EmotionType::HAPPY: return "HAPPY";
        case EmotionType::SAD: return "SAD";
        case EmotionType::ANGRY: return "ANGRY";
        case EmotionType::SURPRISED: return "SURPRISED";
        case EmotionType::CONFUSED: return "CONFUSED";
        case EmotionType::SLEEPY: return "SLEEPY";
        case EmotionType::FOCUSED: return "FOCUSED";
        case EmotionType::CURIOUS: return "CURIOUS";
        case EmotionType::LOVE: return "LOVE";
        case EmotionType::NEUTRAL: return "NEUTRAL";
        case EmotionType::FOLLOWING: return "FOLLOWING";
        case EmotionType::THINKING: return "THINKING";
        case EmotionType::EXCITED: return "EXCITED";
        case EmotionType::WORRIED: return "WORRIED";
        case EmotionType::BLINK: return "BLINK";
        case EmotionType::CUSTOM: return "CUSTOM";
        default: return "UNKNOWN";
    }
}

EmotionType StringToEmotionType(const std::string& emotion_str) {
    if (emotion_str == "HAPPY") return EmotionType::HAPPY;
    if (emotion_str == "SAD") return EmotionType::SAD;
    if (emotion_str == "ANGRY") return EmotionType::ANGRY;
    if (emotion_str == "SURPRISED") return EmotionType::SURPRISED;
    if (emotion_str == "CONFUSED") return EmotionType::CONFUSED;
    if (emotion_str == "SLEEPY") return EmotionType::SLEEPY;
    if (emotion_str == "FOCUSED") return EmotionType::FOCUSED;
    if (emotion_str == "CURIOUS") return EmotionType::CURIOUS;
    if (emotion_str == "LOVE") return EmotionType::LOVE;
    if (emotion_str == "NEUTRAL") return EmotionType::NEUTRAL;
    if (emotion_str == "FOLLOWING") return EmotionType::FOLLOWING;
    if (emotion_str == "THINKING") return EmotionType::THINKING;
    if (emotion_str == "EXCITED") return EmotionType::EXCITED;
    if (emotion_str == "WORRIED") return EmotionType::WORRIED;
    if (emotion_str == "BLINK") return EmotionType::BLINK;
    if (emotion_str == "CUSTOM") return EmotionType::CUSTOM;
    return EmotionType::NEUTRAL;
}

std::string AnimationTypeToString(AnimationType animation) {
    switch (animation) {
        case AnimationType::NONE: return "NONE";
        case AnimationType::BLINK: return "BLINK";
        case AnimationType::LOOK_AROUND: return "LOOK_AROUND";
        case AnimationType::FOLLOW_TARGET: return "FOLLOW_TARGET";
        case AnimationType::PULSE: return "PULSE";
        case AnimationType::FADE: return "FADE";
        case AnimationType::ROTATE: return "ROTATE";
        case AnimationType::SCALE: return "SCALE";
        case AnimationType::CUSTOM: return "CUSTOM";
        default: return "UNKNOWN";
    }
}

AnimationType StringToAnimationType(const std::string& animation_str) {
    if (animation_str == "NONE") return AnimationType::NONE;
    if (animation_str == "BLINK") return AnimationType::BLINK;
    if (animation_str == "LOOK_AROUND") return AnimationType::LOOK_AROUND;
    if (animation_str == "FOLLOW_TARGET") return AnimationType::FOLLOW_TARGET;
    if (animation_str == "PULSE") return AnimationType::PULSE;
    if (animation_str == "FADE") return AnimationType::FADE;
    if (animation_str == "ROTATE") return AnimationType::ROTATE;
    if (animation_str == "SCALE") return AnimationType::SCALE;
    if (animation_str == "CUSTOM") return AnimationType::CUSTOM;
    return AnimationType::NONE;
}

} // namespace expression_system
