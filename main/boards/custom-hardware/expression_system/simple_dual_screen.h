#pragma once

#include <memory>
#include <functional>
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "lvgl.h"

namespace simple_dual_screen {

// 表情类型
enum class ExpressionType {
    NORMAL = 0,
    HAPPY,
    SAD,
    ANGRY,
    SURPRISED,
    SLEEPY,
    WINK_LEFT,
    WINK_RIGHT
};

// 动画类型
enum class AnimationType {
    BLINK,
    LOOK_LEFT,
    LOOK_RIGHT,
    LOOK_UP,
    LOOK_DOWN,
    LOOK_AROUND
};

// 简化的双屏表情管理器
class SimpleDualScreenManager {
public:
    SimpleDualScreenManager();
    ~SimpleDualScreenManager();

    // 初始化双屏系统
    bool Initialize();
    
    // 设置表情
    bool SetExpression(ExpressionType expression);
    
    // 播放动画
    bool PlayAnimation(AnimationType animation);
    
    // 眨眼
    bool Blink(bool force = false);
    
    // 看向指定位置
    bool LookAt(int32_t x, int32_t y);
    
    // 设置亮度
    bool SetBrightness(uint8_t brightness);
    
    // 紧急停止
    void EmergencyStop();
    
    // 获取当前表情
    ExpressionType GetCurrentExpression() const { return current_expression_; }
    
    // 检查是否已初始化
    bool IsInitialized() const { return initialized_; }

private:
    // 初始化SPI总线
    bool InitializeSPIBus();
    
    // 初始化单个屏幕
    bool InitializeScreen(esp_lcd_panel_io_handle_t* io_handle, 
                         esp_lcd_panel_handle_t* panel_handle,
                         lv_disp_t** display,
                         int cs_pin, const char* name);
    
    // 创建LVGL显示缓冲区
    bool CreateDisplayBuffer(lv_disp_t* display, void** buf1, void** buf2);
    
    // LVGL刷新回调 (LVGL 9.x)
    static void FlushCallback(lv_disp_t* disp, const lv_area_t* area, lv_color_t* color_p);
    
    // 绘制眼睛
    void DrawEye(lv_disp_t* display, ExpressionType expression, bool is_left_eye);
    
    // 绘制简单圆形眼睛
    void DrawSimpleEye(lv_obj_t* parent, int center_x, int center_y, int radius, ExpressionType expression);
    
    // 动画定时器回调
    static void AnimationTimerCallback(lv_timer_t* timer);
    
    // 眨眼定时器回调
    static void BlinkTimerCallback(lv_timer_t* timer);

private:
    // 硬件配置
    static constexpr int SIMPLE_PIN_SCK = 19;
    static constexpr int SIMPLE_PIN_MOSI = 20;
    static constexpr int SIMPLE_PIN_DC = 21;
    static constexpr int SIMPLE_PIN_RST = 1;
    static constexpr int SIMPLE_PIN_CS_LEFT = 2;
    static constexpr int SIMPLE_PIN_CS_RIGHT = 45;
    static constexpr int SIMPLE_PIN_BACKLIGHT = 42;

    // 显示配置
    static constexpr int SIMPLE_SCREEN_WIDTH = 240;
    static constexpr int SIMPLE_SCREEN_HEIGHT = 240;
    static constexpr int SIMPLE_BUFFER_HEIGHT = 60;
    static constexpr size_t SIMPLE_BUFFER_SIZE = SIMPLE_SCREEN_WIDTH * SIMPLE_BUFFER_HEIGHT * sizeof(lv_color_t);
    
    // 硬件句柄
    esp_lcd_panel_io_handle_t left_io_handle_;
    esp_lcd_panel_io_handle_t right_io_handle_;
    esp_lcd_panel_handle_t left_panel_handle_;
    esp_lcd_panel_handle_t right_panel_handle_;
    
    // LVGL显示对象
    lv_disp_t* left_display_;
    lv_disp_t* right_display_;
    
    // 显示缓冲区
    void* left_buf1_;
    void* left_buf2_;
    void* right_buf1_;
    void* right_buf2_;
    
    // 状态变量
    bool initialized_;
    ExpressionType current_expression_;
    uint8_t current_brightness_;
    
    // 动画控制
    lv_timer_t* animation_timer_;
    lv_timer_t* blink_timer_;
    bool animation_running_;
    AnimationType current_animation_;
    
    // 眼睛UI对象
    lv_obj_t* left_eye_container_;
    lv_obj_t* right_eye_container_;
    lv_obj_t* left_pupil_;
    lv_obj_t* right_pupil_;
};

// 工厂函数
std::unique_ptr<SimpleDualScreenManager> CreateSimpleDualScreenManager();

} // namespace simple_dual_screen
