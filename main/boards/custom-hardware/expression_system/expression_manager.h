/**
 * @file expression_manager.h
 * @brief 表情系统管理器
 * 
 * 负责管理双屏表情显示、LVGL集成、表情资源管理
 * 实现拟人化的眼神表情和动画效果
 */

#ifndef EXPRESSION_MANAGER_H
#define EXPRESSION_MANAGER_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include "lvgl.h"

namespace expression_system {

/**
 * @brief 表情类型枚举
 */
enum class EmotionType {
    HAPPY,             // 开心
    SAD,               // 伤心
    ANGRY,             // 生气
    SURPRISED,         // 惊讶
    CONFUSED,          // 困惑
    SLEEPY,            // 困倦
    FOCUSED,           // 专注
    CURIOUS,           // 好奇
    LOVE,              // 喜爱
    NEUTRAL,           // 中性
    FOLLOWING,         // 跟随状态
    THINKING,          // 思考
    EXCITED,           // 兴奋
    WORRIED,           // 担心
    BLINK,             // 眨眼
    CUSTOM             // 自定义
};

/**
 * @brief 动画类型枚举
 */
enum class AnimationType {
    NONE,              // 无动画
    BLINK,             // 眨眼
    LOOK_AROUND,       // 环顾
    FOLLOW_TARGET,     // 跟随目标
    PULSE,             // 脉冲
    FADE,              // 淡入淡出
    ROTATE,            // 旋转
    SCALE,             // 缩放
    CUSTOM             // 自定义
};

/**
 * @brief 眼睛位置枚举
 */
enum class EyePosition {
    LEFT,              // 左眼
    RIGHT,             // 右眼
    BOTH               // 双眼
};

/**
 * @brief 表情配置结构
 */
struct ExpressionConfig {
    EmotionType emotion;           // 表情类型
    std::string name;              // 表情名称
    std::string description;       // 表情描述
    
    // 眼睛形状参数
    int pupil_size;                // 瞳孔大小 (0-100)
    int eye_width;                 // 眼睛宽度
    int eye_height;                // 眼睛高度
    int eye_offset_x;              // 眼睛X偏移
    int eye_offset_y;              // 眼睛Y偏移
    
    // 颜色参数
    lv_color_t pupil_color;        // 瞳孔颜色
    lv_color_t iris_color;         // 虹膜颜色
    lv_color_t background_color;   // 背景颜色
    lv_color_t highlight_color;    // 高光颜色
    
    // 动画参数
    AnimationType animation;       // 动画类型
    uint32_t animation_duration;   // 动画持续时间(ms)
    uint32_t animation_delay;      // 动画延迟(ms)
    bool loop_animation;           // 是否循环动画
    
    // 特效参数
    bool has_sparkle;              // 是否有闪烁效果
    bool has_shadow;               // 是否有阴影
    uint8_t opacity;               // 透明度 (0-255)
    
    ExpressionConfig() : emotion(EmotionType::NEUTRAL), pupil_size(50), 
                        eye_width(80), eye_height(80), eye_offset_x(0), eye_offset_y(0),
                        animation(AnimationType::NONE), animation_duration(1000), 
                        animation_delay(0), loop_animation(false), has_sparkle(false),
                        has_shadow(false), opacity(255) {}
};

/**
 * @brief 动画状态结构
 */
struct AnimationState {
    bool is_playing;               // 是否正在播放
    uint32_t start_time;           // 开始时间
    uint32_t current_time;         // 当前时间
    uint32_t duration;             // 持续时间
    float progress;                // 进度 (0.0-1.0)
    bool is_loop;                  // 是否循环
    int loop_count;                // 循环次数
    
    AnimationState() : is_playing(false), start_time(0), current_time(0),
                      duration(0), progress(0.0f), is_loop(false), loop_count(0) {}
};

/**
 * @brief 眼睛渲染参数
 */
struct EyeRenderParams {
    int center_x;                  // 中心X坐标
    int center_y;                  // 中心Y坐标
    int pupil_x;                   // 瞳孔X坐标
    int pupil_y;                   // 瞳孔Y坐标
    int pupil_radius;              // 瞳孔半径
    int iris_radius;               // 虹膜半径
    float rotation;                // 旋转角度
    float scale_x;                 // X缩放
    float scale_y;                 // Y缩放
    uint8_t alpha;                 // 透明度
    
    EyeRenderParams() : center_x(120), center_y(120), pupil_x(120), pupil_y(120),
                       pupil_radius(30), iris_radius(60), rotation(0.0f),
                       scale_x(1.0f), scale_y(1.0f), alpha(255) {}
};

/**
 * @brief 表情回调接口
 */
class ExpressionCallback {
public:
    virtual ~ExpressionCallback() = default;
    
    // 表情变化回调
    virtual void OnExpressionChanged(EmotionType old_emotion, EmotionType new_emotion) = 0;
    
    // 动画开始回调
    virtual void OnAnimationStarted(AnimationType animation, EyePosition eye) = 0;
    
    // 动画结束回调
    virtual void OnAnimationFinished(AnimationType animation, EyePosition eye) = 0;
    
    // 渲染完成回调
    virtual void OnRenderCompleted(EyePosition eye) = 0;
};

/**
 * @brief 单眼表情渲染器
 */
class EyeRenderer {
public:
    /**
     * @brief 构造函数
     * @param display_buffer 显示缓冲区
     * @param width 屏幕宽度
     * @param height 屏幕高度
     */
    EyeRenderer(lv_disp_t* display, int width = 240, int height = 240);
    
    /**
     * @brief 析构函数
     */
    ~EyeRenderer();
    
    /**
     * @brief 初始化渲染器
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 渲染表情
     * @param config 表情配置
     * @param params 渲染参数
     * @return 是否成功
     */
    bool RenderExpression(const ExpressionConfig& config, const EyeRenderParams& params);
    
    /**
     * @brief 清除屏幕
     * @param color 清除颜色
     * @return 是否成功
     */
    bool Clear(lv_color_t color = lv_color_black());
    
    /**
     * @brief 更新动画
     * @param animation_state 动画状态
     * @param config 表情配置
     * @return 更新后的渲染参数
     */
    EyeRenderParams UpdateAnimation(const AnimationState& animation_state, 
                                   const ExpressionConfig& config);
    
    /**
     * @brief 设置亮度
     * @param brightness 亮度值 (0-100)
     */
    void SetBrightness(uint8_t brightness);
    
    /**
     * @brief 获取显示对象
     * @return LVGL显示对象
     */
    lv_disp_t* GetDisplay() const { return display_; }

private:
    lv_disp_t* display_;
    lv_obj_t* screen_;
    lv_obj_t* eye_container_;
    int width_;
    int height_;
    bool initialized_;
    
    // 渲染辅助函数
    void RenderPupil(const ExpressionConfig& config, const EyeRenderParams& params);
    void RenderIris(const ExpressionConfig& config, const EyeRenderParams& params);
    void RenderHighlight(const ExpressionConfig& config, const EyeRenderParams& params);
    void RenderEyelid(const ExpressionConfig& config, const EyeRenderParams& params);
    void RenderSpecialEffects(const ExpressionConfig& config, const EyeRenderParams& params);
    
    // 动画计算函数
    float CalculateBlinkAnimation(float progress);
    float CalculatePulseAnimation(float progress);
    void CalculateFollowAnimation(float progress, int target_x, int target_y, 
                                 int& pupil_x, int& pupil_y);
};

/**
 * @brief 表情管理器主类
 */
class ExpressionManager {
public:
    static ExpressionManager& GetInstance();
    
    /**
     * @brief 初始化表情管理器
     * @param left_display 左眼显示器
     * @param right_display 右眼显示器
     * @return 是否成功
     */
    bool Initialize(lv_disp_t* left_display, lv_disp_t* right_display);
    
    /**
     * @brief 设置表情
     * @param emotion 表情类型
     * @param eye 眼睛位置
     * @param duration 持续时间(ms, 0表示永久)
     * @return 是否成功
     */
    bool SetExpression(EmotionType emotion, EyePosition eye = EyePosition::BOTH, 
                      uint32_t duration = 0);
    
    /**
     * @brief 播放动画
     * @param animation 动画类型
     * @param eye 眼睛位置
     * @param duration 持续时间(ms)
     * @param loop 是否循环
     * @return 是否成功
     */
    bool PlayAnimation(AnimationType animation, EyePosition eye = EyePosition::BOTH,
                      uint32_t duration = 1000, bool loop = false);
    
    /**
     * @brief 停止动画
     * @param eye 眼睛位置
     * @return 是否成功
     */
    bool StopAnimation(EyePosition eye = EyePosition::BOTH);
    
    /**
     * @brief 眨眼
     * @param eye 眼睛位置
     * @param count 眨眼次数
     * @param speed 眨眼速度
     * @return 是否成功
     */
    bool Blink(EyePosition eye = EyePosition::BOTH, int count = 1, uint32_t speed = 200);
    
    /**
     * @brief 跟随目标
     * @param target_x 目标X坐标
     * @param target_y 目标Y坐标
     * @param eye 眼睛位置
     * @return 是否成功
     */
    bool FollowTarget(int target_x, int target_y, EyePosition eye = EyePosition::BOTH);
    
    /**
     * @brief 环顾四周
     * @param eye 眼睛位置
     * @param duration 持续时间(ms)
     * @return 是否成功
     */
    bool LookAround(EyePosition eye = EyePosition::BOTH, uint32_t duration = 3000);
    
    /**
     * @brief 设置自定义表情
     * @param config 表情配置
     * @param eye 眼睛位置
     * @return 是否成功
     */
    bool SetCustomExpression(const ExpressionConfig& config, 
                           EyePosition eye = EyePosition::BOTH);
    
    /**
     * @brief 加载表情资源
     * @param resource_path 资源路径
     * @return 是否成功
     */
    bool LoadExpressionResources(const std::string& resource_path);
    
    /**
     * @brief 保存表情配置
     * @param config_path 配置路径
     * @return 是否成功
     */
    bool SaveExpressionConfig(const std::string& config_path);
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    void SetCallback(std::shared_ptr<ExpressionCallback> callback);
    
    /**
     * @brief 获取当前表情
     * @param eye 眼睛位置
     * @return 当前表情类型
     */
    EmotionType GetCurrentExpression(EyePosition eye) const;
    
    /**
     * @brief 获取预定义表情配置
     * @param emotion 表情类型
     * @return 表情配置
     */
    ExpressionConfig GetExpressionConfig(EmotionType emotion) const;
    
    /**
     * @brief 更新表情系统
     * 需要在主循环中定期调用
     */
    void Update();
    
    /**
     * @brief 设置全局亮度
     * @param brightness 亮度值 (0-100)
     */
    void SetBrightness(uint8_t brightness);

private:
    ExpressionManager() = default;
    ~ExpressionManager() = default;
    ExpressionManager(const ExpressionManager&) = delete;
    ExpressionManager& operator=(const ExpressionManager&) = delete;
    
    // 初始化预定义表情
    void InitializePredefinedExpressions();
    
    // 更新动画状态
    void UpdateAnimations();
    
    // 渲染眼睛
    void RenderEyes();
    
    std::unique_ptr<EyeRenderer> left_eye_renderer_;
    std::unique_ptr<EyeRenderer> right_eye_renderer_;
    
    std::map<EmotionType, ExpressionConfig> predefined_expressions_;
    ExpressionConfig current_left_expression_;
    ExpressionConfig current_right_expression_;
    
    AnimationState left_animation_state_;
    AnimationState right_animation_state_;
    
    std::shared_ptr<ExpressionCallback> callback_;
    
    bool initialized_;
    uint8_t global_brightness_;
    uint32_t last_update_time_;
    
    // 跟随目标参数
    int follow_target_x_;
    int follow_target_y_;
    bool is_following_;
};

/**
 * @brief 表情类型转字符串
 */
std::string EmotionTypeToString(EmotionType emotion);

/**
 * @brief 字符串转表情类型
 */
EmotionType StringToEmotionType(const std::string& emotion_str);

/**
 * @brief 动画类型转字符串
 */
std::string AnimationTypeToString(AnimationType animation);

/**
 * @brief 字符串转动画类型
 */
AnimationType StringToAnimationType(const std::string& animation_str);

} // namespace expression_system

#endif // EXPRESSION_MANAGER_H
