/**
 * @file dual_screen_expression.cc
 * @brief 双屏表情系统实现 - 基于main_lvgl_proper的优化方案
 */

#include "dual_screen_expression.h"
#include <esp_log.h>
#include <esp_random.h>
#include <algorithm>

static const char *TAG = "DualScreenExpression";

namespace dual_screen_expression {

// 静态实例指针，用于回调函数访问
static DualScreenExpressionManager* g_instance = nullptr;

DualScreenExpressionManager::DualScreenExpressionManager()
    : left_panel_handle_(nullptr), right_panel_handle_(nullptr),
      left_disp_(nullptr), right_disp_(nullptr),
      initialized_(false), current_expression_(ExpressionType::NORMAL),
      auto_animation_enabled_(true), lvgl_mux_(nullptr),
      animation_timer_(nullptr), lvgl_tick_timer_(nullptr) {
    
    ESP_LOGI(TAG, "创建双屏表情管理器");
    
    // 初始化眼睛结构体
    memset(&left_eye_, 0, sizeof(eye_t));
    memset(&right_eye_, 0, sizeof(eye_t));
    
    left_eye_.is_left_eye = true;
    right_eye_.is_left_eye = false;
    left_eye_.state = ExpressionType::NORMAL;
    right_eye_.state = ExpressionType::NORMAL;
    
    g_instance = this;
}

DualScreenExpressionManager::~DualScreenExpressionManager() {
    ESP_LOGI(TAG, "销毁双屏表情管理器");
    
    if (animation_timer_) {
        lv_timer_del(animation_timer_);
    }
    
    if (lvgl_tick_timer_) {
        esp_timer_stop(lvgl_tick_timer_);
        esp_timer_delete(lvgl_tick_timer_);
    }
    
    if (lvgl_mux_) {
        vSemaphoreDelete(lvgl_mux_);
    }
    
    g_instance = nullptr;
}

bool DualScreenExpressionManager::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "双屏表情系统已初始化");
        return true;
    }
    
    ESP_LOGI(TAG, "初始化双屏表情系统...");
    
    // 1. 初始化SPI总线
    esp_err_t ret = InitializeSPIBus();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI总线初始化失败: %d", ret);
        return false;
    }
    
    // 2. 初始化LVGL
    ESP_LOGI(TAG, "初始化LVGL库");
    lv_init();
    
    // 3. 创建LVGL互斥锁
    lvgl_mux_ = xSemaphoreCreateMutex();
    if (!lvgl_mux_) {
        ESP_LOGE(TAG, "创建LVGL互斥锁失败");
        return false;
    }
    
    // 4. 初始化左屏
    ret = InitializeScreen(PIN_CS_LEFT, &left_panel_handle_, &left_disp_, "左屏");
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "左屏初始化失败: %d", ret);
        return false;
    }
    
    // 5. 初始化右屏
    ret = InitializeScreen(PIN_CS_RIGHT, &right_panel_handle_, &right_disp_, "右屏");
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "右屏初始化失败: %d", ret);
        return false;
    }
    
    // 6. 设置LVGL定时器
    SetupLVGLTimer();
    
    // 7. 启动LVGL任务
    StartLVGLTask();
    
    // 8. 等待初始化完成
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // 9. 创建眼睛UI
    CreateEyeUI(left_disp_, &left_eye_, "左眼");
    CreateEyeUI(right_disp_, &right_eye_, "右眼");
    
    // 10. 启动动画定时器
    if (LockLVGL(1000)) {
        animation_timer_ = lv_timer_create(AnimationTimerCallback, 200, this);
        UnlockLVGL();
    }
    
    initialized_ = true;
    ESP_LOGI(TAG, "双屏表情系统初始化成功");
    
    return true;
}

esp_err_t DualScreenExpressionManager::InitializeSPIBus() {
    ESP_LOGI(TAG, "初始化SPI总线");
    
    // 使用GC9A01的标准SPI配置
    const spi_bus_config_t bus_cfg = GC9A01_PANEL_BUS_SPI_CONFIG(
        PIN_SCK, PIN_MOSI, SCREEN_WIDTH * SCREEN_HEIGHT);
    
    return spi_bus_initialize(LCD_HOST, &bus_cfg, SPI_DMA_CH_AUTO);
}

esp_err_t DualScreenExpressionManager::InitializeScreen(int cs_pin, 
                                                       esp_lcd_panel_handle_t *panel_handle, 
                                                       lv_disp_t **disp, 
                                                       const char* screen_name) {
    ESP_LOGI(TAG, "初始化%s (CS引脚: %d)", screen_name, cs_pin);
    
    // 创建面板IO
    esp_lcd_panel_io_handle_t io_handle = NULL;
    const esp_lcd_panel_io_spi_config_t io_config = 
        GC9A01_PANEL_IO_SPI_CONFIG(cs_pin, PIN_DC, NotifyFlushReady, nullptr);
    
    esp_err_t ret = esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)LCD_HOST, &io_config, &io_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "%s面板IO创建失败: %d", screen_name, ret);
        return ret;
    }
    
    // 创建面板设备
    const esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = PIN_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB,
        .data_endian = LCD_RGB_DATA_ENDIAN_BIG,
        .bits_per_pixel = 16,
    };
    
    ret = esp_lcd_new_panel_gc9a01(io_handle, &panel_config, panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "%s面板设备创建失败: %d", screen_name, ret);
        return ret;
    }
    
    // 配置面板
    ESP_ERROR_CHECK(esp_lcd_panel_reset(*panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_init(*panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_invert_color(*panel_handle, true));
    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(*panel_handle, true));
    
    // 分配显示缓冲区
    static lv_disp_draw_buf_t disp_buf;
    static lv_disp_drv_t disp_drv;
    
    lv_color_t *buf1 = (lv_color_t*)heap_caps_malloc(
        SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    lv_color_t *buf2 = (lv_color_t*)heap_caps_malloc(
        SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    
    if (!buf1 || !buf2) {
        ESP_LOGE(TAG, "%s显示缓冲区分配失败", screen_name);
        return ESP_ERR_NO_MEM;
    }
    
    lv_disp_draw_buf_init(&disp_buf, buf1, buf2, SCREEN_WIDTH * LVGL_BUFFER_HEIGHT);
    
    // 初始化LVGL显示驱动
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = SCREEN_WIDTH;
    disp_drv.ver_res = SCREEN_HEIGHT;
    disp_drv.flush_cb = FlushCallback;
    disp_drv.draw_buf = &disp_buf;
    disp_drv.user_data = *panel_handle;
    
    *disp = lv_disp_drv_register(&disp_drv);
    
    ESP_LOGI(TAG, "%s初始化完成", screen_name);
    return ESP_OK;
}

void DualScreenExpressionManager::CreateEyeUI(lv_disp_t *disp, eye_t *eye, const char* eye_name) {
    ESP_LOGI(TAG, "创建%s UI", eye_name);
    
    // 设置当前显示器
    lv_disp_set_default(disp);
    
    // 获取当前屏幕 - 纯黑背景
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    lv_obj_set_style_bg_color(scr, lv_color_black(), 0);
    
    // 创建眼球容器 - 纯白眼球
    eye->eye_container = lv_obj_create(scr);
    lv_obj_set_size(eye->eye_container, 200, 200);
    lv_obj_center(eye->eye_container);
    lv_obj_set_style_radius(eye->eye_container, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->eye_container, lv_color_white(), 0);
    lv_obj_set_style_border_width(eye->eye_container, 0, 0);
    lv_obj_set_style_shadow_width(eye->eye_container, 0, 0);
    lv_obj_set_style_pad_all(eye->eye_container, 0, 0);
    
    // 创建瞳孔 - 纯黑
    eye->pupil = lv_obj_create(eye->eye_container);
    lv_obj_set_size(eye->pupil, 80, 80);
    lv_obj_center(eye->pupil);
    lv_obj_set_style_radius(eye->pupil, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->pupil, lv_color_black(), 0);
    lv_obj_set_style_border_width(eye->pupil, 0, 0);
    lv_obj_set_style_pad_all(eye->pupil, 0, 0);
    
    // 创建大高光点
    eye->highlight = lv_obj_create(eye->pupil);
    lv_obj_set_size(eye->highlight, 25, 25);
    lv_obj_set_pos(eye->highlight, 30, 55);
    lv_obj_set_style_radius(eye->highlight, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->highlight, lv_color_white(), 0);
    lv_obj_set_style_border_width(eye->highlight, 0, 0);
    lv_obj_set_style_pad_all(eye->highlight, 0, 0);
    
    // 创建小高光点
    eye->small_highlight = lv_obj_create(eye->pupil);
    lv_obj_set_size(eye->small_highlight, 8, 8);
    lv_obj_set_pos(eye->small_highlight, 15, 20);
    lv_obj_set_style_radius(eye->small_highlight, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->small_highlight, lv_color_white(), 0);
    lv_obj_set_style_border_width(eye->small_highlight, 0, 0);
    lv_obj_set_style_pad_all(eye->small_highlight, 0, 0);
    
    // 创建上眼睑
    eye->eyelid_top = lv_obj_create(scr);
    lv_obj_set_size(eye->eyelid_top, 200, 0);
    lv_obj_align_to(eye->eyelid_top, eye->eye_container, LV_ALIGN_OUT_TOP_MID, 0, 0);
    lv_obj_set_style_bg_color(eye->eyelid_top, lv_color_black(), 0);
    lv_obj_set_style_border_width(eye->eyelid_top, 0, 0);
    lv_obj_set_style_radius(eye->eyelid_top, 0, 0);
    lv_obj_set_style_pad_all(eye->eyelid_top, 0, 0);
    
    // 创建下眼睑
    eye->eyelid_bottom = lv_obj_create(scr);
    lv_obj_set_size(eye->eyelid_bottom, 200, 0);
    lv_obj_align_to(eye->eyelid_bottom, eye->eye_container, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    lv_obj_set_style_bg_color(eye->eyelid_bottom, lv_color_black(), 0);
    lv_obj_set_style_border_width(eye->eyelid_bottom, 0, 0);
    lv_obj_set_style_radius(eye->eyelid_bottom, 0, 0);
    lv_obj_set_style_pad_all(eye->eyelid_bottom, 0, 0);
    
    // 初始化眼睛状态
    eye->state = ExpressionType::NORMAL;
    eye->pupil_x = 0;
    eye->pupil_y = 0;
    eye->start_x = 0;
    eye->start_y = 0;
    eye->target_x = 0;
    eye->target_y = 0;
    
    ESP_LOGI(TAG, "%s UI创建完成", eye_name);
}

void DualScreenExpressionManager::SetupLVGLTimer() {
    ESP_LOGI(TAG, "设置LVGL定时器");
    
    const esp_timer_create_args_t lvgl_tick_timer_args = {
        .callback = &IncreaseLVGLTick,
        .name = "lvgl_tick"
    };
    
    ESP_ERROR_CHECK(esp_timer_create(&lvgl_tick_timer_args, &lvgl_tick_timer_));
    ESP_ERROR_CHECK(esp_timer_start_periodic(lvgl_tick_timer_, LVGL_TICK_PERIOD_MS * 1000));
}

void DualScreenExpressionManager::StartLVGLTask() {
    ESP_LOGI(TAG, "启动LVGL任务");
    
    xTaskCreate(LVGLTask, "LVGL", LVGL_TASK_STACK_SIZE, this, LVGL_TASK_PRIORITY, NULL);
}

bool DualScreenExpressionManager::SetExpression(ExpressionType expression, const ExpressionConfig* config) {
    if (!initialized_) {
        ESP_LOGE(TAG, "表情系统未初始化");
        return false;
    }
    
    ESP_LOGI(TAG, "设置表情: %s", ExpressionTypeToString(expression).c_str());
    
    if (!LockLVGL(1000)) {
        ESP_LOGE(TAG, "获取LVGL锁失败");
        return false;
    }
    
    SetEyeEmotion(&left_eye_, expression);
    SetEyeEmotion(&right_eye_, expression);
    current_expression_ = expression;
    
    UnlockLVGL();
    
    if (expression_callback_) {
        expression_callback_(expression, true);
    }
    
    return true;
}

bool DualScreenExpressionManager::Blink(bool both_eyes) {
    if (!initialized_) {
        return false;
    }
    
    if (!LockLVGL(1000)) {
        return false;
    }
    
    if (both_eyes) {
        StartBlinkAnimation(&left_eye_);
        StartBlinkAnimation(&right_eye_);
    } else {
        // 随机选择一只眼睛眨眼
        if (esp_random() % 2) {
            StartBlinkAnimation(&left_eye_);
        } else {
            StartBlinkAnimation(&right_eye_);
        }
    }
    
    UnlockLVGL();
    return true;
}

// 回调函数实现
bool DualScreenExpressionManager::NotifyFlushReady(esp_lcd_panel_io_handle_t panel_io,
                                                   esp_lcd_panel_io_event_data_t *edata,
                                                   void *user_ctx) {
    // LVGL 9.x 不再需要手动调用flush_ready
    // 这个回调主要用于通知传输完成
    return false;
}

void DualScreenExpressionManager::FlushCallback(lv_display_t *disp, const lv_area_t *area, lv_color_t *color_map) {
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t)lv_display_get_user_data(disp);
    esp_lcd_panel_draw_bitmap(panel_handle, area->x1, area->y1, area->x2 + 1, area->y2 + 1, color_map);
    lv_display_flush_ready(disp);
}

void DualScreenExpressionManager::IncreaseLVGLTick(void *arg) {
    lv_tick_inc(LVGL_TICK_PERIOD_MS);
}

void DualScreenExpressionManager::LVGLTask(void *arg) {
    DualScreenExpressionManager* manager = static_cast<DualScreenExpressionManager*>(arg);
    
    ESP_LOGI(TAG, "LVGL任务启动");
    
    uint32_t task_delay_ms = LVGL_TASK_MAX_DELAY_MS;
    while (1) {
        if (manager->LockLVGL(-1)) {
            task_delay_ms = lv_timer_handler();
            manager->UnlockLVGL();
        }
        
        // 限制任务延迟范围
        if (task_delay_ms > LVGL_TASK_MAX_DELAY_MS) {
            task_delay_ms = LVGL_TASK_MAX_DELAY_MS;
        } else if (task_delay_ms < LVGL_TASK_MIN_DELAY_MS) {
            task_delay_ms = LVGL_TASK_MIN_DELAY_MS;
        }
        
        vTaskDelay(pdMS_TO_TICKS(task_delay_ms));
    }
}

bool DualScreenExpressionManager::LockLVGL(int timeout_ms) {
    if (!lvgl_mux_) return false;
    
    const TickType_t timeout_ticks = (timeout_ms == -1) ? portMAX_DELAY : pdMS_TO_TICKS(timeout_ms);
    return xSemaphoreTake(lvgl_mux_, timeout_ticks) == pdTRUE;
}

void DualScreenExpressionManager::UnlockLVGL() {
    if (lvgl_mux_) {
        xSemaphoreGive(lvgl_mux_);
    }
}

// 辅助函数实现
std::string ExpressionTypeToString(ExpressionType expression) {
    switch (expression) {
        case ExpressionType::NORMAL: return "NORMAL";
        case ExpressionType::HAPPY: return "HAPPY";
        case ExpressionType::SAD: return "SAD";
        case ExpressionType::ANGRY: return "ANGRY";
        case ExpressionType::SURPRISED: return "SURPRISED";
        case ExpressionType::CONFUSED: return "CONFUSED";
        case ExpressionType::SLEEPY: return "SLEEPY";
        case ExpressionType::FOCUSED: return "FOCUSED";
        case ExpressionType::BLINKING: return "BLINKING";
        case ExpressionType::LOOKING: return "LOOKING";
        case ExpressionType::LOVE: return "LOVE";
        case ExpressionType::THINKING: return "THINKING";
        case ExpressionType::EXCITED: return "EXCITED";
        case ExpressionType::WORRIED: return "WORRIED";
        case ExpressionType::CUSTOM: return "CUSTOM";
        default: return "UNKNOWN";
    }
}

// 动画实现
void DualScreenExpressionManager::StartBlinkAnimation(eye_t *eye) {
    if (eye->state != ExpressionType::NORMAL) {
        return;  // 只有在正常状态下才能眨眼
    }

    eye->state = ExpressionType::BLINKING;

    // 创建眨眼动画 - 上眼睑下降
    lv_anim_t anim_close;
    lv_anim_init(&anim_close);
    lv_anim_set_var(&anim_close, eye->eyelid_top);
    lv_anim_set_values(&anim_close, 0, 100);  // 从0到100像素高度
    lv_anim_set_time(&anim_close, 150);       // 150ms闭眼
    lv_anim_set_exec_cb(&anim_close, (lv_anim_exec_xcb_t)lv_obj_set_height);
    lv_anim_set_path_cb(&anim_close, lv_anim_path_ease_in_out);

    // 创建眨眼动画 - 下眼睑上升
    lv_anim_t anim_close_bottom;
    lv_anim_init(&anim_close_bottom);
    lv_anim_set_var(&anim_close_bottom, eye->eyelid_bottom);
    lv_anim_set_values(&anim_close_bottom, 0, 100);
    lv_anim_set_time(&anim_close_bottom, 150);
    lv_anim_set_exec_cb(&anim_close_bottom, (lv_anim_exec_xcb_t)lv_obj_set_height);
    lv_anim_set_path_cb(&anim_close_bottom, lv_anim_path_ease_in_out);

    // 设置完成回调
    lv_anim_set_ready_cb(&anim_close, [](lv_anim_t *a) {
        eye_t *eye = (eye_t*)lv_anim_get_user_data(a);

        // 创建睁眼动画
        lv_anim_t anim_open;
        lv_anim_init(&anim_open);
        lv_anim_set_var(&anim_open, eye->eyelid_top);
        lv_anim_set_values(&anim_open, 100, 0);
        lv_anim_set_time(&anim_open, 150);
        lv_anim_set_exec_cb(&anim_open, (lv_anim_exec_xcb_t)lv_obj_set_height);
        lv_anim_set_path_cb(&anim_open, lv_anim_path_ease_in_out);
        lv_anim_set_user_data(&anim_open, eye);
        lv_anim_set_ready_cb(&anim_open, [](lv_anim_t *a) {
            eye_t *eye = (eye_t*)lv_anim_get_user_data(a);
            eye->state = ExpressionType::NORMAL;
        });
        lv_anim_start(&anim_open);

        // 下眼睑睁眼动画
        lv_anim_t anim_open_bottom;
        lv_anim_init(&anim_open_bottom);
        lv_anim_set_var(&anim_open_bottom, eye->eyelid_bottom);
        lv_anim_set_values(&anim_open_bottom, 100, 0);
        lv_anim_set_time(&anim_open_bottom, 150);
        lv_anim_set_exec_cb(&anim_open_bottom, (lv_anim_exec_xcb_t)lv_obj_set_height);
        lv_anim_set_path_cb(&anim_open_bottom, lv_anim_path_ease_in_out);
        lv_anim_start(&anim_open_bottom);
    });

    lv_anim_set_user_data(&anim_close, eye);
    lv_anim_start(&anim_close);
    lv_anim_start(&anim_close_bottom);
}

void DualScreenExpressionManager::StartLookAnimation(eye_t *eye, int32_t target_x, int32_t target_y) {
    if (eye->state == ExpressionType::BLINKING) {
        return;  // 眨眼时不能移动
    }

    eye->state = ExpressionType::LOOKING;
    eye->start_x = eye->pupil_x;
    eye->start_y = eye->pupil_y;
    eye->target_x = target_x;
    eye->target_y = target_y;

    // 创建平滑移动动画
    lv_anim_t anim;
    lv_anim_init(&anim);
    lv_anim_set_var(&anim, eye);
    lv_anim_set_values(&anim, 0, 1000);  // 0-1000的进度值
    lv_anim_set_time(&anim, 500);        // 500ms移动时间
    lv_anim_set_exec_cb(&anim, [](void *obj, int32_t value) {
        eye_t *eye = (eye_t *)obj;

        // 计算当前动画进度 (0-1)
        float progress = value / 1000.0f;

        // 使用插值计算当前位置
        int32_t current_x = eye->start_x + (eye->target_x - eye->start_x) * progress;
        int32_t current_y = eye->start_y + (eye->target_y - eye->start_y) * progress;

        // 计算瞳孔中心位置
        int32_t pupil_size = lv_obj_get_width(eye->pupil);
        int32_t center_x = (200 - pupil_size) / 2;
        int32_t center_y = (200 - pupil_size) / 2;

        // 设置瞳孔位置
        lv_obj_set_pos(eye->pupil, center_x + current_x, center_y + current_y);

        // 更新高光位置
        int32_t highlight_x = 30 - current_x/4;
        int32_t highlight_y = 55 - current_y/4;
        highlight_x = LV_CLAMP(10, highlight_x, pupil_size - 15);
        highlight_y = LV_CLAMP(15, highlight_y, pupil_size - 10);
        lv_obj_set_pos(eye->highlight, highlight_x, highlight_y);

        // 更新小高光位置
        int32_t small_highlight_x = 15 - current_x/6;
        int32_t small_highlight_y = 20 - current_y/6;
        small_highlight_x = LV_CLAMP(5, small_highlight_x, pupil_size - 10);
        small_highlight_y = LV_CLAMP(10, small_highlight_y, pupil_size - 5);
        lv_obj_set_pos(eye->small_highlight, small_highlight_x, small_highlight_y);

        // 更新当前位置记录
        eye->pupil_x = current_x;
        eye->pupil_y = current_y;
    });

    lv_anim_set_path_cb(&anim, lv_anim_path_ease_in_out);
    lv_anim_set_ready_cb(&anim, [](lv_anim_t *a) {
        eye_t *eye = (eye_t*)lv_anim_get_user_data(a);
        eye->state = ExpressionType::NORMAL;
    });
    lv_anim_set_user_data(&anim, eye);
    lv_anim_start(&anim);
}

void DualScreenExpressionManager::SetEyeEmotion(eye_t *eye, ExpressionType emotion) {
    switch (emotion) {
        case ExpressionType::HAPPY:
            // 开心表情：瞳孔稍大，眼睛稍微眯起
            lv_obj_set_size(eye->pupil, 90, 90);
            lv_obj_set_height(eye->eyelid_bottom, 20);
            break;

        case ExpressionType::SAD:
            // 伤心表情：瞳孔稍小，上眼睑下垂
            lv_obj_set_size(eye->pupil, 70, 70);
            lv_obj_set_height(eye->eyelid_top, 30);
            break;

        case ExpressionType::ANGRY:
            // 生气表情：瞳孔缩小，眉毛效果
            lv_obj_set_size(eye->pupil, 60, 60);
            lv_obj_set_height(eye->eyelid_top, 40);
            break;

        case ExpressionType::SURPRISED:
            // 惊讶表情：瞳孔放大，眼睛睁大
            lv_obj_set_size(eye->pupil, 100, 100);
            lv_obj_set_height(eye->eyelid_top, 0);
            lv_obj_set_height(eye->eyelid_bottom, 0);
            break;

        case ExpressionType::SLEEPY:
            // 困倦表情：眼睛半闭
            lv_obj_set_height(eye->eyelid_top, 60);
            lv_obj_set_height(eye->eyelid_bottom, 60);
            break;

        case ExpressionType::LOVE:
            // 喜爱表情：心形瞳孔效果（用大瞳孔模拟）
            lv_obj_set_size(eye->pupil, 95, 95);
            lv_obj_set_style_bg_color(eye->pupil, lv_color_make(50, 0, 0), 0);  // 深红色
            break;

        case ExpressionType::NORMAL:
        default:
            // 恢复正常状态
            lv_obj_set_size(eye->pupil, 80, 80);
            lv_obj_set_height(eye->eyelid_top, 0);
            lv_obj_set_height(eye->eyelid_bottom, 0);
            lv_obj_set_style_bg_color(eye->pupil, lv_color_black(), 0);
            break;
    }

    eye->state = emotion;
}

void DualScreenExpressionManager::AnimationTimerCallback(lv_timer_t *timer) {
    DualScreenExpressionManager* manager = static_cast<DualScreenExpressionManager*>(lv_timer_get_user_data(timer));
    if (manager && manager->auto_animation_enabled_) {
        manager->UpdateAutoAnimation();
    }
}

void DualScreenExpressionManager::UpdateAutoAnimation() {
    static uint32_t animation_counter = 0;
    static uint32_t last_blink_time = 0;
    static uint32_t last_look_time = 0;

    uint32_t current_time = lv_tick_get();
    animation_counter++;

    // 自动眨眼 (每3-6秒)
    if (current_time - last_blink_time > (3000 + (esp_random() % 3000))) {
        if (left_eye_.state == ExpressionType::NORMAL && right_eye_.state == ExpressionType::NORMAL) {
            Blink(true);
            last_blink_time = current_time;
        }
    }

    // 随机眼球移动 (每4-8秒)
    if (current_time - last_look_time > (4000 + (esp_random() % 4000))) {
        if (left_eye_.state == ExpressionType::NORMAL && right_eye_.state == ExpressionType::NORMAL) {
            int32_t target_x = (esp_random() % 24) - 12;  // -12 到 +12
            int32_t target_y = (esp_random() % 24) - 12;  // -12 到 +12

            StartLookAnimation(&left_eye_, target_x, target_y);
            StartLookAnimation(&right_eye_, target_x, target_y);
            last_look_time = current_time;
        }
    }
}

bool DualScreenExpressionManager::LookAt(int32_t target_x, int32_t target_y, bool both_eyes) {
    if (!initialized_) {
        return false;
    }

    if (!LockLVGL(1000)) {
        return false;
    }

    if (both_eyes) {
        StartLookAnimation(&left_eye_, target_x, target_y);
        StartLookAnimation(&right_eye_, target_x, target_y);
    } else {
        // 只移动一只眼睛（可以用于特殊效果）
        StartLookAnimation(&left_eye_, target_x, target_y);
    }

    UnlockLVGL();
    return true;
}

bool DualScreenExpressionManager::FollowTarget(int32_t target_x, int32_t target_y) {
    // 跟随目标的逻辑，可以添加平滑跟随算法
    return LookAt(target_x, target_y, true);
}

void DualScreenExpressionManager::EnableAutoAnimation(bool enable) {
    auto_animation_enabled_ = enable;
    ESP_LOGI(TAG, "自动动画: %s", enable ? "启用" : "禁用");
}

void DualScreenExpressionManager::SetExpressionChangeCallback(std::function<void(ExpressionType, bool)> callback) {
    expression_callback_ = callback;
}

ExpressionType DualScreenExpressionManager::GetCurrentExpression() const {
    return current_expression_;
}

bool DualScreenExpressionManager::SetSingleEyeExpression(bool is_left_eye, ExpressionType expression,
                                                         const ExpressionConfig* config) {
    if (!initialized_) {
        return false;
    }

    if (!LockLVGL(1000)) {
        return false;
    }

    eye_t* target_eye = is_left_eye ? &left_eye_ : &right_eye_;
    SetEyeEmotion(target_eye, expression);

    UnlockLVGL();
    return true;
}

ExpressionType StringToExpressionType(const std::string& expression_str) {
    if (expression_str == "NORMAL") return ExpressionType::NORMAL;
    if (expression_str == "HAPPY") return ExpressionType::HAPPY;
    if (expression_str == "SAD") return ExpressionType::SAD;
    if (expression_str == "ANGRY") return ExpressionType::ANGRY;
    if (expression_str == "SURPRISED") return ExpressionType::SURPRISED;
    if (expression_str == "CONFUSED") return ExpressionType::CONFUSED;
    if (expression_str == "SLEEPY") return ExpressionType::SLEEPY;
    if (expression_str == "FOCUSED") return ExpressionType::FOCUSED;
    if (expression_str == "BLINKING") return ExpressionType::BLINKING;
    if (expression_str == "LOOKING") return ExpressionType::LOOKING;
    if (expression_str == "LOVE") return ExpressionType::LOVE;
    if (expression_str == "THINKING") return ExpressionType::THINKING;
    if (expression_str == "EXCITED") return ExpressionType::EXCITED;
    if (expression_str == "WORRIED") return ExpressionType::WORRIED;
    if (expression_str == "CUSTOM") return ExpressionType::CUSTOM;
    return ExpressionType::NORMAL;
}

ExpressionConfig CreateDefaultExpressionConfig(ExpressionType expression) {
    ExpressionConfig config;

    switch (expression) {
        case ExpressionType::HAPPY:
            config.pupil_size = 90;
            config.blink_duration = 200;
            config.animation_speed = 400;
            break;
        case ExpressionType::SAD:
            config.pupil_size = 70;
            config.blink_duration = 300;
            config.animation_speed = 200;
            break;
        case ExpressionType::ANGRY:
            config.pupil_size = 60;
            config.blink_duration = 100;
            config.animation_speed = 150;
            break;
        case ExpressionType::SURPRISED:
            config.pupil_size = 100;
            config.blink_duration = 50;
            config.animation_speed = 100;
            break;
        default:
            // 使用默认配置
            break;
    }

    return config;
}

std::unique_ptr<DualScreenExpressionManager> CreateDualScreenExpressionManager() {
    return std::make_unique<DualScreenExpressionManager>();
}

} // namespace dual_screen_expression
