/**
 * @file expression_integration.cc
 * @brief 表情系统集成实现
 */

#include "expression_integration.h"
#include <esp_log.h>
#include <esp_system.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

static const char* TAG = "ExpressionIntegration";

namespace expression_integration {

UnifiedExpressionController::UnifiedExpressionController()
    : current_impl_(ImplementationType::AUTO_SELECT), initialized_(false),
      current_emotion_(expression_system::EmotionType::NEUTRAL) {
    ESP_LOGI(TAG, "创建统一表情控制器");
}

UnifiedExpressionController::~UnifiedExpressionController() {
    ESP_LOGI(TAG, "销毁统一表情控制器");
    
    if (dual_screen_impl_) {
        dual_screen_impl_.reset();
    }
    
    if (legacy_impl_) {
        legacy_impl_.reset();
    }
}

bool UnifiedExpressionController::Initialize(const ExpressionSystemConfig& config) {
    if (initialized_) {
        ESP_LOGW(TAG, "表情控制器已初始化");
        return true;
    }
    
    ESP_LOGI(TAG, "初始化统一表情控制器...");
    config_ = config;
    
    // 选择最佳实现方案
    if (config_.impl_type == ImplementationType::AUTO_SELECT) {
        current_impl_ = SelectBestImplementation();
    } else {
        current_impl_ = config_.impl_type;
    }
    
    ESP_LOGI(TAG, "选择实现方案: %s", ImplementationTypeToString(current_impl_).c_str());
    
    // 初始化选定的实现
    bool success = false;
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            success = InitializeDualScreenImpl();
            break;
            
        case ImplementationType::LEGACY_SYSTEM:
            success = InitializeLegacyImpl();
            break;
            
        default:
            ESP_LOGE(TAG, "不支持的实现类型");
            return false;
    }
    
    if (!success) {
        ESP_LOGE(TAG, "表情系统初始化失败");
        return false;
    }
    
    initialized_ = true;
    ESP_LOGI(TAG, "统一表情控制器初始化成功");
    
    return true;
}

ImplementationType UnifiedExpressionController::SelectBestImplementation() {
    ESP_LOGI(TAG, "自动选择最佳实现方案...");
    
    // 检测针脚共用兼容性
    if (config_.enable_pin_sharing_optimization && TestPinSharingCompatibility()) {
        ESP_LOGI(TAG, "检测到针脚共用优化支持，选择双屏优化方案");
        return ImplementationType::DUAL_SCREEN_OPTIMIZED;
    }
    
    // 检测可用内存
    size_t free_heap = esp_get_free_heap_size();
    if (free_heap > 200 * 1024) {  // 200KB以上内存
        ESP_LOGI(TAG, "内存充足(%zu KB)，选择双屏优化方案", free_heap / 1024);
        return ImplementationType::DUAL_SCREEN_OPTIMIZED;
    }
    
    ESP_LOGI(TAG, "内存有限(%zu KB)，选择传统方案", free_heap / 1024);
    return ImplementationType::LEGACY_SYSTEM;
}

bool UnifiedExpressionController::InitializeDualScreenImpl() {
    ESP_LOGI(TAG, "初始化双屏优化实现...");
    
    dual_screen_impl_ = dual_screen_expression::CreateDualScreenExpressionManager();
    
    if (!dual_screen_impl_->Initialize()) {
        ESP_LOGE(TAG, "双屏表情管理器初始化失败");
        return false;
    }
    
    // 设置回调
    dual_screen_impl_->SetExpressionChangeCallback([this](dual_screen_expression::ExpressionType expr, bool success) {
        expression_system::EmotionType emotion = ConvertFromExpressionType(expr);
        current_emotion_ = emotion;
        
        if (expression_callback_) {
            expression_callback_(emotion, success);
        }
        
        ESP_LOGI(TAG, "表情变化: %s -> %s", 
                 dual_screen_expression::ExpressionTypeToString(expr).c_str(),
                 success ? "成功" : "失败");
    });
    
    // 配置自动动画
    dual_screen_impl_->EnableAutoAnimation(config_.enable_auto_animation);
    
    ESP_LOGI(TAG, "双屏优化实现初始化成功");
    return true;
}

bool UnifiedExpressionController::InitializeLegacyImpl() {
    ESP_LOGI(TAG, "初始化传统系统实现...");
    
    legacy_impl_ = std::make_unique<expression_system::ExpressionManager>();
    
    if (!legacy_impl_->Initialize()) {
        ESP_LOGE(TAG, "传统表情管理器初始化失败");
        return false;
    }
    
    // 设置回调
    legacy_impl_->SetExpressionChangeCallback([this](expression_system::EmotionType emotion, bool success) {
        current_emotion_ = emotion;
        
        if (expression_callback_) {
            expression_callback_(emotion, success);
        }
        
        ESP_LOGI(TAG, "表情变化: %s -> %s", 
                 expression_system::EmotionTypeToString(emotion).c_str(),
                 success ? "成功" : "失败");
    });
    
    // 配置自动动画
    legacy_impl_->EnableAutoExpression(config_.enable_auto_animation);
    
    ESP_LOGI(TAG, "传统系统实现初始化成功");
    return true;
}

bool UnifiedExpressionController::TestPinSharingCompatibility() {
    ESP_LOGI(TAG, "测试针脚共用兼容性...");
    
    // 检查GPIO配置是否支持共享SPI总线
    // 这里简化为检查可用GPIO数量
    return true;  // 假设支持
}

bool UnifiedExpressionController::SetEmotion(expression_system::EmotionType emotion) {
    if (!initialized_) {
        ESP_LOGE(TAG, "控制器未初始化");
        return false;
    }
    
    ESP_LOGI(TAG, "设置表情: %s", expression_system::EmotionTypeToString(emotion).c_str());
    
    bool success = false;
    
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            if (dual_screen_impl_) {
                dual_screen_expression::ExpressionType expr = ConvertToExpressionType(emotion);
                success = dual_screen_impl_->SetExpression(expr);
            }
            break;
            
        case ImplementationType::LEGACY_SYSTEM:
            if (legacy_impl_) {
                success = legacy_impl_->SetEmotion(emotion);
            }
            break;
            
        default:
            ESP_LOGE(TAG, "未知的实现类型");
            return false;
    }
    
    if (success) {
        current_emotion_ = emotion;
    }
    
    return success;
}

bool UnifiedExpressionController::PlayAnimation(expression_system::AnimationType animation) {
    if (!initialized_) {
        return false;
    }
    
    ESP_LOGI(TAG, "播放动画: %s", expression_system::AnimationTypeToString(animation).c_str());
    
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            if (dual_screen_impl_) {
                switch (animation) {
                    case expression_system::AnimationType::BLINK:
                        return dual_screen_impl_->Blink(true);
                    case expression_system::AnimationType::LOOK_AROUND:
                        return ExecuteLookAroundAnimation();
                    default:
                        return false;
                }
            }
            break;
            
        case ImplementationType::LEGACY_SYSTEM:
            if (legacy_impl_) {
                return legacy_impl_->PlayAnimation(animation);
            }
            break;
    }
    
    return false;
}

bool UnifiedExpressionController::Blink(bool both_eyes) {
    if (!initialized_) {
        return false;
    }
    
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            return dual_screen_impl_ ? dual_screen_impl_->Blink(both_eyes) : false;
            
        case ImplementationType::LEGACY_SYSTEM:
            return legacy_impl_ ? legacy_impl_->PlayAnimation(expression_system::AnimationType::BLINK) : false;
    }
    
    return false;
}

bool UnifiedExpressionController::LookAt(int32_t target_x, int32_t target_y) {
    if (!initialized_) {
        return false;
    }
    
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            return dual_screen_impl_ ? dual_screen_impl_->LookAt(target_x, target_y, true) : false;
            
        case ImplementationType::LEGACY_SYSTEM:
            // 传统系统可能不支持精确的眼球移动
            return true;
    }
    
    return false;
}

bool UnifiedExpressionController::FollowTarget(int32_t target_x, int32_t target_y) {
    if (!initialized_) {
        return false;
    }
    
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            return dual_screen_impl_ ? dual_screen_impl_->FollowTarget(target_x, target_y) : false;
            
        case ImplementationType::LEGACY_SYSTEM:
            // 传统系统的跟随实现
            return true;
    }
    
    return false;
}

bool UnifiedExpressionController::SetBrightness(uint8_t brightness) {
    if (!initialized_) {
        return false;
    }
    
    config_.brightness = brightness;
    ESP_LOGI(TAG, "设置亮度: %d", brightness);
    
    // 两种实现都支持亮度设置
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            // TODO: 实现双屏亮度控制
            return true;
            
        case ImplementationType::LEGACY_SYSTEM:
            return legacy_impl_ ? legacy_impl_->SetBrightness(brightness) : false;
    }
    
    return false;
}

void UnifiedExpressionController::EnableAutoAnimation(bool enable) {
    config_.enable_auto_animation = enable;
    
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            if (dual_screen_impl_) {
                dual_screen_impl_->EnableAutoAnimation(enable);
            }
            break;
            
        case ImplementationType::LEGACY_SYSTEM:
            if (legacy_impl_) {
                legacy_impl_->EnableAutoExpression(enable);
            }
            break;
    }
    
    ESP_LOGI(TAG, "自动动画: %s", enable ? "启用" : "禁用");
}

expression_system::EmotionType UnifiedExpressionController::GetCurrentEmotion() const {
    return current_emotion_;
}

ImplementationType UnifiedExpressionController::GetImplementationType() const {
    return current_impl_;
}

void UnifiedExpressionController::SetExpressionChangeCallback(std::function<void(expression_system::EmotionType, bool)> callback) {
    expression_callback_ = callback;
}

void UnifiedExpressionController::EmergencyStop() {
    ESP_LOGW(TAG, "紧急停止所有表情动画");
    
    switch (current_impl_) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED:
            if (dual_screen_impl_) {
                dual_screen_impl_->EnableAutoAnimation(false);
                dual_screen_impl_->SetExpression(dual_screen_expression::ExpressionType::NORMAL);
            }
            break;
            
        case ImplementationType::LEGACY_SYSTEM:
            if (legacy_impl_) {
                legacy_impl_->EnableAutoExpression(false);
                legacy_impl_->SetEmotion(expression_system::EmotionType::NEUTRAL);
            }
            break;
    }
}

// 类型转换函数
dual_screen_expression::ExpressionType UnifiedExpressionController::ConvertToExpressionType(expression_system::EmotionType emotion) {
    switch (emotion) {
        case expression_system::EmotionType::HAPPY: return dual_screen_expression::ExpressionType::HAPPY;
        case expression_system::EmotionType::SAD: return dual_screen_expression::ExpressionType::SAD;
        case expression_system::EmotionType::ANGRY: return dual_screen_expression::ExpressionType::ANGRY;
        case expression_system::EmotionType::SURPRISED: return dual_screen_expression::ExpressionType::SURPRISED;
        case expression_system::EmotionType::CONFUSED: return dual_screen_expression::ExpressionType::CONFUSED;
        case expression_system::EmotionType::SLEEPY: return dual_screen_expression::ExpressionType::SLEEPY;
        case expression_system::EmotionType::FOCUSED: return dual_screen_expression::ExpressionType::FOCUSED;
        case expression_system::EmotionType::LOVE: return dual_screen_expression::ExpressionType::LOVE;
        case expression_system::EmotionType::THINKING: return dual_screen_expression::ExpressionType::THINKING;
        case expression_system::EmotionType::EXCITED: return dual_screen_expression::ExpressionType::EXCITED;
        case expression_system::EmotionType::WORRIED: return dual_screen_expression::ExpressionType::WORRIED;
        default: return dual_screen_expression::ExpressionType::NORMAL;
    }
}

expression_system::EmotionType UnifiedExpressionController::ConvertFromExpressionType(dual_screen_expression::ExpressionType expression) {
    switch (expression) {
        case dual_screen_expression::ExpressionType::HAPPY: return expression_system::EmotionType::HAPPY;
        case dual_screen_expression::ExpressionType::SAD: return expression_system::EmotionType::SAD;
        case dual_screen_expression::ExpressionType::ANGRY: return expression_system::EmotionType::ANGRY;
        case dual_screen_expression::ExpressionType::SURPRISED: return expression_system::EmotionType::SURPRISED;
        case dual_screen_expression::ExpressionType::CONFUSED: return expression_system::EmotionType::CONFUSED;
        case dual_screen_expression::ExpressionType::SLEEPY: return expression_system::EmotionType::SLEEPY;
        case dual_screen_expression::ExpressionType::FOCUSED: return expression_system::EmotionType::FOCUSED;
        case dual_screen_expression::ExpressionType::LOVE: return expression_system::EmotionType::LOVE;
        case dual_screen_expression::ExpressionType::THINKING: return expression_system::EmotionType::THINKING;
        case dual_screen_expression::ExpressionType::EXCITED: return expression_system::EmotionType::EXCITED;
        case dual_screen_expression::ExpressionType::WORRIED: return expression_system::EmotionType::WORRIED;
        default: return expression_system::EmotionType::NEUTRAL;
    }
}

bool UnifiedExpressionController::ExecuteLookAroundAnimation() {
    ESP_LOGI(TAG, "执行环顾动画");
    
    if (current_impl_ == ImplementationType::DUAL_SCREEN_OPTIMIZED && dual_screen_impl_) {
        // 左看
        dual_screen_impl_->LookAt(-15, 0, true);
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 右看
        dual_screen_impl_->LookAt(15, 0, true);
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 上看
        dual_screen_impl_->LookAt(0, -15, true);
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 回中心
        dual_screen_impl_->LookAt(0, 0, true);
        
        return true;
    }
    
    return false;
}

// 工厂类实现
std::unique_ptr<UnifiedExpressionController> ExpressionSystemFactory::CreateController(const ExpressionSystemConfig& config) {
    auto controller = std::make_unique<UnifiedExpressionController>();
    
    if (controller->Initialize(config)) {
        return controller;
    }
    
    return nullptr;
}

ImplementationType ExpressionSystemFactory::DetectBestImplementation() {
    // 检测系统能力
    size_t free_heap = esp_get_free_heap_size();
    
    if (free_heap > 200 * 1024 && TestPinSharingSupport()) {
        return ImplementationType::DUAL_SCREEN_OPTIMIZED;
    }
    
    return ImplementationType::LEGACY_SYSTEM;
}

bool ExpressionSystemFactory::TestPinSharingSupport() {
    // 简化的针脚支持检测
    return true;
}

// 辅助函数实现
std::string ImplementationTypeToString(ImplementationType type) {
    switch (type) {
        case ImplementationType::DUAL_SCREEN_OPTIMIZED: return "DUAL_SCREEN_OPTIMIZED";
        case ImplementationType::LEGACY_SYSTEM: return "LEGACY_SYSTEM";
        case ImplementationType::AUTO_SELECT: return "AUTO_SELECT";
        default: return "UNKNOWN";
    }
}

ImplementationType StringToImplementationType(const std::string& type_str) {
    if (type_str == "DUAL_SCREEN_OPTIMIZED") return ImplementationType::DUAL_SCREEN_OPTIMIZED;
    if (type_str == "LEGACY_SYSTEM") return ImplementationType::LEGACY_SYSTEM;
    if (type_str == "AUTO_SELECT") return ImplementationType::AUTO_SELECT;
    return ImplementationType::AUTO_SELECT;
}

ExpressionSystemConfig CreateDefaultConfig() {
    return ExpressionSystemConfig();
}

ExpressionSystemConfig CreatePinOptimizedConfig() {
    ExpressionSystemConfig config;
    config.impl_type = ImplementationType::DUAL_SCREEN_OPTIMIZED;
    config.enable_pin_sharing_optimization = true;
    config.enable_auto_animation = true;
    config.animation_speed = 60;
    config.brightness = 90;
    return config;
}

} // namespace expression_integration
