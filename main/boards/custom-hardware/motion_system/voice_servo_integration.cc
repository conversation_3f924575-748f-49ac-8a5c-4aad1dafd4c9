/**
 * @file voice_servo_integration.cc
 * @brief 语音控制与180度舵机集成实现
 */

#include "voice_servo_integration.h"
#include <esp_log.h>
#include <algorithm>
#include <sstream>
#include <regex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char* TAG = "VoiceServo";

namespace voice_servo_integration {

VoiceServoController::VoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio)
    : initialized_(false) {
    
    ESP_LOGI(TAG, "创建语音舵机控制器: 左臂GPIO=%d, 右臂GPIO=%d", left_gpio, right_gpio);
    
    // 创建180度舵机控制器
    servo_controller_ = std::make_unique<servo_180::Servo180Controller>(left_gpio, right_gpio);
}

VoiceServoController::~VoiceServoController() {
    ESP_LOGI(TAG, "语音舵机控制器析构");
    Deinitialize();
}

bool VoiceServoController::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "语音舵机控制器已初始化");
        return true;
    }
    
    ESP_LOGI(TAG, "初始化语音舵机控制器...");
    
    // 初始化舵机控制器
    if (!servo_controller_ || !servo_controller_->Initialize()) {
        ESP_LOGE(TAG, "舵机控制器初始化失败");
        return false;
    }
    
    // 初始化默认语音指令
    InitializeDefaultCommands();
    
    initialized_ = true;
    ESP_LOGI(TAG, "语音舵机控制器初始化成功");
    
    return true;
}

void VoiceServoController::Deinitialize() {
    if (!initialized_) {
        return;
    }
    
    ESP_LOGI(TAG, "反初始化语音舵机控制器");
    
    if (servo_controller_) {
        servo_controller_->Deinitialize();
    }
    
    initialized_ = false;
}

void VoiceServoController::InitializeDefaultCommands() {
    ESP_LOGI(TAG, "初始化默认语音指令...");
    
    // 问候指令
    command_map_["你好"] = VoiceCommandType::GREETING;
    command_map_["hello"] = VoiceCommandType::GREETING;
    command_map_["hi"] = VoiceCommandType::GREETING;
    command_map_["嗨"] = VoiceCommandType::GREETING;
    
    // 告别指令
    command_map_["再见"] = VoiceCommandType::FAREWELL;
    command_map_["goodbye"] = VoiceCommandType::FAREWELL;
    command_map_["bye"] = VoiceCommandType::FAREWELL;
    command_map_["拜拜"] = VoiceCommandType::FAREWELL;
    
    // 挥手指令
    command_map_["挥手"] = VoiceCommandType::WAVE;
    command_map_["wave"] = VoiceCommandType::WAVE;
    command_map_["挥挥手"] = VoiceCommandType::WAVE;
    command_map_["招手"] = VoiceCommandType::WAVE;
    
    // 举手指令
    command_map_["举手"] = VoiceCommandType::RAISE_HAND;
    command_map_["raise"] = VoiceCommandType::RAISE_HAND;
    command_map_["举起手"] = VoiceCommandType::RAISE_HAND;
    command_map_["抬手"] = VoiceCommandType::RAISE_HAND;
    
    // 放下手指令
    command_map_["放下"] = VoiceCommandType::LOWER_HAND;
    command_map_["lower"] = VoiceCommandType::LOWER_HAND;
    command_map_["放下手"] = VoiceCommandType::LOWER_HAND;
    
    // 敬礼指令
    command_map_["敬礼"] = VoiceCommandType::SALUTE;
    command_map_["salute"] = VoiceCommandType::SALUTE;
    command_map_["立正"] = VoiceCommandType::SALUTE;
    
    // 跳舞指令
    command_map_["跳舞"] = VoiceCommandType::DANCE;
    command_map_["dance"] = VoiceCommandType::DANCE;
    command_map_["舞蹈"] = VoiceCommandType::DANCE;
    
    // 停止指令
    command_map_["停止"] = VoiceCommandType::STOP;
    command_map_["stop"] = VoiceCommandType::STOP;
    command_map_["停下"] = VoiceCommandType::STOP;
    
    // 状态查询
    command_map_["状态"] = VoiceCommandType::STATUS;
    command_map_["status"] = VoiceCommandType::STATUS;
    command_map_["怎么样"] = VoiceCommandType::STATUS;
    
    ESP_LOGI(TAG, "默认语音指令初始化完成，共%d条指令", command_map_.size());
}

VoiceCommandResult VoiceServoController::ProcessVoiceCommand(const std::string& voice_text) {
    ESP_LOGI(TAG, "处理语音指令: %s", voice_text.c_str());
    
    VoiceCommandResult result = ParseVoiceCommand(voice_text);
    
    if (result.is_valid) {
        bool success = ExecuteAction(result.command_type, result.params);
        result.response_text = GenerateResponse(result.command_type, success);
        
        if (voice_callback_) {
            voice_callback_(result);
        }
        
        if (action_callback_) {
            action_callback_(result.command_type, success);
        }
    } else {
        result.response_text = "抱歉，我不明白这个指令。";
        ESP_LOGW(TAG, "无法识别的语音指令: %s", voice_text.c_str());
    }
    
    return result;
}

VoiceCommandResult VoiceServoController::ParseVoiceCommand(const std::string& voice_text) {
    VoiceCommandResult result;
    result.original_text = voice_text;
    
    // 标准化文本
    std::string normalized_text = NormalizeText(voice_text);
    
    // 查找匹配的指令
    float best_confidence = 0.0f;
    VoiceCommandType best_command = VoiceCommandType::UNKNOWN;
    
    for (const auto& pair : command_map_) {
        if (normalized_text.find(pair.first) != std::string::npos) {
            float confidence = CalculateConfidence(normalized_text, pair.second);
            if (confidence > best_confidence) {
                best_confidence = confidence;
                best_command = pair.second;
            }
        }
    }
    
    if (best_confidence > 0.5f) {
        result.command_type = best_command;
        result.confidence = best_confidence;
        result.params = ExtractActionParams(normalized_text, best_command);
        result.is_valid = true;
    }
    
    return result;
}

ActionParams VoiceServoController::ExtractActionParams(const std::string& voice_text, VoiceCommandType command_type) {
    ActionParams params = CreateDefaultActionParams(command_type);
    
    // 提取目标舵机
    if (voice_text.find("左") != std::string::npos || voice_text.find("left") != std::string::npos) {
        params.target_servo = "left";
    } else if (voice_text.find("右") != std::string::npos || voice_text.find("right") != std::string::npos) {
        params.target_servo = "right";
    } else {
        params.target_servo = "both";
    }
    
    // 提取重复次数
    std::regex repeat_regex(R"((\d+)次|(\d+)遍)");
    std::smatch match;
    if (std::regex_search(voice_text, match, repeat_regex)) {
        int repeat_count = std::stoi(match[1].str().empty() ? match[2].str() : match[1].str());
        params.repeat_count = std::clamp(repeat_count, 1, 10);
    }
    
    // 提取速度信息
    if (voice_text.find("快") != std::string::npos || voice_text.find("快速") != std::string::npos) {
        params.duration = params.duration / 2;
    } else if (voice_text.find("慢") != std::string::npos || voice_text.find("慢慢") != std::string::npos) {
        params.duration = params.duration * 2;
    }
    
    return params;
}

bool VoiceServoController::ExecuteAction(VoiceCommandType command_type, const ActionParams& params) {
    if (!initialized_ || !servo_controller_) {
        ESP_LOGE(TAG, "控制器未初始化");
        return false;
    }
    
    ESP_LOGI(TAG, "执行动作: %s, 目标: %s, 重复: %u次",
             VoiceCommandTypeToString(command_type).c_str(),
             params.target_servo.c_str(), (unsigned int)params.repeat_count);
    
    bool success = false;
    
    switch (command_type) {
        case VoiceCommandType::GREETING:
            success = ExecuteWaveAction(params);
            break;
            
        case VoiceCommandType::FAREWELL:
            success = ExecuteWaveAction(params);
            break;
            
        case VoiceCommandType::WAVE:
            success = ExecuteWaveAction(params);
            break;
            
        case VoiceCommandType::RAISE_HAND:
            success = ExecuteRaiseHandAction(params);
            break;
            
        case VoiceCommandType::LOWER_HAND:
            success = servo_controller_->SetBothAngles(90.0f, 90.0f, params.duration);
            break;
            
        case VoiceCommandType::SALUTE:
            success = ExecuteSaluteAction(params);
            break;
            
        case VoiceCommandType::DANCE:
            success = ExecuteDanceAction(params);
            break;
            
        case VoiceCommandType::POINT:
            success = ExecutePointAction(params);
            break;
            
        case VoiceCommandType::STOP:
            servo_controller_->Stop();
            success = true;
            break;
            
        case VoiceCommandType::STATUS:
            // 状态查询不需要执行动作
            success = true;
            break;
            
        default:
            ESP_LOGW(TAG, "不支持的指令类型: %d", static_cast<int>(command_type));
            success = false;
            break;
    }
    
    return success;
}

bool VoiceServoController::ExecuteWaveAction(const ActionParams& params) {
    ESP_LOGI(TAG, "执行挥手动作");
    
    for (uint32_t i = 0; i < params.repeat_count; i++) {
        if (params.target_servo == "left") {
            if (!servo_controller_->SetAngle("left", 45.0f, params.duration / 2)) return false;
            if (!servo_controller_->SetAngle("left", 135.0f, params.duration / 2)) return false;
            if (!servo_controller_->SetAngle("left", 90.0f, params.duration / 2)) return false;
        } else if (params.target_servo == "right") {
            if (!servo_controller_->SetAngle("right", 45.0f, params.duration / 2)) return false;
            if (!servo_controller_->SetAngle("right", 135.0f, params.duration / 2)) return false;
            if (!servo_controller_->SetAngle("right", 90.0f, params.duration / 2)) return false;
        } else {
            // 双手挥手
            if (!servo_controller_->SetBothAngles(45.0f, 45.0f, params.duration / 2)) return false;
            if (!servo_controller_->SetBothAngles(135.0f, 135.0f, params.duration / 2)) return false;
            if (!servo_controller_->SetBothAngles(90.0f, 90.0f, params.duration / 2)) return false;
        }
    }
    
    return true;
}

bool VoiceServoController::ExecuteRaiseHandAction(const ActionParams& params) {
    ESP_LOGI(TAG, "执行举手动作");
    
    if (params.target_servo == "left") {
        return servo_controller_->SetAngle("left", 45.0f, params.duration);
    } else if (params.target_servo == "right") {
        return servo_controller_->SetAngle("right", 45.0f, params.duration);
    } else {
        return servo_controller_->SetBothAngles(45.0f, 45.0f, params.duration);
    }
}

bool VoiceServoController::ExecuteSaluteAction(const ActionParams& params) {
    ESP_LOGI(TAG, "执行敬礼动作");
    
    // 右手敬礼
    if (!servo_controller_->SetAngle("right", 45.0f, params.duration)) return false;
    
    // 保持姿势
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 回到初始位置
    return servo_controller_->SetAngle("right", 90.0f, params.duration);
}

bool VoiceServoController::ExecuteDanceAction(const ActionParams& params) {
    ESP_LOGI(TAG, "执行跳舞动作");
    
    // 简单的舞蹈动作序列
    float dance_sequence[][2] = {
        {45.0f, 135.0f},   // 左低右高
        {135.0f, 45.0f},   // 左高右低
        {90.0f, 90.0f},    // 中间
        {0.0f, 180.0f},    // 极端位置
        {180.0f, 0.0f},    // 反向极端
        {90.0f, 90.0f}     // 回到中间
    };
    
    uint32_t step_duration = params.duration / 6;
    
    for (int i = 0; i < 6; i++) {
        if (!servo_controller_->SetBothAngles(dance_sequence[i][0], dance_sequence[i][1], step_duration)) {
            return false;
        }
    }
    
    return true;
}

bool VoiceServoController::ExecutePointAction(const ActionParams& params) {
    ESP_LOGI(TAG, "执行指向动作");
    
    // 右手指向前方
    return servo_controller_->SetAngle("right", 0.0f, params.duration);
}

std::string VoiceServoController::GenerateResponse(VoiceCommandType command_type, bool success) {
    if (!success) {
        return "动作执行失败，请重试。";
    }
    
    switch (command_type) {
        case VoiceCommandType::GREETING:
            return "你好！很高兴见到你！";
        case VoiceCommandType::FAREWELL:
            return "再见！下次再来找我玩！";
        case VoiceCommandType::WAVE:
            return "我在向你挥手呢！";
        case VoiceCommandType::RAISE_HAND:
            return "我举手了！";
        case VoiceCommandType::LOWER_HAND:
            return "手放下了。";
        case VoiceCommandType::SALUTE:
            return "敬礼！";
        case VoiceCommandType::DANCE:
            return "看我跳舞！";
        case VoiceCommandType::POINT:
            return "我在指向那边！";
        case VoiceCommandType::STOP:
            return "好的，我停下了。";
        case VoiceCommandType::STATUS:
            return GetStatus();
        default:
            return "动作完成。";
    }
}

float VoiceServoController::CalculateConfidence(const std::string& text, VoiceCommandType command_type) {
    // 简化的置信度计算
    float base_confidence = 0.7f;
    
    // 根据指令类型调整置信度
    switch (command_type) {
        case VoiceCommandType::GREETING:
        case VoiceCommandType::FAREWELL:
        case VoiceCommandType::WAVE:
            return base_confidence + 0.2f;
        default:
            return base_confidence;
    }
}

std::string VoiceServoController::NormalizeText(const std::string& text) {
    std::string normalized = text;
    
    // 转换为小写
    std::transform(normalized.begin(), normalized.end(), normalized.begin(), ::tolower);
    
    // 移除标点符号
    normalized.erase(std::remove_if(normalized.begin(), normalized.end(), 
                                   [](char c) { return std::ispunct(c); }), normalized.end());
    
    return normalized;
}

void VoiceServoController::EmergencyStop() {
    ESP_LOGW(TAG, "紧急停止");
    if (servo_controller_) {
        servo_controller_->Stop();
    }
}

std::string VoiceServoController::GetStatus() const {
    if (!initialized_) {
        return "语音舵机控制器未初始化";
    }
    
    std::string status = "语音舵机控制器状态：\n";
    status += "  初始化状态: 已初始化\n";
    status += "  支持指令数: " + std::to_string(command_map_.size()) + "\n";
    
    if (servo_controller_) {
        status += "  舵机状态: 正常\n";
        status += "  左臂角度: " + std::to_string(servo_controller_->GetCurrentAngle("left")) + "°\n";
        status += "  右臂角度: " + std::to_string(servo_controller_->GetCurrentAngle("right")) + "°\n";
    }
    
    return status;
}

bool VoiceServoController::IsInitialized() const {
    return initialized_;
}

void VoiceServoController::SetVoiceCommandCallback(std::function<void(const VoiceCommandResult&)> callback) {
    voice_callback_ = callback;
}

void VoiceServoController::SetActionCompleteCallback(std::function<void(VoiceCommandType, bool)> callback) {
    action_callback_ = callback;
}

// 工厂函数和辅助函数
std::unique_ptr<VoiceServoController> CreateVoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio) {
    return std::make_unique<VoiceServoController>(left_gpio, right_gpio);
}

std::string VoiceCommandTypeToString(VoiceCommandType command_type) {
    switch (command_type) {
        case VoiceCommandType::UNKNOWN: return "UNKNOWN";
        case VoiceCommandType::GREETING: return "GREETING";
        case VoiceCommandType::FAREWELL: return "FAREWELL";
        case VoiceCommandType::WAVE: return "WAVE";
        case VoiceCommandType::RAISE_HAND: return "RAISE_HAND";
        case VoiceCommandType::LOWER_HAND: return "LOWER_HAND";
        case VoiceCommandType::SALUTE: return "SALUTE";
        case VoiceCommandType::POINT: return "POINT";
        case VoiceCommandType::DANCE: return "DANCE";
        case VoiceCommandType::STOP: return "STOP";
        case VoiceCommandType::CALIBRATE: return "CALIBRATE";
        case VoiceCommandType::STATUS: return "STATUS";
        default: return "UNKNOWN";
    }
}

ActionParams CreateDefaultActionParams(VoiceCommandType command_type) {
    ActionParams params;
    
    switch (command_type) {
        case VoiceCommandType::WAVE:
            params.duration = 1500;
            params.repeat_count = 3;
            break;
        case VoiceCommandType::DANCE:
            params.duration = 3000;
            params.repeat_count = 1;
            break;
        case VoiceCommandType::SALUTE:
            params.duration = 1000;
            params.target_servo = "right";
            break;
        default:
            params.duration = 1000;
            params.repeat_count = 1;
            break;
    }
    
    return params;
}

} // namespace voice_servo_integration
