/**
 * @file servo_controller.cc
 * @brief 舵机控制器实现 - 基于180度舵机
 */

#include "motion_controller.h"
#include "../servo_180.h"
#include <esp_log.h>
#include <memory>

static const char* TAG = "SERVO_CTRL";

namespace motion_system {

class ServoControllerImpl {
public:
    ServoControllerImpl() : servo_controller_(nullptr), initialized_(false) {}

    ~ServoControllerImpl() {
        if (servo_controller_) {
            servo_controller_.reset();
        }
    }

    bool Initialize(const std::vector<ServoConfig>& configs) {
        if (configs.size() < 2) {
            ESP_LOGE(TAG, "需要至少2个舵机配置（左臂和右臂）");
            return false;
        }

        // 查找左臂和右臂配置
        gpio_num_t left_gpio = GPIO_NUM_NC;
        gpio_num_t right_gpio = GPIO_NUM_NC;

        for (const auto& config : configs) {
            if (config.id == "left_arm" || config.id == "左臂") {
                left_gpio = static_cast<gpio_num_t>(config.gpio_pin);
            } else if (config.id == "right_arm" || config.id == "右臂") {
                right_gpio = static_cast<gpio_num_t>(config.gpio_pin);
            }
        }

        if (left_gpio == GPIO_NUM_NC || right_gpio == GPIO_NUM_NC) {
            ESP_LOGE(TAG, "未找到有效的左臂或右臂GPIO配置");
            return false;
        }

        // 创建180度舵机控制器
        servo_controller_ = std::make_unique<servo_180::Servo180Controller>(left_gpio, right_gpio);

        if (!servo_controller_->IsInitialized()) {
            ESP_LOGE(TAG, "180度舵机控制器初始化失败");
            return false;
        }

        initialized_ = true;
        ESP_LOGI(TAG, "舵机控制器初始化成功");
        return true;
    }

    bool SetAngle(const std::string& servo_id, float angle, uint8_t speed) {
        if (!initialized_) {
            ESP_LOGE(TAG, "舵机控制器未初始化");
            return false;
        }

        return servo_controller_->SetSingleArm(servo_id, angle, speed);
    }

    bool ExecuteAction(const std::string& servo_id, ServoAction action, const MotionParams& params) {
        if (!initialized_) {
            ESP_LOGE(TAG, "舵机控制器未初始化");
            return false;
        }

        ESP_LOGI(TAG, "执行舵机动作: %s, 动作: %s", servo_id.c_str(), ServoActionToString(action).c_str());

        switch (action) {
            case ServoAction::WAVE:
                if (servo_id == "both" || servo_id == "双臂") {
                    return servo_controller_->DualWave(3, params.speed, true);
                } else {
                    auto* servo = GetServoByID(servo_id);
                    return servo ? servo->Wave(3, params.speed, 45.0f) : false;
                }

            case ServoAction::RAISE:
                if (servo_id == "both" || servo_id == "双臂") {
                    return servo_controller_->DualRaise(150.0f, params.speed);
                } else {
                    auto* servo = GetServoByID(servo_id);
                    return servo ? servo->RaiseArm(150.0f, params.speed) : false;
                }

            case ServoAction::LOWER:
                if (servo_id == "both" || servo_id == "双臂") {
                    return servo_controller_->SetBothArms(30.0f, 30.0f, params.speed, true);
                } else {
                    auto* servo = GetServoByID(servo_id);
                    return servo ? servo->LowerArm(params.speed) : false;
                }

            case ServoAction::SALUTE:
                if (servo_id == "both" || servo_id == "双臂") {
                    return servo_controller_->DualSalute(params.speed);
                } else {
                    auto* servo = GetServoByID(servo_id);
                    return servo ? servo->Salute(params.speed) : false;
                }

            case ServoAction::STOP:
                servo_controller_->StopAll();
                return true;

            default:
                ESP_LOGW(TAG, "不支持的动作类型: %s", ServoActionToString(action).c_str());
                return false;
        }
    }

    bool StopServo(const std::string& servo_id) {
        if (!initialized_) {
            return false;
        }

        if (servo_id == "all" || servo_id == "所有") {
            servo_controller_->StopAll();
        } else {
            auto* servo = GetServoByID(servo_id);
            if (servo) {
                servo->Stop();
            }
        }

        return true;
    }

    float GetCurrentAngle(const std::string& servo_id) const {
        if (!initialized_) {
            return 0.0f;
        }

        auto* servo = GetServoByID(servo_id);
        return servo ? servo->GetCurrentAngle() : 0.0f;
    }

private:
    std::unique_ptr<servo_180::Servo180Controller> servo_controller_;
    bool initialized_;

    servo_180::Servo180* GetServoByID(const std::string& servo_id) const {
        if (!servo_controller_) {
            return nullptr;
        }

        if (servo_id == "left_arm" || servo_id == "左臂") {
            return servo_controller_->GetLeftServo();
        } else if (servo_id == "right_arm" || servo_id == "右臂") {
            return servo_controller_->GetRightServo();
        }

        return nullptr;
    }
};

// ServoController实现
ServoController::ServoController() : impl_(std::make_unique<ServoControllerImpl>()) {}

ServoController::~ServoController() = default;

bool ServoController::Initialize(const std::vector<ServoConfig>& configs) {
    return impl_->Initialize(configs);
}

bool ServoController::SetAngle(const std::string& servo_id, int angle, uint8_t speed) {
    return impl_->SetAngle(servo_id, static_cast<float>(angle), speed);
}

bool ServoController::ExecuteAction(const std::string& servo_id, ServoAction action, const MotionParams& params) {
    return impl_->ExecuteAction(servo_id, action, params);
}

bool ServoController::ExecuteDualArmAction(ServoAction left_action, ServoAction right_action, const MotionParams& params) {
    bool left_ok = impl_->ExecuteAction("left_arm", left_action, params);
    bool right_ok = impl_->ExecuteAction("right_arm", right_action, params);
    return left_ok && right_ok;
}

bool ServoController::StopServo(const std::string& servo_id) {
    return impl_->StopServo(servo_id);
}

bool ServoController::StopAllServos() {
    return impl_->StopServo("all");
}

int ServoController::GetCurrentAngle(const std::string& servo_id) const {
    return static_cast<int>(impl_->GetCurrentAngle(servo_id));
}

bool ServoController::CalibrateServo(const std::string& servo_id) {
    ESP_LOGI(TAG, "校准舵机: %s", servo_id.c_str());
    // TODO: 实现校准逻辑
    return true;
}

} // namespace motion_system
