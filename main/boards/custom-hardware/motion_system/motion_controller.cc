// 运动控制器存根实现
#include "motion_controller.h"
#include <esp_log.h>

static const char* TAG = "MOTION_CTRL";

namespace motion_system {

MotionController& MotionController::GetInstance() {
    static MotionController instance;
    return instance;
}

bool MotionController::Initialize(const std::vector<ServoConfig>& servo_configs,
                                const std::vector<MotorConfig>& motor_configs) {
    ESP_LOGI(TAG, "Motion controller initialized (stub)");
    initialized_ = true;
    return true;
}

std::string ServoActionToString(ServoAction action) {
    switch (action) {
        case ServoAction::STOP: return "STOP";
        case ServoAction::WAVE: return "WAVE";
        case ServoAction::RAISE: return "RAISE";
        case ServoAction::LOWER: return "LOWER";
        case ServoAction::SALUTE: return "SALUTE";
        case ServoAction::POINT: return "POINT";
        case ServoAction::HUG: return "HUG";
        case ServoAction::CLAP: return "CLAP";
        case ServoAction::DANCE: return "DANCE";
        case ServoAction::CUSTOM: return "CUSTOM";
        default: return "UNKNOWN";
    }
}

} // namespace motion_system
