# 运动控制系统

## 📖 概述

运动控制系统负责机器人的肢体动作控制，主要包括180度舵机控制和语音集成功能。

## 🔧 硬件配置

### 舵机规格
- **型号**: 180度舵机 (如SG90)
- **数量**: 2个 (左臂 + 右臂)
- **控制方式**: PWM信号
- **角度范围**: 0° - 180°

### 引脚连接
```
舵机控制:
- 左臂舵机: GPIO8  (PWM输出)
- 右臂舵机: GPIO9  (PWM输出)

PWM参数:
- 频率: 50Hz (20ms周期)
- 脉宽: 0.5ms-2.5ms (对应0°-180°)
```

## 💻 软件架构

### 核心类
- `Servo180Controller`: 180度舵机控制器
- `VoiceServoController`: 语音舵机集成控制器

### 主要功能
1. **舵机初始化**: MCPWM驱动初始化
2. **角度控制**: 精确角度设置
3. **动作序列**: 复杂动作组合
4. **语音集成**: 语音命令直接控制

## 🎯 动作类型

### 基础动作
- `WAVE`: 挥手 (左臂或右臂)
- `RAISE`: 举手 (单臂举起)
- `SALUTE`: 敬礼 (右臂敬礼姿势)
- `POINT`: 指向 (手臂指向前方)
- `REST`: 复位 (回到初始位置)

### 复合动作
- `DANCE`: 跳舞 (双臂协调动作)
- `GREETING`: 问候 (双臂欢迎姿势)
- `CELEBRATION`: 庆祝 (双臂高举)

## 🔄 使用示例

```cpp
#include "servo_180.h"
#include "voice_servo_integration.h"

// 初始化舵机控制器
auto servo_controller = CreateServo180Controller(GPIO_NUM_8, GPIO_NUM_9);
if (!servo_controller->Initialize()) {
    ESP_LOGE(TAG, "舵机初始化失败");
    return;
}

// 基础角度控制
servo_controller->SetAngle("left", 45.0f, 1000);   // 左臂45度，1秒完成
servo_controller->SetAngle("right", 135.0f, 1000); // 右臂135度，1秒完成

// 语音集成控制
VoiceServoController voice_controller(servo_controller.get());
voice_controller.Initialize();

// 执行语音命令
ActionParams params = {
    .target_servo = "left",
    .duration = 2000,
    .repeat_count = 3
};
voice_controller.ExecuteAction(VoiceCommandType::WAVE, params);
```

## 📊 技术参数

### PWM配置
- **定时器频率**: 1MHz (1μs分辨率)
- **PWM周期**: 20ms (50Hz)
- **脉宽范围**: 500μs - 2500μs
- **角度精度**: ±1°

### 性能指标
- **响应时间**: <50ms
- **最大转速**: 60°/秒
- **位置精度**: ±2°
- **重复精度**: ±1°

## 🎮 语音命令

### 支持的语音指令
```
挥手动作:
- "挥手" / "wave"
- "左手挥手" / "右手挥手"

举手动作:
- "举手" / "raise hand"
- "左手举起" / "右手举起"

敬礼动作:
- "敬礼" / "salute"

指向动作:
- "指向前方" / "point forward"

复位动作:
- "复位" / "reset" / "回到原位"
```

### 参数控制
```cpp
ActionParams params = {
    .target_servo = "left",      // 目标舵机: "left", "right", "both"
    .duration = 2000,            // 动作持续时间(ms)
    .repeat_count = 1,           // 重复次数
    .speed_factor = 1.0f         // 速度因子 (0.1-2.0)
};
```

## 🐛 故障排除

### 常见问题
1. **舵机不动**: 检查PWM信号和电源
2. **角度不准**: 校准PWM脉宽范围
3. **抖动**: 检查电源稳定性
4. **过热**: 降低负载或增加散热

### 调试方法
```cpp
// 启用调试日志
#define SERVO_DEBUG_ENABLED 1

// 测试舵机连接
servo_controller->TestServo("left");
servo_controller->TestServo("right");

// 校准舵机
servo_controller->CalibrateServo("left");
```

## 🔮 未来扩展

- [ ] 更多舵机支持 (头部、腰部)
- [ ] 平滑插值算法
- [ ] 力反馈检测
- [ ] 动作录制回放
- [ ] 机器学习动作优化
