/**
 * @file motor_controller.cc
 * @brief 电机控制器完整实现
 */

#include "motion_controller.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <cmath>
#include <algorithm>

static const char* TAG = "MotorController";

namespace motion_system {

// 电机状态
enum class MotorState {
    IDLE,
    MOVING,
    TURNING,
    BRAKING,
    ERROR
};

// 电机配置
struct MotorConfig {
    std::string motor_id;
    int gpio_pin1;
    int gpio_pin2;
    int pwm_channel;
    bool reversed;

    MotorConfig() : gpio_pin1(-1), gpio_pin2(-1), pwm_channel(-1), reversed(false) {}
};

class MotorControllerImpl {
public:
    MotorControllerImpl() : initialized_(false), current_state_(MotorState::IDLE) {}

    ~MotorControllerImpl() {
        StopAllMotors();
    }

    bool Initialize(const std::vector<MotorConfig>& configs) {
        if (initialized_) {
            ESP_LOGW(TAG, "电机控制器已初始化");
            return true;
        }

        ESP_LOGI(TAG, "初始化电机控制器...");

        // 配置电机
        for (const auto& config : configs) {
            if (!ConfigureMotor(config)) {
                ESP_LOGE(TAG, "配置电机失败: %s", config.motor_id.c_str());
                return false;
            }
            motor_configs_[config.motor_id] = config;
        }

        // 初始化PWM
        if (!InitializePWM()) {
            ESP_LOGE(TAG, "PWM初始化失败");
            return false;
        }

        current_state_ = MotorState::IDLE;
        initialized_ = true;

        ESP_LOGI(TAG, "电机控制器初始化成功，配置了%d个电机", configs.size());
        return true;
    }

    bool SetSpeed(const std::string& motor_id, int8_t speed) {
        if (!initialized_) {
            ESP_LOGE(TAG, "电机控制器未初始化");
            return false;
        }

        auto it = motor_configs_.find(motor_id);
        if (it == motor_configs_.end()) {
            ESP_LOGE(TAG, "未找到电机: %s", motor_id.c_str());
            return false;
        }

        const MotorConfig& config = it->second;

        // 限制速度范围
        speed = std::clamp(speed, static_cast<int8_t>(-100), static_cast<int8_t>(100));

        // 应用反向设置
        if (config.reversed) {
            speed = -speed;
        }

        // 设置电机方向和速度
        bool success = SetMotorPWM(config, speed);

        if (success) {
            motor_speeds_[motor_id] = speed;
            ESP_LOGD(TAG, "设置电机%s速度: %d", motor_id.c_str(), speed);
        }

        return success;
    }

    bool Move(const std::string& direction, uint8_t speed, uint32_t duration) {
        if (!initialized_) {
            return false;
        }

        ESP_LOGI(TAG, "移动: %s, 速度: %d, 持续时间: %dms",
                 direction.c_str(), speed, duration);

        current_state_ = MotorState::MOVING;

        // 限制速度范围
        speed = std::clamp(speed, static_cast<uint8_t>(0), static_cast<uint8_t>(100));

        bool success = false;

        if (direction == "forward") {
            success = MoveForward(speed);
        } else if (direction == "backward") {
            success = MoveBackward(speed);
        } else if (direction == "left") {
            success = TurnLeft(speed);
        } else if (direction == "right") {
            success = TurnRight(speed);
        } else {
            ESP_LOGW(TAG, "未知方向: %s", direction.c_str());
            return false;
        }

        // 如果指定了持续时间，设置定时停止
        if (success && duration > 0) {
            ScheduleStop(duration);
        }

        return success;
    }

    bool Turn(float angle, uint8_t speed) {
        if (!initialized_) {
            return false;
        }

        ESP_LOGI(TAG, "转向: %.1f度, 速度: %d", angle, speed);

        current_state_ = MotorState::TURNING;

        // 计算转向时间（简化计算）
        uint32_t turn_duration = CalculateTurnDuration(angle, speed);

        bool success = false;
        if (angle > 0) {
            success = TurnRight(speed);
        } else {
            success = TurnLeft(speed);
        }

        if (success && turn_duration > 0) {
            ScheduleStop(turn_duration);
        }

        return success;
    }

    bool StopAll() {
        if (!initialized_) {
            return false;
        }

        ESP_LOGI(TAG, "停止所有电机");

        bool success = true;
        for (const auto& pair : motor_configs_) {
            if (!SetSpeed(pair.first, 0)) {
                success = false;
            }
        }

        current_state_ = MotorState::IDLE;
        return success;
    }

    int8_t GetCurrentSpeed(const std::string& motor_id) const {
        auto it = motor_speeds_.find(motor_id);
        if (it != motor_speeds_.end()) {
            return it->second;
        }
        return 0;
    }

    MotorStatus GetStatus() const {
        MotorStatus status;
        status.is_moving = (current_state_ != MotorState::IDLE);

        switch (current_state_) {
            case MotorState::IDLE:
                status.state = "IDLE";
                break;
            case MotorState::MOVING:
                status.state = "MOVING";
                break;
            case MotorState::TURNING:
                status.state = "TURNING";
                break;
            case MotorState::BRAKING:
                status.state = "BRAKING";
                break;
            case MotorState::ERROR:
                status.state = "ERROR";
                break;
        }

        // 复制电机速度
        for (const auto& pair : motor_speeds_) {
            status.motor_speeds[pair.first] = pair.second;
        }

        return status;
    }

private:
    bool initialized_;
    MotorState current_state_;
    std::map<std::string, MotorConfig> motor_configs_;
    std::map<std::string, int8_t> motor_speeds_;
    TaskHandle_t stop_timer_task_;

    bool ConfigureMotor(const MotorConfig& config) {
        ESP_LOGI(TAG, "配置电机: %s, GPIO: %d,%d, PWM: %d",
                 config.motor_id.c_str(), config.gpio_pin1, config.gpio_pin2, config.pwm_channel);

        // 配置GPIO引脚
        gpio_config_t io_conf = {};
        io_conf.intr_type = GPIO_INTR_DISABLE;
        io_conf.mode = GPIO_MODE_OUTPUT;
        io_conf.pin_bit_mask = (1ULL << config.gpio_pin1) | (1ULL << config.gpio_pin2);
        io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
        io_conf.pull_up_en = GPIO_PULLUP_DISABLE;

        esp_err_t ret = gpio_config(&io_conf);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "GPIO配置失败: %d", ret);
            return false;
        }

        // 初始化为停止状态
        gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin1), 0);
        gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin2), 0);

        return true;
    }

    bool InitializePWM() {
        ESP_LOGI(TAG, "初始化PWM");

        // TODO: 实现PWM初始化
        // 这里需要根据具体的PWM配置来实现

        return true;
    }

    bool SetMotorPWM(const MotorConfig& config, int8_t speed) {
        if (speed == 0) {
            // 停止电机
            gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin1), 0);
            gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin2), 0);
            return true;
        }

        // 设置方向
        if (speed > 0) {
            // 正向
            gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin1), 1);
            gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin2), 0);
        } else {
            // 反向
            gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin1), 0);
            gpio_set_level(static_cast<gpio_num_t>(config.gpio_pin2), 1);
        }

        // TODO: 设置PWM占空比
        // 这里需要根据具体的PWM实现来设置速度

        return true;
    }

    bool MoveForward(uint8_t speed) {
        ESP_LOGD(TAG, "前进，速度: %d", speed);

        bool success = true;

        // 假设有左右两个电机
        if (motor_configs_.find("left") != motor_configs_.end()) {
            success &= SetSpeed("left", speed);
        }
        if (motor_configs_.find("right") != motor_configs_.end()) {
            success &= SetSpeed("right", speed);
        }

        return success;
    }

    bool MoveBackward(uint8_t speed) {
        ESP_LOGD(TAG, "后退，速度: %d", speed);

        bool success = true;

        if (motor_configs_.find("left") != motor_configs_.end()) {
            success &= SetSpeed("left", -speed);
        }
        if (motor_configs_.find("right") != motor_configs_.end()) {
            success &= SetSpeed("right", -speed);
        }

        return success;
    }

    bool TurnLeft(uint8_t speed) {
        ESP_LOGD(TAG, "左转，速度: %d", speed);

        bool success = true;

        // 左轮反向，右轮正向
        if (motor_configs_.find("left") != motor_configs_.end()) {
            success &= SetSpeed("left", -speed);
        }
        if (motor_configs_.find("right") != motor_configs_.end()) {
            success &= SetSpeed("right", speed);
        }

        return success;
    }

    bool TurnRight(uint8_t speed) {
        ESP_LOGD(TAG, "右转，速度: %d", speed);

        bool success = true;

        // 左轮正向，右轮反向
        if (motor_configs_.find("left") != motor_configs_.end()) {
            success &= SetSpeed("left", speed);
        }
        if (motor_configs_.find("right") != motor_configs_.end()) {
            success &= SetSpeed("right", -speed);
        }

        return success;
    }

    uint32_t CalculateTurnDuration(float angle, uint8_t speed) {
        // 简化的转向时间计算
        // 假设90度转向在50%速度下需要1秒
        float base_time = 1000.0f;  // 毫秒
        float angle_factor = std::abs(angle) / 90.0f;
        float speed_factor = 50.0f / speed;

        return static_cast<uint32_t>(base_time * angle_factor * speed_factor);
    }

    void ScheduleStop(uint32_t delay_ms) {
        // 创建定时停止任务
        if (stop_timer_task_) {
            vTaskDelete(stop_timer_task_);
        }

        xTaskCreate([](void* param) {
            uint32_t delay = *static_cast<uint32_t*>(param);
            vTaskDelay(pdMS_TO_TICKS(delay));

            // 获取控制器实例并停止
            // 这里需要一个更好的方式来访问控制器实例
            ESP_LOGI(TAG, "定时停止电机");

            vTaskDelete(nullptr);
        }, "stop_timer", 2048, &delay_ms, 1, &stop_timer_task_);
    }

    void StopAllMotors() {
        for (const auto& pair : motor_configs_) {
            SetSpeed(pair.first, 0);
        }
    }
};

// MotorController实现
MotorController::MotorController() : impl_(std::make_unique<MotorControllerImpl>()) {}

MotorController::~MotorController() = default;

bool MotorController::Initialize(const std::vector<MotorConfig>& configs) {
    // 转换配置格式
    std::vector<MotorConfig> internal_configs;
    for (const auto& config : configs) {
        MotorConfig internal_config;
        internal_config.motor_id = config.id;
        internal_config.gpio_pin1 = config.gpio_pin1;
        internal_config.gpio_pin2 = config.gpio_pin2;
        internal_config.pwm_channel = config.pwm_channel;
        internal_config.reversed = config.reversed;
        internal_configs.push_back(internal_config);
    }

    return impl_->Initialize(internal_configs);
}

bool MotorController::SetSpeed(const std::string& motor_id, int8_t speed) {
    return impl_->SetSpeed(motor_id, speed);
}

bool MotorController::Move(const std::string& direction, uint8_t speed, uint32_t duration) {
    return impl_->Move(direction, speed, duration);
}

bool MotorController::Turn(float angle, uint8_t speed) {
    return impl_->Turn(angle, speed);
}

bool MotorController::StopAll() {
    return impl_->StopAll();
}

int8_t MotorController::GetCurrentSpeed(const std::string& motor_id) const {
    return impl_->GetCurrentSpeed(motor_id);
}

MotorStatus MotorController::GetStatus() const {
    return impl_->GetStatus();
}

bool MotorController::Brake(uint8_t force) {
    ESP_LOGI(TAG, "刹车，力度: %d", force);
    return StopAll();
}

bool MotorController::SetAcceleration(uint8_t acceleration) {
    ESP_LOGI(TAG, "设置加速度: %d", acceleration);
    // TODO: 实现加速度控制
    return true;
}

} // namespace motion_system
