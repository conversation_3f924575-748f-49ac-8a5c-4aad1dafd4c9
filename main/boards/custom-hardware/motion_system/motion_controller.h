/**
 * @file motion_controller.h
 * @brief 运动控制系统
 * 
 * 负责舵机控制、电机驱动、人物跟随算法的实现
 * 提供统一的运动控制接口
 */

#ifndef MOTION_CONTROLLER_H
#define MOTION_CONTROLLER_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

namespace motion_system {

/**
 * @brief 舵机动作类型
 */
enum class ServoAction {
    STOP,              // 停止
    WAVE,              // 挥手
    RAISE,             // 举手
    LOWER,             // 放下
    SALUTE,            // 敬礼
    POINT,             // 指向
    HUG,               // 拥抱
    CLAP,              // 鼓掌
    DANCE,             // 舞蹈
    CUSTOM             // 自定义
};

/**
 * @brief 电机运动方向
 */
enum class MotorDirection {
    FORWARD,           // 前进
    BACKWARD,          // 后退
    LEFT,              // 左转
    RIGHT,             // 右转
    ROTATE_LEFT,       // 原地左转
    ROTATE_RIGHT,      // 原地右转
    STOP               // 停止
};

/**
 * @brief 跟随状态
 */
enum class FollowState {
    IDLE,              // 空闲
    SEARCHING,         // 搜索目标
    TRACKING,          // 跟踪目标
    FOLLOWING,         // 跟随中
    LOST_TARGET,       // 丢失目标
    OBSTACLE_DETECTED, // 检测到障碍
    ERROR              // 错误状态
};

/**
 * @brief 舵机配置
 */
struct ServoConfig {
    std::string id;                // 舵机ID
    uint8_t gpio_pin;              // GPIO引脚
    int min_angle;                 // 最小角度
    int max_angle;                 // 最大角度
    int neutral_angle;             // 中性角度
    uint8_t max_speed;             // 最大速度
    bool reverse_direction;        // 是否反向
    std::string description;       // 描述
    
    ServoConfig() : gpio_pin(0), min_angle(0), max_angle(180), 
                   neutral_angle(90), max_speed(100), reverse_direction(false) {}
};

/**
 * @brief 电机配置
 */
struct MotorConfig {
    std::string id;                // 电机ID
    uint8_t in1_pin;               // IN1引脚
    uint8_t in2_pin;               // IN2引脚
    uint8_t enable_pin;            // 使能引脚
    int max_speed;                 // 最大速度
    bool reverse_direction;        // 是否反向
    std::string description;       // 描述
    
    MotorConfig() : in1_pin(0), in2_pin(0), enable_pin(0), 
                   max_speed(100), reverse_direction(false) {}
};

/**
 * @brief 目标信息
 */
struct TargetInfo {
    bool detected;                 // 是否检测到
    float distance;                // 距离(米)
    float angle;                   // 角度(度)
    float confidence;              // 置信度
    uint32_t last_seen_time;       // 最后检测时间
    int x_position;                // X位置
    int y_position;                // Y位置
    
    TargetInfo() : detected(false), distance(0.0f), angle(0.0f), 
                  confidence(0.0f), last_seen_time(0), x_position(0), y_position(0) {}
};

/**
 * @brief 运动参数
 */
struct MotionParams {
    uint8_t speed;                 // 速度 (0-100)
    uint32_t duration;             // 持续时间(ms)
    float target_distance;         // 目标距离(米)
    float target_angle;            // 目标角度(度)
    bool smooth_motion;            // 是否平滑运动
    bool auto_stop;                // 是否自动停止
    
    MotionParams() : speed(50), duration(0), target_distance(0.0f), 
                    target_angle(0.0f), smooth_motion(true), auto_stop(true) {}
};

/**
 * @brief 运动控制回调接口
 */
class MotionCallback {
public:
    virtual ~MotionCallback() = default;
    
    // 舵机动作完成回调
    virtual void OnServoActionCompleted(const std::string& servo_id, ServoAction action) = 0;
    
    // 电机运动完成回调
    virtual void OnMotorMotionCompleted(const std::string& motor_id, MotorDirection direction) = 0;
    
    // 跟随状态变化回调
    virtual void OnFollowStateChanged(FollowState old_state, FollowState new_state) = 0;
    
    // 目标检测回调
    virtual void OnTargetDetected(const TargetInfo& target) = 0;
    
    // 目标丢失回调
    virtual void OnTargetLost() = 0;
    
    // 障碍物检测回调
    virtual void OnObstacleDetected(float distance, float angle) = 0;
    
    // 运动错误回调
    virtual void OnMotionError(const std::string& error_message) = 0;
};

/**
 * @brief 舵机控制器
 */
class ServoController {
public:
    /**
     * @brief 构造函数
     */
    ServoController();
    
    /**
     * @brief 析构函数
     */
    ~ServoController();
    
    /**
     * @brief 初始化舵机控制器
     * @param configs 舵机配置列表
     * @return 是否成功
     */
    bool Initialize(const std::vector<ServoConfig>& configs);
    
    /**
     * @brief 设置舵机角度
     * @param servo_id 舵机ID
     * @param angle 角度
     * @param speed 速度
     * @return 是否成功
     */
    bool SetAngle(const std::string& servo_id, int angle, uint8_t speed = 50);
    
    /**
     * @brief 执行舵机动作
     * @param servo_id 舵机ID
     * @param action 动作类型
     * @param params 运动参数
     * @return 是否成功
     */
    bool ExecuteAction(const std::string& servo_id, ServoAction action, 
                      const MotionParams& params = MotionParams());
    
    /**
     * @brief 执行双臂协调动作
     * @param left_action 左臂动作
     * @param right_action 右臂动作
     * @param params 运动参数
     * @return 是否成功
     */
    bool ExecuteDualArmAction(ServoAction left_action, ServoAction right_action,
                             const MotionParams& params = MotionParams());
    
    /**
     * @brief 停止舵机
     * @param servo_id 舵机ID
     * @return 是否成功
     */
    bool StopServo(const std::string& servo_id);
    
    /**
     * @brief 停止所有舵机
     * @return 是否成功
     */
    bool StopAllServos();
    
    /**
     * @brief 获取当前角度
     * @param servo_id 舵机ID
     * @return 当前角度
     */
    int GetCurrentAngle(const std::string& servo_id) const;
    
    /**
     * @brief 校准舵机
     * @param servo_id 舵机ID
     * @return 是否成功
     */
    bool CalibrateServo(const std::string& servo_id);

private:
    std::map<std::string, ServoConfig> servo_configs_;
    std::map<std::string, int> current_angles_;
    std::map<std::string, TaskHandle_t> servo_tasks_;
    bool initialized_;
    mutable std::mutex mutex_;
    
    // 舵机动作实现
    void ExecuteWaveAction(const std::string& servo_id, const MotionParams& params);
    void ExecuteRaiseAction(const std::string& servo_id, const MotionParams& params);
    void ExecuteSaluteAction(const std::string& servo_id, const MotionParams& params);
    void ExecuteHugAction(const MotionParams& params);
    void ExecuteClapAction(const MotionParams& params);
    
    // 舵机任务函数
    static void ServoActionTask(void* parameter);
};

/**
 * @brief 电机控制器
 */
class MotorController {
public:
    /**
     * @brief 构造函数
     */
    MotorController();
    
    /**
     * @brief 析构函数
     */
    ~MotorController();
    
    /**
     * @brief 初始化电机控制器
     * @param configs 电机配置列表
     * @return 是否成功
     */
    bool Initialize(const std::vector<MotorConfig>& configs);
    
    /**
     * @brief 设置电机速度
     * @param motor_id 电机ID
     * @param speed 速度 (-100到100)
     * @return 是否成功
     */
    bool SetSpeed(const std::string& motor_id, int speed);
    
    /**
     * @brief 移动
     * @param direction 方向
     * @param params 运动参数
     * @return 是否成功
     */
    bool Move(MotorDirection direction, const MotionParams& params = MotionParams());
    
    /**
     * @brief 转向
     * @param angle 转向角度(度)
     * @param params 运动参数
     * @return 是否成功
     */
    bool Turn(float angle, const MotionParams& params = MotionParams());
    
    /**
     * @brief 停止所有电机
     * @return 是否成功
     */
    bool StopAll();
    
    /**
     * @brief 获取当前速度
     * @param motor_id 电机ID
     * @return 当前速度
     */
    int GetCurrentSpeed(const std::string& motor_id) const;
    
    /**
     * @brief 紧急停止
     * @return 是否成功
     */
    bool EmergencyStop();

private:
    std::map<std::string, MotorConfig> motor_configs_;
    std::map<std::string, int> current_speeds_;
    bool initialized_;
    mutable std::mutex mutex_;
    
    // 电机控制辅助函数
    void SetMotorDirection(const std::string& motor_id, MotorDirection direction);
    void SetMotorPWM(const std::string& motor_id, uint8_t pwm_value);
};

/**
 * @brief 人物跟随控制器
 */
class PersonFollowController {
public:
    /**
     * @brief 构造函数
     */
    PersonFollowController();
    
    /**
     * @brief 析构函数
     */
    ~PersonFollowController();
    
    /**
     * @brief 初始化跟随控制器
     * @param motor_controller 电机控制器
     * @return 是否成功
     */
    bool Initialize(std::shared_ptr<MotorController> motor_controller);
    
    /**
     * @brief 开始跟随
     * @return 是否成功
     */
    bool StartFollowing();
    
    /**
     * @brief 停止跟随
     * @return 是否成功
     */
    bool StopFollowing();
    
    /**
     * @brief 更新目标信息
     * @param target 目标信息
     */
    void UpdateTarget(const TargetInfo& target);
    
    /**
     * @brief 设置跟随参数
     * @param min_distance 最小跟随距离(米)
     * @param max_distance 最大跟随距离(米)
     * @param follow_speed 跟随速度
     * @param turn_sensitivity 转向灵敏度
     */
    void SetFollowParameters(float min_distance, float max_distance, 
                           uint8_t follow_speed, float turn_sensitivity);
    
    /**
     * @brief 获取跟随状态
     * @return 跟随状态
     */
    FollowState GetFollowState() const;
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    void SetCallback(std::shared_ptr<MotionCallback> callback);

private:
    std::shared_ptr<MotorController> motor_controller_;
    std::shared_ptr<MotionCallback> callback_;
    
    FollowState current_state_;
    TargetInfo current_target_;
    
    // 跟随参数
    float min_follow_distance_;
    float max_follow_distance_;
    uint8_t follow_speed_;
    float turn_sensitivity_;
    
    // 控制参数
    float kp_distance_;            // 距离比例系数
    float kp_angle_;               // 角度比例系数
    uint32_t target_lost_timeout_; // 目标丢失超时时间
    
    TaskHandle_t follow_task_;
    bool following_active_;
    mutable std::mutex mutex_;
    
    // 跟随控制任务
    static void FollowControlTask(void* parameter);
    
    // 跟随算法实现
    void UpdateFollowControl();
    MotionParams CalculateMotionParams(const TargetInfo& target);
    void HandleTargetLost();
    void SearchForTarget();
};

/**
 * @brief 运动控制管理器
 */
class MotionController {
public:
    static MotionController& GetInstance();
    
    /**
     * @brief 初始化运动控制管理器
     * @param servo_configs 舵机配置
     * @param motor_configs 电机配置
     * @return 是否成功
     */
    bool Initialize(const std::vector<ServoConfig>& servo_configs,
                   const std::vector<MotorConfig>& motor_configs);
    
    /**
     * @brief 获取舵机控制器
     * @return 舵机控制器
     */
    std::shared_ptr<ServoController> GetServoController();
    
    /**
     * @brief 获取电机控制器
     * @return 电机控制器
     */
    std::shared_ptr<MotorController> GetMotorController();
    
    /**
     * @brief 获取跟随控制器
     * @return 跟随控制器
     */
    std::shared_ptr<PersonFollowController> GetFollowController();
    
    /**
     * @brief 执行组合动作
     * @param action_name 动作名称
     * @param params 运动参数
     * @return 是否成功
     */
    bool ExecuteComboAction(const std::string& action_name, 
                           const MotionParams& params = MotionParams());
    
    /**
     * @brief 紧急停止所有运动
     * @return 是否成功
     */
    bool EmergencyStopAll();
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    void SetCallback(std::shared_ptr<MotionCallback> callback);
    
    /**
     * @brief 获取系统状态
     * @return 状态字符串
     */
    std::string GetSystemStatus() const;

private:
    MotionController() = default;
    ~MotionController() = default;
    MotionController(const MotionController&) = delete;
    MotionController& operator=(const MotionController&) = delete;
    
    std::shared_ptr<ServoController> servo_controller_;
    std::shared_ptr<MotorController> motor_controller_;
    std::shared_ptr<PersonFollowController> follow_controller_;
    std::shared_ptr<MotionCallback> callback_;
    bool initialized_;
    
    // 预定义组合动作
    void InitializeComboActions();
    std::map<std::string, std::function<bool(const MotionParams&)>> combo_actions_;
};

/**
 * @brief 舵机动作类型转字符串
 */
std::string ServoActionToString(ServoAction action);

/**
 * @brief 字符串转舵机动作类型
 */
ServoAction StringToServoAction(const std::string& action_str);

/**
 * @brief 电机方向转字符串
 */
std::string MotorDirectionToString(MotorDirection direction);

/**
 * @brief 跟随状态转字符串
 */
std::string FollowStateToString(FollowState state);

} // namespace motion_system

#endif // MOTION_CONTROLLER_H
