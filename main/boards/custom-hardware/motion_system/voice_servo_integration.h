/**
 * @file voice_servo_integration.h
 * @brief 语音舵机集成控制器
 * 
 * 将语音指令转换为舵机动作，实现自然语言控制舵机系统
 * 支持复杂的语音指令解析和动作序列执行
 */

#pragma once
#include "servo_180.h"
#include <string>
#include <map>
#include <vector>
#include <functional>
#include <memory>

namespace voice_servo_integration {

/**
 * @brief 语音指令类型
 */
enum class VoiceCommandType {
    UNKNOWN,           // 未知指令
    GREETING,          // 问候
    FAREWELL,          // 告别
    WAVE,              // 挥手
    RAISE_HAND,        // 举手
    LOWER_HAND,        // 放下手
    SALUTE,            // 敬礼
    POINT,             // 指向
    DANCE,             // 跳舞
    STOP,              // 停止
    CALIBRATE,         // 校准
    STATUS             // 状态查询
};

/**
 * @brief 动作参数
 */
struct ActionParams {
    std::string target_servo;      // 目标舵机（"left", "right", "both"）
    float angle;                   // 目标角度
    uint32_t duration;             // 持续时间（毫秒）
    uint32_t repeat_count;         // 重复次数
    bool smooth;                   // 是否平滑运动
    
    ActionParams() : target_servo("both"), angle(90.0f), duration(1000), 
                    repeat_count(1), smooth(true) {}
};

/**
 * @brief 语音指令解析结果
 */
struct VoiceCommandResult {
    VoiceCommandType command_type;
    ActionParams params;
    float confidence;              // 识别置信度
    std::string original_text;     // 原始语音文本
    std::string response_text;     // 回复文本
    bool is_valid;                 // 是否有效
    
    VoiceCommandResult() : command_type(VoiceCommandType::UNKNOWN), 
                          confidence(0.0f), is_valid(false) {}
};

/**
 * @brief 语音舵机集成控制器
 */
class VoiceServoController {
public:
    /**
     * @brief 构造函数
     * @param left_gpio 左臂舵机GPIO
     * @param right_gpio 右臂舵机GPIO
     */
    VoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio);
    
    /**
     * @brief 析构函数
     */
    ~VoiceServoController();
    
    /**
     * @brief 初始化控制器
     * @return 成功返回true，失败返回false
     */
    bool Initialize();
    
    /**
     * @brief 反初始化控制器
     */
    void Deinitialize();
    
    /**
     * @brief 处理语音指令
     * @param voice_text 语音识别文本
     * @return 处理结果
     */
    VoiceCommandResult ProcessVoiceCommand(const std::string& voice_text);
    
    /**
     * @brief 执行动作指令
     * @param command_type 指令类型
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteAction(VoiceCommandType command_type, const ActionParams& params);
    
    /**
     * @brief 紧急停止所有动作
     */
    void EmergencyStop();
    
    /**
     * @brief 获取当前状态
     * @return 状态信息字符串
     */
    std::string GetStatus() const;
    
    /**
     * @brief 检查是否已初始化
     * @return 已初始化返回true，否则返回false
     */
    bool IsInitialized() const;
    
    /**
     * @brief 设置语音指令回调函数
     * @param callback 回调函数
     */
    void SetVoiceCommandCallback(std::function<void(const VoiceCommandResult&)> callback);
    
    /**
     * @brief 设置动作完成回调函数
     * @param callback 回调函数
     */
    void SetActionCompleteCallback(std::function<void(VoiceCommandType, bool)> callback);
    
    /**
     * @brief 添加自定义语音指令
     * @param command_text 指令文本
     * @param command_type 指令类型
     * @param params 默认参数
     * @return 成功返回true，失败返回false
     */
    bool AddCustomCommand(const std::string& command_text, VoiceCommandType command_type, const ActionParams& params);
    
    /**
     * @brief 移除自定义语音指令
     * @param command_text 指令文本
     * @return 成功返回true，失败返回false
     */
    bool RemoveCustomCommand(const std::string& command_text);
    
    /**
     * @brief 获取支持的指令列表
     * @return 指令列表
     */
    std::vector<std::string> GetSupportedCommands() const;

private:
    std::unique_ptr<servo_180::Servo180Controller> servo_controller_;
    bool initialized_;
    
    // 语音指令映射
    std::map<std::string, VoiceCommandType> command_map_;
    std::map<std::string, ActionParams> custom_params_;
    
    // 回调函数
    std::function<void(const VoiceCommandResult&)> voice_callback_;
    std::function<void(VoiceCommandType, bool)> action_callback_;
    
    /**
     * @brief 初始化默认语音指令
     */
    void InitializeDefaultCommands();
    
    /**
     * @brief 解析语音指令
     * @param voice_text 语音文本
     * @return 解析结果
     */
    VoiceCommandResult ParseVoiceCommand(const std::string& voice_text);
    
    /**
     * @brief 提取动作参数
     * @param voice_text 语音文本
     * @param command_type 指令类型
     * @return 动作参数
     */
    ActionParams ExtractActionParams(const std::string& voice_text, VoiceCommandType command_type);
    
    /**
     * @brief 生成回复文本
     * @param command_type 指令类型
     * @param success 执行是否成功
     * @return 回复文本
     */
    std::string GenerateResponse(VoiceCommandType command_type, bool success);
    
    /**
     * @brief 执行挥手动作
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteWaveAction(const ActionParams& params);
    
    /**
     * @brief 执行举手动作
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteRaiseHandAction(const ActionParams& params);
    
    /**
     * @brief 执行敬礼动作
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteSaluteAction(const ActionParams& params);
    
    /**
     * @brief 执行跳舞动作
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteDanceAction(const ActionParams& params);
    
    /**
     * @brief 执行指向动作
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecutePointAction(const ActionParams& params);
    
    /**
     * @brief 计算指令置信度
     * @param voice_text 语音文本
     * @param command_type 指令类型
     * @return 置信度（0.0-1.0）
     */
    float CalculateConfidence(const std::string& voice_text, VoiceCommandType command_type);
    
    /**
     * @brief 标准化语音文本
     * @param text 原始文本
     * @return 标准化后的文本
     */
    std::string NormalizeText(const std::string& text);
    
    /**
     * @brief 检查文本是否包含关键词
     * @param text 文本
     * @param keywords 关键词列表
     * @return 包含返回true，否则返回false
     */
    bool ContainsKeywords(const std::string& text, const std::vector<std::string>& keywords);
};

/**
 * @brief 创建语音舵机控制器
 * @param left_gpio 左臂舵机GPIO
 * @param right_gpio 右臂舵机GPIO
 * @return 控制器实例
 */
std::unique_ptr<VoiceServoController> CreateVoiceServoController(gpio_num_t left_gpio, gpio_num_t right_gpio);

/**
 * @brief 语音指令类型转字符串
 * @param command_type 指令类型
 * @return 指令名称字符串
 */
std::string VoiceCommandTypeToString(VoiceCommandType command_type);

/**
 * @brief 字符串转语音指令类型
 * @param command_str 指令名称字符串
 * @return 指令类型
 */
VoiceCommandType StringToVoiceCommandType(const std::string& command_str);

/**
 * @brief 创建默认动作参数
 * @param command_type 指令类型
 * @return 默认参数
 */
ActionParams CreateDefaultActionParams(VoiceCommandType command_type);

/**
 * @brief 验证动作参数
 * @param params 动作参数
 * @return 有效返回true，否则返回false
 */
bool ValidateActionParams(const ActionParams& params);

} // namespace voice_servo_integration
