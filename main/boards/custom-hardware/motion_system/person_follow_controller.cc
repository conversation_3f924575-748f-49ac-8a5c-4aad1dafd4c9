/**
 * @file person_follow_controller.cc
 * @brief 人物跟随控制器完整实现
 */

#include "motion_controller.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <cmath>
#include <algorithm>

static const char* TAG = "PersonFollow";

namespace motion_system {

// 跟随状态
enum class FollowState {
    IDLE,           // 空闲
    SEARCHING,      // 搜索目标
    TRACKING,       // 跟踪目标
    FOLLOWING,      // 跟随移动
    LOST,           // 目标丢失
    OBSTACLE        // 遇到障碍
};

// 跟随参数
struct FollowParams {
    float target_distance;      // 目标距离 (米)
    float distance_tolerance;   // 距离容差 (米)
    float angle_tolerance;      // 角度容差 (度)
    float max_speed;           // 最大速度
    float min_speed;           // 最小速度
    float turn_speed;          // 转向速度
    uint32_t lost_timeout;     // 目标丢失超时 (毫秒)
    uint32_t search_timeout;   // 搜索超时 (毫秒)

    FollowParams() : target_distance(1.0f), distance_tolerance(0.3f),
                    angle_tolerance(15.0f), max_speed(80), min_speed(20),
                    turn_speed(60), lost_timeout(3000), search_timeout(10000) {}
};

// 目标信息
struct TargetInfo {
    float distance;         // 距离 (米)
    float angle;           // 角度 (度，相对于正前方)
    float confidence;      // 置信度 (0-1)
    uint32_t last_seen;    // 最后检测时间
    bool is_valid;         // 是否有效

    TargetInfo() : distance(0), angle(0), confidence(0),
                  last_seen(0), is_valid(false) {}
};

class PersonFollowControllerImpl {
public:
    PersonFollowControllerImpl() :
        state_(FollowState::IDLE), enabled_(false),
        follow_task_(nullptr), initialized_(false) {}

    ~PersonFollowControllerImpl() {
        if (follow_task_) {
            vTaskDelete(follow_task_);
        }
    }

    bool Initialize() {
        if (initialized_) {
            return true;
        }

        ESP_LOGI(TAG, "初始化人物跟随控制器...");

        // 创建跟随任务
        BaseType_t ret = xTaskCreate(FollowTask, "follow_task", 4096, this, 4, &follow_task_);
        if (ret != pdPASS) {
            ESP_LOGE(TAG, "创建跟随任务失败");
            return false;
        }

        state_ = FollowState::IDLE;
        initialized_ = true;

        ESP_LOGI(TAG, "人物跟随控制器初始化成功");
        return true;
    }

    bool StartFollowing(const FollowConfig& config) {
        if (!initialized_) {
            ESP_LOGE(TAG, "控制器未初始化");
            return false;
        }

        ESP_LOGI(TAG, "开始人物跟随");

        // 更新配置
        params_.target_distance = config.target_distance;
        params_.max_speed = config.max_speed;

        enabled_ = true;
        state_ = FollowState::SEARCHING;

        return true;
    }

    void StopFollowing() {
        ESP_LOGI(TAG, "停止人物跟随");
        enabled_ = false;
        state_ = FollowState::IDLE;
    }

    bool UpdateTarget(float distance, float angle, float confidence) {
        if (!enabled_) {
            return false;
        }

        target_.distance = distance;
        target_.angle = angle;
        target_.confidence = confidence;
        target_.last_seen = esp_timer_get_time() / 1000;
        target_.is_valid = (confidence > 0.5f);

        if (target_.is_valid) {
            if (state_ == FollowState::SEARCHING || state_ == FollowState::LOST) {
                state_ = FollowState::TRACKING;
                ESP_LOGI(TAG, "目标已找到: 距离=%.2fm, 角度=%.1f°, 置信度=%.2f",
                         distance, angle, confidence);
            }
        }

        return true;
    }

    FollowStatus GetStatus() const {
        FollowStatus status;
        status.is_following = enabled_;
        status.target_distance = target_.distance;
        status.target_angle = target_.angle;
        status.confidence = target_.confidence;

        switch (state_) {
            case FollowState::IDLE:
                status.state = "IDLE";
                break;
            case FollowState::SEARCHING:
                status.state = "SEARCHING";
                break;
            case FollowState::TRACKING:
                status.state = "TRACKING";
                break;
            case FollowState::FOLLOWING:
                status.state = "FOLLOWING";
                break;
            case FollowState::LOST:
                status.state = "LOST";
                break;
            case FollowState::OBSTACLE:
                status.state = "OBSTACLE";
                break;
        }

        return status;
    }

private:
    FollowState state_;
    bool enabled_;
    bool initialized_;
    TaskHandle_t follow_task_;
    FollowParams params_;
    TargetInfo target_;

    // 跟随任务
    static void FollowTask(void* parameter) {
        PersonFollowControllerImpl* controller = static_cast<PersonFollowControllerImpl*>(parameter);

        while (true) {
            if (controller->enabled_) {
                controller->ProcessFollowing();
            }
            vTaskDelay(pdMS_TO_TICKS(100));  // 10Hz更新频率
        }
    }

    void ProcessFollowing() {
        uint32_t current_time = esp_timer_get_time() / 1000;

        switch (state_) {
            case FollowState::SEARCHING:
                ProcessSearching(current_time);
                break;

            case FollowState::TRACKING:
                ProcessTracking(current_time);
                break;

            case FollowState::FOLLOWING:
                ProcessFollowingMovement(current_time);
                break;

            case FollowState::LOST:
                ProcessLost(current_time);
                break;

            case FollowState::OBSTACLE:
                ProcessObstacle(current_time);
                break;

            default:
                break;
        }
    }

    void ProcessSearching(uint32_t current_time) {
        // 搜索模式：缓慢转动寻找目标
        static uint32_t search_start_time = current_time;
        static bool turn_direction = true;  // true=右转, false=左转

        if (current_time - search_start_time > params_.search_timeout) {
            ESP_LOGW(TAG, "搜索超时，停止跟随");
            StopFollowing();
            return;
        }

        // 检查是否找到目标
        if (target_.is_valid && (current_time - target_.last_seen) < 500) {
            state_ = FollowState::TRACKING;
            ESP_LOGI(TAG, "搜索中发现目标");
            return;
        }

        // 执行搜索转动
        float turn_angle = turn_direction ? 30.0f : -30.0f;
        ExecuteTurn(turn_angle, params_.turn_speed / 2);

        // 每2秒改变转向
        if ((current_time - search_start_time) % 2000 < 100) {
            turn_direction = !turn_direction;
        }
    }

    void ProcessTracking(uint32_t current_time) {
        // 检查目标是否丢失
        if (!target_.is_valid || (current_time - target_.last_seen) > 1000) {
            state_ = FollowState::LOST;
            ESP_LOGW(TAG, "目标丢失");
            return;
        }

        // 计算跟随动作
        FollowAction action = CalculateFollowAction();

        switch (action.type) {
            case FollowActionType::STOP:
                ExecuteStop();
                state_ = FollowState::TRACKING;  // 保持跟踪状态
                break;

            case FollowActionType::MOVE_FORWARD:
                ExecuteMove("forward", action.speed);
                state_ = FollowState::FOLLOWING;
                break;

            case FollowActionType::MOVE_BACKWARD:
                ExecuteMove("backward", action.speed);
                state_ = FollowState::FOLLOWING;
                break;

            case FollowActionType::TURN_LEFT:
                ExecuteTurn(-action.angle, action.speed);
                state_ = FollowState::FOLLOWING;
                break;

            case FollowActionType::TURN_RIGHT:
                ExecuteTurn(action.angle, action.speed);
                state_ = FollowState::FOLLOWING;
                break;

            case FollowActionType::MOVE_AND_TURN:
                ExecuteMoveAndTurn(action.angle, action.speed);
                state_ = FollowState::FOLLOWING;
                break;
        }
    }

    void ProcessFollowingMovement(uint32_t current_time) {
        // 在移动过程中继续跟踪目标
        if (!target_.is_valid || (current_time - target_.last_seen) > 1000) {
            state_ = FollowState::LOST;
            ExecuteStop();
            return;
        }

        // 检查是否需要调整动作
        FollowAction action = CalculateFollowAction();

        if (action.type == FollowActionType::STOP) {
            ExecuteStop();
            state_ = FollowState::TRACKING;
        }
        // 其他情况继续当前动作，等待下次更新
    }

    void ProcessLost(uint32_t current_time) {
        static uint32_t lost_start_time = current_time;

        ExecuteStop();

        // 检查是否重新找到目标
        if (target_.is_valid && (current_time - target_.last_seen) < 500) {
            state_ = FollowState::TRACKING;
            ESP_LOGI(TAG, "重新找到目标");
            return;
        }

        // 超时后开始搜索
        if (current_time - lost_start_time > params_.lost_timeout) {
            state_ = FollowState::SEARCHING;
            ESP_LOGI(TAG, "开始搜索丢失的目标");
        }
    }

    void ProcessObstacle(uint32_t current_time) {
        // 遇到障碍物时的处理
        ExecuteStop();

        // 简单的避障策略：后退并转向
        ExecuteMove("backward", params_.min_speed);
        vTaskDelay(pdMS_TO_TICKS(500));

        ExecuteTurn(45.0f, params_.turn_speed);
        vTaskDelay(pdMS_TO_TICKS(1000));

        state_ = FollowState::SEARCHING;
    }

    FollowAction CalculateFollowAction() {
        FollowAction action;
        action.type = FollowActionType::STOP;
        action.speed = 0;
        action.angle = 0;

        float distance_error = target_.distance - params_.target_distance;
        float angle_error = target_.angle;

        // 角度控制优先
        if (std::abs(angle_error) > params_.angle_tolerance) {
            if (std::abs(distance_error) < params_.distance_tolerance) {
                // 只需要转向
                action.type = (angle_error > 0) ? FollowActionType::TURN_RIGHT : FollowActionType::TURN_LEFT;
                action.angle = std::abs(angle_error);
                action.speed = CalculateTurnSpeed(angle_error);
            } else {
                // 需要边移动边转向
                action.type = FollowActionType::MOVE_AND_TURN;
                action.angle = angle_error;
                action.speed = CalculateMoveSpeed(distance_error);
            }
        } else {
            // 角度正确，只需要距离控制
            if (distance_error > params_.distance_tolerance) {
                action.type = FollowActionType::MOVE_FORWARD;
                action.speed = CalculateMoveSpeed(distance_error);
            } else if (distance_error < -params_.distance_tolerance) {
                action.type = FollowActionType::MOVE_BACKWARD;
                action.speed = CalculateMoveSpeed(-distance_error);
            } else {
                action.type = FollowActionType::STOP;
            }
        }

        return action;
    }

    uint8_t CalculateMoveSpeed(float distance_error) {
        // 基于距离误差计算移动速度
        float abs_error = std::abs(distance_error);
        float speed_ratio = std::min(abs_error / 2.0f, 1.0f);  // 2米内线性变化

        uint8_t speed = params_.min_speed +
                       (params_.max_speed - params_.min_speed) * speed_ratio;

        return std::clamp(speed, params_.min_speed, params_.max_speed);
    }

    uint8_t CalculateTurnSpeed(float angle_error) {
        // 基于角度误差计算转向速度
        float abs_error = std::abs(angle_error);
        float speed_ratio = std::min(abs_error / 90.0f, 1.0f);  // 90度内线性变化

        uint8_t speed = params_.min_speed +
                       (params_.turn_speed - params_.min_speed) * speed_ratio;

        return std::clamp(speed, params_.min_speed, params_.turn_speed);
    }

    // 运动执行函数（这些需要与电机控制器集成）
    void ExecuteStop() {
        ESP_LOGD(TAG, "执行停止");
        // TODO: 调用电机控制器停止
    }

    void ExecuteMove(const std::string& direction, uint8_t speed) {
        ESP_LOGD(TAG, "执行移动: %s, 速度: %d", direction.c_str(), speed);
        // TODO: 调用电机控制器移动
    }

    void ExecuteTurn(float angle, uint8_t speed) {
        ESP_LOGD(TAG, "执行转向: %.1f度, 速度: %d", angle, speed);
        // TODO: 调用电机控制器转向
    }

    void ExecuteMoveAndTurn(float angle, uint8_t speed) {
        ESP_LOGD(TAG, "执行移动转向: %.1f度, 速度: %d", angle, speed);
        // TODO: 调用电机控制器同时移动和转向
    }
};

// PersonFollowController实现
PersonFollowController::PersonFollowController() : impl_(std::make_unique<PersonFollowControllerImpl>()) {}

PersonFollowController::~PersonFollowController() = default;

bool PersonFollowController::Initialize() {
    return impl_->Initialize();
}

bool PersonFollowController::StartFollowing(const FollowConfig& config) {
    return impl_->StartFollowing(config);
}

void PersonFollowController::StopFollowing() {
    impl_->StopFollowing();
}

bool PersonFollowController::UpdateTarget(float distance, float angle, float confidence) {
    return impl_->UpdateTarget(distance, angle, confidence);
}

FollowStatus PersonFollowController::GetStatus() const {
    return impl_->GetStatus();
}

bool PersonFollowController::SetObstacleDetected(bool detected) {
    // TODO: 实现障碍物检测处理
    return true;
}

} // namespace motion_system
