/**
 * @file servo_180.h
 * @brief 180度舵机控制器
 * 
 * 基于现有360度舵机控制逻辑，实现180度舵机的精确角度控制
 * 避免360度舵机可能出现的震动和不稳定问题
 */

#pragma once
#include "driver/mcpwm_prelude.h"
#include "driver/gpio.h"
#include "esp_err.h"
#include <functional>
#include <string>
#include <memory>

namespace servo_180 {

/**
 * @brief 180度舵机PWM配置
 */
typedef struct {
    uint32_t min_pulse_width_us;    // 最小脉冲宽度（0度）
    uint32_t max_pulse_width_us;    // 最大脉冲宽度（180度）
    uint32_t neutral_pulse_width_us; // 中性脉冲宽度（90度）
} servo_180_pwm_range_t;

/**
 * @brief 舵机动作类型
 */
enum class ServoAction {
    STOP,              // 停止
    MOVE_TO_ANGLE,     // 移动到指定角度
    WAVE,              // 挥手
    RAISE,             // 举手
    LOWER,             // 放下
    SALUTE,            // 敬礼
    POINT,             // 指向
    SWEEP,             // 扫描
    CUSTOM             // 自定义
};

/**
 * @brief 舵机状态
 */
enum class ServoState {
    IDLE,              // 空闲
    MOVING,            // 运动中
    HOLDING,           // 保持位置
    ERROR              // 错误状态
};

/**
 * @brief 舵机配置
 */
typedef struct {
    gpio_num_t gpio_pin;                    // GPIO引脚
    mcpwm_timer_handle_t timer_handle;      // MCPWM定时器句柄
    mcpwm_oper_handle_t operator_handle;    // MCPWM操作器句柄
    mcpwm_cmpr_handle_t comparator_handle;  // MCPWM比较器句柄
    mcpwm_gen_handle_t generator_handle;    // MCPWM生成器句柄
    servo_180_pwm_range_t pwm_range;        // PWM范围配置
    float current_angle;                    // 当前角度
    float target_angle;                     // 目标角度
    ServoState state;                       // 舵机状态
    std::string servo_id;                   // 舵机ID
} servo_180_config_t;

/**
 * @brief 舵机运动参数
 */
typedef struct {
    float start_angle;      // 起始角度
    float end_angle;        // 结束角度
    uint32_t duration_ms;   // 持续时间（毫秒）
    uint32_t steps;         // 步数
    bool smooth;            // 是否平滑运动
} servo_motion_params_t;

/**
 * @brief 舵机运动完成回调函数类型
 */
typedef std::function<void(const std::string& servo_id, bool success)> servo_motion_callback_t;

/**
 * @brief 180度舵机控制器类
 */
class Servo180Controller {
public:
    /**
     * @brief 构造函数
     * @param left_gpio 左臂舵机GPIO
     * @param right_gpio 右臂舵机GPIO
     */
    Servo180Controller(gpio_num_t left_gpio, gpio_num_t right_gpio);
    
    /**
     * @brief 析构函数
     */
    ~Servo180Controller();
    
    /**
     * @brief 初始化舵机控制器
     * @return 成功返回true，失败返回false
     */
    bool Initialize();
    
    /**
     * @brief 反初始化舵机控制器
     */
    void Deinitialize();
    
    /**
     * @brief 设置舵机角度
     * @param servo_id 舵机ID（"left"或"right"）
     * @param angle 目标角度（0-180度）
     * @param duration_ms 运动持续时间（毫秒）
     * @return 成功返回true，失败返回false
     */
    bool SetAngle(const std::string& servo_id, float angle, uint32_t duration_ms = 1000);
    
    /**
     * @brief 同时设置双臂角度
     * @param left_angle 左臂角度
     * @param right_angle 右臂角度
     * @param duration_ms 运动持续时间
     * @return 成功返回true，失败返回false
     */
    bool SetBothAngles(float left_angle, float right_angle, uint32_t duration_ms = 1000);
    
    /**
     * @brief 执行预定义动作
     * @param action 动作类型
     * @param params 动作参数（可选）
     * @return 成功返回true，失败返回false
     */
    bool ExecuteAction(ServoAction action, const servo_motion_params_t* params = nullptr);
    
    /**
     * @brief 停止舵机运动
     * @param servo_id 舵机ID，空字符串表示停止所有舵机
     */
    void Stop(const std::string& servo_id = "");
    
    /**
     * @brief 获取舵机当前角度
     * @param servo_id 舵机ID
     * @return 当前角度，失败返回-1
     */
    float GetCurrentAngle(const std::string& servo_id) const;
    
    /**
     * @brief 获取舵机状态
     * @param servo_id 舵机ID
     * @return 舵机状态
     */
    ServoState GetServoState(const std::string& servo_id) const;
    
    /**
     * @brief 检查舵机是否在运动
     * @param servo_id 舵机ID，空字符串检查所有舵机
     * @return 运动中返回true，否则返回false
     */
    bool IsMoving(const std::string& servo_id = "") const;
    
    /**
     * @brief 设置运动完成回调函数
     * @param callback 回调函数
     */
    void SetMotionCallback(servo_motion_callback_t callback);
    
    /**
     * @brief 校准舵机
     * @param servo_id 舵机ID
     * @return 成功返回true，失败返回false
     */
    bool CalibrateServo(const std::string& servo_id);
    
    /**
     * @brief 设置舵机PWM范围
     * @param servo_id 舵机ID
     * @param pwm_range PWM范围配置
     * @return 成功返回true，失败返回false
     */
    bool SetPWMRange(const std::string& servo_id, const servo_180_pwm_range_t& pwm_range);
    
    /**
     * @brief 获取控制器状态信息
     * @return 状态信息字符串
     */
    std::string GetStatusInfo() const;

private:
    servo_180_config_t left_servo_;     // 左臂舵机配置
    servo_180_config_t right_servo_;    // 右臂舵机配置
    bool initialized_;                  // 初始化状态
    servo_motion_callback_t motion_callback_; // 运动完成回调
    
    /**
     * @brief 初始化单个舵机
     * @param config 舵机配置
     * @return 成功返回true，失败返回false
     */
    bool InitializeServo(servo_180_config_t& config);
    
    /**
     * @brief 反初始化单个舵机
     * @param config 舵机配置
     */
    void DeinitializeServo(servo_180_config_t& config);
    
    /**
     * @brief 角度转换为PWM占空比
     * @param angle 角度（0-180度）
     * @param pwm_range PWM范围配置
     * @return PWM占空比
     */
    uint32_t AngleToPWM(float angle, const servo_180_pwm_range_t& pwm_range) const;
    
    /**
     * @brief 设置舵机PWM
     * @param config 舵机配置
     * @param angle 目标角度
     * @return 成功返回true，失败返回false
     */
    bool SetServoPWM(servo_180_config_t& config, float angle);
    
    /**
     * @brief 平滑运动到目标角度
     * @param config 舵机配置
     * @param target_angle 目标角度
     * @param duration_ms 持续时间
     * @return 成功返回true，失败返回false
     */
    bool SmoothMoveTo(servo_180_config_t& config, float target_angle, uint32_t duration_ms);
    
    /**
     * @brief 获取舵机配置引用
     * @param servo_id 舵机ID
     * @return 舵机配置引用，失败返回nullptr
     */
    servo_180_config_t* GetServoConfig(const std::string& servo_id);
    
    /**
     * @brief 获取舵机配置引用（常量版本）
     * @param servo_id 舵机ID
     * @return 舵机配置引用，失败返回nullptr
     */
    const servo_180_config_t* GetServoConfig(const std::string& servo_id) const;
    
    /**
     * @brief 执行挥手动作
     * @param servo_id 舵机ID
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteWaveAction(const std::string& servo_id, const servo_motion_params_t* params);
    
    /**
     * @brief 执行举手动作
     * @param servo_id 舵机ID
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteRaiseAction(const std::string& servo_id, const servo_motion_params_t* params);
    
    /**
     * @brief 执行敬礼动作
     * @return 成功返回true，失败返回false
     */
    bool ExecuteSaluteAction();
    
    /**
     * @brief 执行扫描动作
     * @param servo_id 舵机ID
     * @param params 动作参数
     * @return 成功返回true，失败返回false
     */
    bool ExecuteSweepAction(const std::string& servo_id, const servo_motion_params_t* params);
};

/**
 * @brief 创建180度舵机控制器实例
 * @param left_gpio 左臂舵机GPIO
 * @param right_gpio 右臂舵机GPIO
 * @return 舵机控制器实例
 */
std::unique_ptr<Servo180Controller> CreateServo180Controller(gpio_num_t left_gpio, gpio_num_t right_gpio);

/**
 * @brief 舵机动作类型转字符串
 * @param action 动作类型
 * @return 动作名称字符串
 */
std::string ServoActionToString(ServoAction action);

/**
 * @brief 字符串转舵机动作类型
 * @param action_str 动作名称字符串
 * @return 动作类型
 */
ServoAction StringToServoAction(const std::string& action_str);

/**
 * @brief 舵机状态转字符串
 * @param state 舵机状态
 * @return 状态名称字符串
 */
std::string ServoStateToString(ServoState state);

/**
 * @brief 创建默认PWM范围配置
 * @return 默认PWM范围配置
 */
servo_180_pwm_range_t CreateDefaultPWMRange();

/**
 * @brief 创建默认运动参数
 * @param start_angle 起始角度
 * @param end_angle 结束角度
 * @param duration_ms 持续时间
 * @return 运动参数
 */
servo_motion_params_t CreateMotionParams(float start_angle, float end_angle, uint32_t duration_ms);

} // namespace servo_180
