/**
 * @file servo_180.cc
 * @brief 180度舵机控制器实现
 */

#include "servo_180.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <cmath>
#include <algorithm>

static const char* TAG = "Servo180";

namespace servo_180 {

// 默认SG90 180度舵机PWM范围（微秒）
static const servo_180_pwm_range_t default_pwm_range = {
    .min_pulse_width_us = 500,      // 0.5ms - 0度
    .max_pulse_width_us = 2500,     // 2.5ms - 180度
    .neutral_pulse_width_us = 1500  // 1.5ms - 90度
};

#define SERVO_PWM_FREQ 50  // 50Hz
#define SERVO_PWM_PERIOD_US (1000000 / SERVO_PWM_FREQ)

Servo180Controller::Servo180Controller(gpio_num_t left_gpio, gpio_num_t right_gpio)
    : initialized_(false), motion_callback_(nullptr) {
    
    ESP_LOGI(TAG, "创建180度舵机控制器: 左臂GPIO=%d, 右臂GPIO=%d", left_gpio, right_gpio);
    
    // 初始化左臂舵机配置
    left_servo_.gpio_pin = left_gpio;
    left_servo_.servo_id = "left";
    left_servo_.current_angle = 90.0f;
    left_servo_.target_angle = 90.0f;
    left_servo_.state = ServoState::IDLE;
    left_servo_.pwm_range = default_pwm_range;
    left_servo_.timer_handle = nullptr;
    left_servo_.operator_handle = nullptr;
    left_servo_.comparator_handle = nullptr;
    left_servo_.generator_handle = nullptr;
    
    // 初始化右臂舵机配置
    right_servo_.gpio_pin = right_gpio;
    right_servo_.servo_id = "right";
    right_servo_.current_angle = 90.0f;
    right_servo_.target_angle = 90.0f;
    right_servo_.state = ServoState::IDLE;
    right_servo_.pwm_range = default_pwm_range;
    right_servo_.timer_handle = nullptr;
    right_servo_.operator_handle = nullptr;
    right_servo_.comparator_handle = nullptr;
    right_servo_.generator_handle = nullptr;
}

Servo180Controller::~Servo180Controller() {
    ESP_LOGI(TAG, "销毁180度舵机控制器");
    Deinitialize();
}

bool Servo180Controller::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "舵机控制器已初始化");
        return true;
    }
    
    ESP_LOGI(TAG, "初始化180度舵机控制器...");
    
    // 初始化左臂舵机
    if (!InitializeServo(left_servo_)) {
        ESP_LOGE(TAG, "左臂舵机初始化失败");
        return false;
    }
    
    // 初始化右臂舵机
    if (!InitializeServo(right_servo_)) {
        ESP_LOGE(TAG, "右臂舵机初始化失败");
        DeinitializeServo(left_servo_);
        return false;
    }
    
    initialized_ = true;
    ESP_LOGI(TAG, "180度舵机控制器初始化成功");
    
    // 设置初始位置为90度
    SetBothAngles(90.0f, 90.0f, 500);
    
    return true;
}

void Servo180Controller::Deinitialize() {
    if (!initialized_) {
        return;
    }
    
    ESP_LOGI(TAG, "反初始化180度舵机控制器");
    
    // 停止所有运动
    Stop();
    
    // 反初始化舵机
    DeinitializeServo(left_servo_);
    DeinitializeServo(right_servo_);
    
    initialized_ = false;
}

bool Servo180Controller::InitializeServo(servo_180_config_t& config) {
    ESP_LOGI(TAG, "初始化舵机: %s, GPIO=%d", config.servo_id.c_str(), config.gpio_pin);
    
    // 创建MCPWM定时器
    mcpwm_timer_config_t timer_config = {
        .group_id = 0,
        .clk_src = MCPWM_TIMER_CLK_SRC_DEFAULT,
        .resolution_hz = 1000000,  // 1MHz, 1us per tick
        .period_ticks = SERVO_PWM_PERIOD_US,
        .count_mode = MCPWM_TIMER_COUNT_MODE_UP,
    };
    
    esp_err_t ret = mcpwm_new_timer(&timer_config, &config.timer_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建MCPWM定时器失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 创建MCPWM操作器
    mcpwm_operator_config_t operator_config = {
        .group_id = 0,
    };
    
    ret = mcpwm_new_operator(&operator_config, &config.operator_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建MCPWM操作器失败: %s", esp_err_to_name(ret));
        mcpwm_del_timer(config.timer_handle);
        return false;
    }
    
    // 连接定时器和操作器
    ret = mcpwm_operator_connect_timer(config.operator_handle, config.timer_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "连接定时器和操作器失败: %s", esp_err_to_name(ret));
        mcpwm_del_operator(config.operator_handle);
        mcpwm_del_timer(config.timer_handle);
        return false;
    }
    
    // 创建MCPWM比较器
    mcpwm_comparator_config_t comparator_config = {
        .flags = {
            .update_cmp_on_tez = true,
        },
    };
    
    ret = mcpwm_new_comparator(config.operator_handle, &comparator_config, &config.comparator_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建MCPWM比较器失败: %s", esp_err_to_name(ret));
        mcpwm_del_operator(config.operator_handle);
        mcpwm_del_timer(config.timer_handle);
        return false;
    }
    
    // 创建MCPWM生成器
    mcpwm_generator_config_t generator_config = {
        .gen_gpio_num = config.gpio_pin,
    };
    
    ret = mcpwm_new_generator(config.operator_handle, &generator_config, &config.generator_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建MCPWM生成器失败: %s", esp_err_to_name(ret));
        mcpwm_del_comparator(config.comparator_handle);
        mcpwm_del_operator(config.operator_handle);
        mcpwm_del_timer(config.timer_handle);
        return false;
    }
    
    // 设置生成器动作
    ret = mcpwm_generator_set_action_on_timer_event(config.generator_handle,
                                                   MCPWM_GEN_TIMER_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, MCPWM_TIMER_EVENT_EMPTY, MCPWM_GEN_ACTION_HIGH));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置生成器定时器动作失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = mcpwm_generator_set_action_on_compare_event(config.generator_handle,
                                                     MCPWM_GEN_COMPARE_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, config.comparator_handle, MCPWM_GEN_ACTION_LOW));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置生成器比较动作失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 启用定时器
    ret = mcpwm_timer_enable(config.timer_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "启用MCPWM定时器失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 启动定时器
    ret = mcpwm_timer_start_stop(config.timer_handle, MCPWM_TIMER_START_NO_STOP);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "启动MCPWM定时器失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    config.state = ServoState::IDLE;
    ESP_LOGI(TAG, "舵机%s初始化成功", config.servo_id.c_str());
    
    return true;
}

void Servo180Controller::DeinitializeServo(servo_180_config_t& config) {
    ESP_LOGI(TAG, "反初始化舵机: %s", config.servo_id.c_str());
    
    if (config.timer_handle) {
        mcpwm_timer_disable(config.timer_handle);
        mcpwm_del_timer(config.timer_handle);
        config.timer_handle = nullptr;
    }
    
    if (config.generator_handle) {
        mcpwm_del_generator(config.generator_handle);
        config.generator_handle = nullptr;
    }
    
    if (config.comparator_handle) {
        mcpwm_del_comparator(config.comparator_handle);
        config.comparator_handle = nullptr;
    }
    
    if (config.operator_handle) {
        mcpwm_del_operator(config.operator_handle);
        config.operator_handle = nullptr;
    }
    
    config.state = ServoState::IDLE;
}

uint32_t Servo180Controller::AngleToPWM(float angle, const servo_180_pwm_range_t& pwm_range) const {
    // 限制角度范围
    angle = std::clamp(angle, 0.0f, 180.0f);
    
    // 线性插值计算PWM脉冲宽度
    float ratio = angle / 180.0f;
    uint32_t pulse_width = pwm_range.min_pulse_width_us + 
                          (uint32_t)(ratio * (pwm_range.max_pulse_width_us - pwm_range.min_pulse_width_us));
    
    return pulse_width;
}

bool Servo180Controller::SetServoPWM(servo_180_config_t& config, float angle) {
    if (!config.comparator_handle) {
        ESP_LOGE(TAG, "舵机%s未初始化", config.servo_id.c_str());
        return false;
    }
    
    uint32_t pulse_width = AngleToPWM(angle, config.pwm_range);
    
    esp_err_t ret = mcpwm_comparator_set_compare_value(config.comparator_handle, pulse_width);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置舵机%s PWM失败: %s", config.servo_id.c_str(), esp_err_to_name(ret));
        return false;
    }
    
    config.current_angle = angle;
    ESP_LOGD(TAG, "舵机%s设置角度: %.1f度, PWM: %lu微秒", 
             config.servo_id.c_str(), angle, pulse_width);
    
    return true;
}

bool Servo180Controller::SetAngle(const std::string& servo_id, float angle, uint32_t duration_ms) {
    if (!initialized_) {
        ESP_LOGE(TAG, "舵机控制器未初始化");
        return false;
    }
    
    servo_180_config_t* config = GetServoConfig(servo_id);
    if (!config) {
        ESP_LOGE(TAG, "无效的舵机ID: %s", servo_id.c_str());
        return false;
    }
    
    // 限制角度范围
    angle = std::clamp(angle, 0.0f, 180.0f);
    
    ESP_LOGI(TAG, "设置舵机%s角度: %.1f度, 持续时间: %lums", 
             servo_id.c_str(), angle, duration_ms);
    
    if (duration_ms <= 100) {
        // 直接设置角度
        bool success = SetServoPWM(*config, angle);
        config->target_angle = angle;
        config->state = success ? ServoState::HOLDING : ServoState::ERROR;
        
        if (motion_callback_) {
            motion_callback_(servo_id, success);
        }
        
        return success;
    } else {
        // 平滑运动
        return SmoothMoveTo(*config, angle, duration_ms);
    }
}

bool Servo180Controller::SetBothAngles(float left_angle, float right_angle, uint32_t duration_ms) {
    ESP_LOGI(TAG, "设置双臂角度: 左臂%.1f度, 右臂%.1f度, 持续时间: %lums", 
             left_angle, right_angle, duration_ms);
    
    bool left_success = SetAngle("left", left_angle, duration_ms);
    bool right_success = SetAngle("right", right_angle, duration_ms);
    
    return left_success && right_success;
}

servo_180_config_t* Servo180Controller::GetServoConfig(const std::string& servo_id) {
    if (servo_id == "left") {
        return &left_servo_;
    } else if (servo_id == "right") {
        return &right_servo_;
    }
    return nullptr;
}

const servo_180_config_t* Servo180Controller::GetServoConfig(const std::string& servo_id) const {
    if (servo_id == "left") {
        return &left_servo_;
    } else if (servo_id == "right") {
        return &right_servo_;
    }
    return nullptr;
}

float Servo180Controller::GetCurrentAngle(const std::string& servo_id) const {
    const servo_180_config_t* config = GetServoConfig(servo_id);
    if (config) {
        return config->current_angle;
    }
    return -1.0f;
}

ServoState Servo180Controller::GetServoState(const std::string& servo_id) const {
    const servo_180_config_t* config = GetServoConfig(servo_id);
    if (config) {
        return config->state;
    }
    return ServoState::ERROR;
}

bool Servo180Controller::IsMoving(const std::string& servo_id) const {
    if (servo_id.empty()) {
        return (left_servo_.state == ServoState::MOVING) || (right_servo_.state == ServoState::MOVING);
    }
    
    const servo_180_config_t* config = GetServoConfig(servo_id);
    if (config) {
        return config->state == ServoState::MOVING;
    }
    return false;
}

void Servo180Controller::SetMotionCallback(servo_motion_callback_t callback) {
    motion_callback_ = callback;
}

void Servo180Controller::Stop(const std::string& servo_id) {
    ESP_LOGI(TAG, "停止舵机运动: %s", servo_id.empty() ? "全部" : servo_id.c_str());
    
    if (servo_id.empty()) {
        left_servo_.state = ServoState::HOLDING;
        right_servo_.state = ServoState::HOLDING;
    } else {
        servo_180_config_t* config = GetServoConfig(servo_id);
        if (config) {
            config->state = ServoState::HOLDING;
        }
    }
}

// 工厂函数和辅助函数实现
std::unique_ptr<Servo180Controller> CreateServo180Controller(gpio_num_t left_gpio, gpio_num_t right_gpio) {
    return std::make_unique<Servo180Controller>(left_gpio, right_gpio);
}

std::string ServoActionToString(ServoAction action) {
    switch (action) {
        case ServoAction::STOP: return "STOP";
        case ServoAction::MOVE_TO_ANGLE: return "MOVE_TO_ANGLE";
        case ServoAction::WAVE: return "WAVE";
        case ServoAction::RAISE: return "RAISE";
        case ServoAction::LOWER: return "LOWER";
        case ServoAction::SALUTE: return "SALUTE";
        case ServoAction::POINT: return "POINT";
        case ServoAction::SWEEP: return "SWEEP";
        case ServoAction::CUSTOM: return "CUSTOM";
        default: return "UNKNOWN";
    }
}

ServoAction StringToServoAction(const std::string& action_str) {
    if (action_str == "STOP") return ServoAction::STOP;
    if (action_str == "MOVE_TO_ANGLE") return ServoAction::MOVE_TO_ANGLE;
    if (action_str == "WAVE") return ServoAction::WAVE;
    if (action_str == "RAISE") return ServoAction::RAISE;
    if (action_str == "LOWER") return ServoAction::LOWER;
    if (action_str == "SALUTE") return ServoAction::SALUTE;
    if (action_str == "POINT") return ServoAction::POINT;
    if (action_str == "SWEEP") return ServoAction::SWEEP;
    if (action_str == "CUSTOM") return ServoAction::CUSTOM;
    return ServoAction::STOP;
}

std::string ServoStateToString(ServoState state) {
    switch (state) {
        case ServoState::IDLE: return "IDLE";
        case ServoState::MOVING: return "MOVING";
        case ServoState::HOLDING: return "HOLDING";
        case ServoState::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

servo_180_pwm_range_t CreateDefaultPWMRange() {
    return default_pwm_range;
}

servo_motion_params_t CreateMotionParams(float start_angle, float end_angle, uint32_t duration_ms) {
    servo_motion_params_t params;
    params.start_angle = start_angle;
    params.end_angle = end_angle;
    params.duration_ms = duration_ms;
    params.steps = duration_ms / 20;  // 20ms per step
    params.smooth = true;
    return params;
}

bool Servo180Controller::SmoothMoveTo(servo_180_config_t& config, float target_angle, uint32_t duration_ms) {
    config.target_angle = target_angle;
    config.state = ServoState::MOVING;

    float start_angle = config.current_angle;
    float angle_diff = target_angle - start_angle;
    uint32_t steps = duration_ms / 20;  // 20ms per step

    if (steps == 0) steps = 1;

    for (uint32_t i = 0; i <= steps; i++) {
        float progress = (float)i / steps;
        float current_angle = start_angle + angle_diff * progress;

        if (!SetServoPWM(config, current_angle)) {
            config.state = ServoState::ERROR;
            return false;
        }

        if (i < steps) {
            vTaskDelay(pdMS_TO_TICKS(20));
        }
    }

    config.state = ServoState::HOLDING;

    if (motion_callback_) {
        motion_callback_(config.servo_id, true);
    }

    return true;
}

bool Servo180Controller::ExecuteAction(ServoAction action, const servo_motion_params_t* params) {
    ESP_LOGI(TAG, "执行动作: %s", ServoActionToString(action).c_str());

    switch (action) {
        case ServoAction::WAVE:
            return ExecuteWaveAction("right", params);

        case ServoAction::RAISE:
            return ExecuteRaiseAction("both", params);

        case ServoAction::SALUTE:
            return ExecuteSaluteAction();

        case ServoAction::SWEEP:
            return ExecuteSweepAction("right", params);

        case ServoAction::LOWER:
            return SetBothAngles(90.0f, 90.0f, 1000);

        case ServoAction::STOP:
            Stop();
            return true;

        default:
            ESP_LOGW(TAG, "不支持的动作类型");
            return false;
    }
}

bool Servo180Controller::ExecuteWaveAction(const std::string& servo_id, const servo_motion_params_t* params) {
    ESP_LOGI(TAG, "执行挥手动作: %s", servo_id.c_str());

    // 默认挥手参数
    float wave_angles[] = {90.0f, 45.0f, 135.0f, 45.0f, 135.0f, 90.0f};
    uint32_t wave_duration = 500;

    if (params) {
        wave_duration = params->duration_ms / 6;  // 分6个步骤
    }

    for (int i = 0; i < 6; i++) {
        if (!SetAngle(servo_id, wave_angles[i], wave_duration)) {
            return false;
        }
        vTaskDelay(pdMS_TO_TICKS(wave_duration));
    }

    return true;
}

bool Servo180Controller::ExecuteRaiseAction(const std::string& servo_id, const servo_motion_params_t* params) {
    ESP_LOGI(TAG, "执行举手动作: %s", servo_id.c_str());

    uint32_t duration = params ? params->duration_ms : 1000;

    if (servo_id == "both") {
        return SetBothAngles(45.0f, 45.0f, duration);
    } else {
        return SetAngle(servo_id, 45.0f, duration);
    }
}

bool Servo180Controller::ExecuteSaluteAction() {
    ESP_LOGI(TAG, "执行敬礼动作");

    // 右手敬礼
    if (!SetAngle("right", 45.0f, 1000)) {
        return false;
    }

    vTaskDelay(pdMS_TO_TICKS(2000));  // 保持2秒

    // 回到初始位置
    return SetAngle("right", 90.0f, 1000);
}

bool Servo180Controller::ExecuteSweepAction(const std::string& servo_id, const servo_motion_params_t* params) {
    ESP_LOGI(TAG, "执行扫描动作: %s", servo_id.c_str());

    uint32_t step_duration = params ? (params->duration_ms / 4) : 500;

    // 扫描动作：0度 -> 180度 -> 0度
    if (!SetAngle(servo_id, 0.0f, step_duration)) return false;
    vTaskDelay(pdMS_TO_TICKS(step_duration));

    if (!SetAngle(servo_id, 180.0f, step_duration * 2)) return false;
    vTaskDelay(pdMS_TO_TICKS(step_duration * 2));

    if (!SetAngle(servo_id, 90.0f, step_duration)) return false;

    return true;
}

bool Servo180Controller::CalibrateServo(const std::string& servo_id) {
    ESP_LOGI(TAG, "校准舵机: %s", servo_id.c_str());

    servo_180_config_t* config = GetServoConfig(servo_id);
    if (!config) {
        return false;
    }

    // 校准过程：移动到几个关键位置
    float calibration_angles[] = {0.0f, 90.0f, 180.0f, 90.0f};

    for (float angle : calibration_angles) {
        if (!SetAngle(servo_id, angle, 1000)) {
            ESP_LOGE(TAG, "校准失败在角度: %.1f", angle);
            return false;
        }
        vTaskDelay(pdMS_TO_TICKS(1500));
    }

    ESP_LOGI(TAG, "舵机%s校准完成", servo_id.c_str());
    return true;
}

bool Servo180Controller::SetPWMRange(const std::string& servo_id, const servo_180_pwm_range_t& pwm_range) {
    servo_180_config_t* config = GetServoConfig(servo_id);
    if (!config) {
        return false;
    }

    config->pwm_range = pwm_range;
    ESP_LOGI(TAG, "设置舵机%s PWM范围: min=%lu, neutral=%lu, max=%lu",
             servo_id.c_str(), pwm_range.min_pulse_width_us,
             pwm_range.neutral_pulse_width_us, pwm_range.max_pulse_width_us);

    return true;
}

std::string Servo180Controller::GetStatusInfo() const {
    std::string status = "Servo180Controller Status:\n";
    status += "  Initialized: " + std::string(initialized_ ? "Yes" : "No") + "\n";
    status += "  Left Servo: " + ServoStateToString(left_servo_.state) +
              ", Angle: " + std::to_string(left_servo_.current_angle) + "°\n";
    status += "  Right Servo: " + ServoStateToString(right_servo_.state) +
              ", Angle: " + std::to_string(right_servo_.current_angle) + "°\n";

    return status;
}

} // namespace servo_180
