# AI玩具系统 - 多Agent开发架构

## 项目概述

基于ESP32-S3 R16N8开发板构建的智能AI玩具系统，采用四层架构设计，实现语音交互、表情显示、运动控制、人物跟随等功能。

## 系统架构

### 四层架构设计

```
┌─────────────────────────────────────────┐
│           小智AI交互层                    │
│     (ai_layer/ai_interface.h)          │
├─────────────────────────────────────────┤
│           核心任务层                      │
│    (core_layer/task_manager.h)         │
├─────────────────────────────────────────┤
│           设备抽象层                      │
│   (device_layer/device_interface.h)    │
├─────────────────────────────────────────┤
│           硬件驱动层                      │
│  (hardware_layer/mcp_communication.h)  │
└─────────────────────────────────────────┘
```

### 功能模块

1. **表情系统** (`expression_system/`)
   - 双屏表情显示
   - LVGL图形渲染
   - 动画效果管理
   - 情感化表达

2. **运动系统** (`motion_system/`)
   - 舵机控制
   - 电机驱动
   - 人物跟随算法
   - 动作协调

3. **传感器系统** (`sensor_system/`)
   - 毫米波雷达
   - 超声波传感器
   - 红外传感器
   - 避障避险

4. **系统控制器** (`system_controller.h`)
   - 多模块协调
   - 状态管理
   - 事件处理
   - 安全监控

## 多Agent开发模式

### Agent角色分工

- **架构师Agent**: 系统设计、接口定义
- **硬件工程师Agent**: 驱动开发、MCP通信
- **UI设计师Agent**: 表情设计、动画效果
- **AI集成工程师Agent**: 小智AI集成、语音处理
- **算法工程师Agent**: 跟随算法、避障逻辑
- **测试工程师Agent**: 质量保证、性能验证

### 开发流程

1. **架构设计阶段**
   - 定义四层架构接口
   - 制定通信协议
   - 设计数据结构

2. **并行开发阶段**
   - 各Agent独立开发对应模块
   - 定期代码审查和集成
   - 持续测试验证

3. **集成测试阶段**
   - 模块间协作测试
   - 性能优化调整
   - 用户体验验证

## 核心特性

### 1. 表情系统
- **双屏显示**: 支持左右眼独立控制
- **丰富表情**: 开心、伤心、惊讶、困惑等多种表情
- **动画效果**: 眨眼、脉冲、跟随等动画
- **实时渲染**: 基于LVGL的高效图形渲染

### 2. 语音交互
- **语音识别**: 集成小智AI语音识别
- **意图理解**: 智能解析用户指令
- **情感回复**: 根据情感状态生成回复
- **多模态交互**: 语音+表情+动作协同

### 3. 运动控制（已升级为180度舵机）
- **精确角度控制**: 180度标准舵机，精度±2度
- **平滑运动**: 智能分步运动算法，避免突然跳跃
- **语音控制**: 支持自然语言指令控制舵机动作
- **双臂协调**: 同步和异步运动模式
- **电机驱动**: 平滑的移动和转向控制
- **人物跟随**: 基于传感器的智能跟随
- **安全保护**: 避障和紧急停止机制

### 4. 传感器融合
- **多传感器**: 毫米波雷达、超声波、红外
- **数据融合**: 多源数据综合分析
- **环境感知**: 障碍物、悬崖、人物检测
- **实时响应**: 快速的环境变化响应

## 硬件配置

### 主控制器
- **MCU**: ESP32-S3 R16N8
- **内存**: 16MB Flash + 8MB PSRAM
- **通信**: WiFi + Bluetooth

### 扩展芯片
- **MCP23017**: I2C GPIO扩展芯片
- **地址**: 0x20 (可配置)
- **引脚**: 16个GPIO扩展引脚

### 外设配置
- **显示屏**: 双240x240 LCD屏幕
- **舵机**: 180度标准舵机 - 左臂(GPIO9) + 右臂(GPIO10)
- **电机**: 差分驱动轮式底盘
- **传感器**: 前置毫米波雷达 + 多方向超声波

## 开发指南

### 环境搭建

1. **安装ESP-IDF**
   ```bash
   git clone https://github.com/espressif/esp-idf.git
   cd esp-idf
   ./install.sh
   . ./export.sh
   ```

2. **配置项目**
   ```bash
   cd xiaozhi-esp32
   idf.py menuconfig
   ```

3. **编译烧录**
   ```bash
   idf.py build
   idf.py flash monitor
   ```

### 代码结构

```
main/boards/custom-hardware/
├── ai_layer/                    # AI交互层
│   ├── ai_interface.h          # AI接口定义
│   └── xiaozhi_ai_impl.cc      # 小智AI实现
├── core_layer/                 # 核心任务层
│   ├── task_manager.h          # 任务管理器
│   └── task_manager.cc
├── device_layer/               # 设备抽象层
│   ├── device_interface.h      # 设备接口
│   └── device_manager.cc       # 设备管理器
├── hardware_layer/             # 硬件驱动层
│   ├── mcp_communication.h     # MCP通信
│   └── mcp_communication.cc
├── expression_system/          # 表情系统
│   ├── expression_manager.h    # 表情管理器
│   └── expression_manager.cc
├── motion_system/              # 运动系统
│   ├── motion_controller.h     # 运动控制器
│   └── motion_controller.cc
├── sensor_system/              # 传感器系统
│   ├── sensor_manager.h        # 传感器管理器
│   └── sensor_manager.cc
├── system_controller.h         # 系统主控制器
├── system_controller.cc
├── ai_toy_demo.cc             # 演示程序
└── README.md                  # 本文档
```

### 添加新功能

1. **定义接口**
   ```cpp
   // 在对应的头文件中定义接口
   class INewFeature {
   public:
       virtual bool Initialize() = 0;
       virtual bool Execute() = 0;
   };
   ```

2. **实现功能**
   ```cpp
   // 创建具体实现类
   class NewFeatureImpl : public INewFeature {
       bool Initialize() override { /* 实现 */ }
       bool Execute() override { /* 实现 */ }
   };
   ```

3. **集成到系统**
   ```cpp
   // 在系统控制器中注册和使用
   auto feature = std::make_unique<NewFeatureImpl>();
   feature->Initialize();
   ```

## 演示程序

系统包含完整的演示程序 (`ai_toy_demo.cc`)，展示各个功能模块：

- **表情演示**: 循环显示不同表情和动画
- **语音交互**: 模拟语音指令处理
- **运动控制**: 演示各种运动动作
- **传感器监控**: 显示传感器数据
- **系统状态**: 监控系统运行状态

## 配置参数

### 系统配置
```cpp
SystemConfig config;
config.default_brightness = 80;        // 默认亮度
config.interaction_timeout = 30000;    // 交互超时(ms)
config.follow_min_distance = 1.0f;     // 最小跟随距离(m)
config.safe_distance = 0.3f;           // 安全距离(m)
```

### 表情配置
```cpp
ExpressionConfig happy;
happy.pupil_size = 60;                 // 瞳孔大小
happy.eye_width = 90;                  // 眼睛宽度
happy.has_sparkle = true;              // 闪烁效果
```

## 调试和测试

### 日志输出
系统使用ESP-IDF的日志系统，支持不同级别的日志输出：
```cpp
ESP_LOGI(TAG, "Information message");
ESP_LOGW(TAG, "Warning message");
ESP_LOGE(TAG, "Error message");
```

### 性能监控
- **内存使用**: `esp_get_free_heap_size()`
- **任务状态**: 任务管理器提供详细状态
- **系统负载**: 实时监控各模块运行状态

### 测试用例
- **单元测试**: 各模块独立功能测试
- **集成测试**: 模块间协作测试
- **压力测试**: 长时间运行稳定性测试

## 扩展开发

### 添加新传感器
1. 继承 `ISensor` 接口
2. 实现具体传感器类
3. 注册到传感器管理器

### 添加新表情
1. 定义表情配置
2. 添加到预定义表情库
3. 实现对应的渲染逻辑

### 添加新动作
1. 定义动作类型枚举
2. 实现动作执行逻辑
3. 集成到运动控制器

## 故障排除

### 常见问题
1. **编译错误**: 检查依赖库和头文件路径
2. **运行异常**: 查看串口日志输出
3. **通信失败**: 检查I2C连接和地址配置
4. **显示异常**: 验证LVGL配置和显示驱动

### 调试技巧
- 使用串口监控查看实时日志
- 通过LED指示灯观察系统状态
- 分模块测试定位问题范围
- 使用示波器检查硬件信号

## 贡献指南

1. **代码规范**: 遵循项目编码规范
2. **文档更新**: 及时更新相关文档
3. **测试验证**: 确保新功能通过测试
4. **代码审查**: 提交前进行代码审查

## 许可证

本项目采用开源许可证，具体条款请参考项目根目录的LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库: https://github.com/eastxiaodong/xiaozhi-esp32
- 邮箱: <EMAIL>

---

*本文档持续更新中，最新版本请查看项目仓库。*
