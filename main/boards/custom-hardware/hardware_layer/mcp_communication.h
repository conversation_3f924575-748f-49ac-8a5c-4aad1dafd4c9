/**
 * @file mcp_communication.h
 * @brief 硬件驱动层 - MCP通信协议实现
 * 
 * 负责与MCP23017扩展芯片的通信，实现GPIO扩展和硬件控制
 * 提供统一的硬件访问接口
 */

#ifndef MCP_COMMUNICATION_H
#define MCP_COMMUNICATION_H

#include <stdint.h>
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <memory>
#include <mutex>
#include "driver/i2c_master.h"
#include "driver/gpio.h"

namespace hardware_layer {

/**
 * @brief MCP23017寄存器地址定义
 */
enum class MCPRegister : uint8_t {
    IODIRA = 0x00,     // GPIO方向寄存器A
    IODIRB = 0x01,     // GPIO方向寄存器B
    IPOLA = 0x02,      // 输入极性寄存器A
    IPOLB = 0x03,      // 输入极性寄存器B
    GPINTENA = 0x04,   // 中断使能寄存器A
    GPINTENB = 0x05,   // 中断使能寄存器B
    DEFVALA = 0x06,    // 默认值寄存器A
    DEFVALB = 0x07,    // 默认值寄存器B
    INTCONA = 0x08,    // 中断控制寄存器A
    INTCONB = 0x09,    // 中断控制寄存器B
    IOCONA = 0x0A,     // I/O配置寄存器A
    IOCONB = 0x0B,     // I/O配置寄存器B
    GPPUA = 0x0C,      // 上拉电阻寄存器A
    GPPUB = 0x0D,      // 上拉电阻寄存器B
    INTFA = 0x0E,      // 中断标志寄存器A
    INTFB = 0x0F,      // 中断标志寄存器B
    INTCAPA = 0x10,    // 中断捕获寄存器A
    INTCAPB = 0x11,    // 中断捕获寄存器B
    GPIOA = 0x12,      // GPIO寄存器A
    GPIOB = 0x13,      // GPIO寄存器B
    OLATA = 0x14,      // 输出锁存寄存器A
    OLATB = 0x15       // 输出锁存寄存器B
};

/**
 * @brief GPIO引脚模式
 */
enum class GPIOMode {
    INPUT,             // 输入模式
    OUTPUT,            // 输出模式
    INPUT_PULLUP       // 输入上拉模式
};

/**
 * @brief GPIO引脚状态
 */
enum class GPIOState {
    LOW = 0,           // 低电平
    HIGH = 1           // 高电平
};

/**
 * @brief MCP设备信息
 */
struct MCPDeviceInfo {
    uint8_t address;           // I2C地址
    std::string name;          // 设备名称
    bool is_connected;         // 是否连接
    uint32_t last_access_time; // 最后访问时间
    uint32_t error_count;      // 错误计数
    
    MCPDeviceInfo() : address(0x20), is_connected(false), 
                     last_access_time(0), error_count(0) {}
};

/**
 * @brief GPIO引脚配置
 */
struct GPIOConfig {
    uint8_t pin;               // 引脚号(0-15)
    GPIOMode mode;             // 引脚模式
    GPIOState initial_state;   // 初始状态
    bool interrupt_enable;     // 是否使能中断
    std::string description;   // 引脚描述
    
    GPIOConfig() : pin(0), mode(GPIOMode::INPUT), 
                  initial_state(GPIOState::LOW), interrupt_enable(false) {}
};

/**
 * @brief 硬件设备映射
 */
struct HardwareMapping {
    std::string device_name;   // 设备名称
    std::vector<uint8_t> pins; // 使用的引脚
    std::string device_type;   // 设备类型
    std::map<std::string, std::string> parameters; // 设备参数
};

/**
 * @brief MCP通信回调接口
 */
class MCPCallback {
public:
    virtual ~MCPCallback() = default;
    
    // GPIO状态变化回调
    virtual void OnGPIOStateChanged(uint8_t pin, GPIOState state) = 0;
    
    // 中断回调
    virtual void OnInterrupt(uint8_t pin) = 0;
    
    // 通信错误回调
    virtual void OnCommunicationError(const std::string& error_message) = 0;
    
    // 设备连接状态变化回调
    virtual void OnDeviceConnectionChanged(uint8_t address, bool connected) = 0;
};

/**
 * @brief MCP23017通信类
 */
class MCP23017 {
public:
    /**
     * @brief 构造函数
     * @param i2c_port I2C端口
     * @param address I2C地址
     * @param sda_pin SDA引脚
     * @param scl_pin SCL引脚
     */
    MCP23017(i2c_port_t i2c_port, uint8_t address, gpio_num_t sda_pin, gpio_num_t scl_pin);
    
    /**
     * @brief 析构函数
     */
    ~MCP23017();
    
    /**
     * @brief 初始化MCP23017
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 反初始化
     */
    void Deinitialize();
    
    /**
     * @brief 配置GPIO引脚
     * @param pin 引脚号(0-15)
     * @param mode 引脚模式
     * @param initial_state 初始状态
     * @return 是否成功
     */
    bool ConfigureGPIO(uint8_t pin, GPIOMode mode, GPIOState initial_state = GPIOState::LOW);
    
    /**
     * @brief 设置GPIO状态
     * @param pin 引脚号
     * @param state 引脚状态
     * @return 是否成功
     */
    bool SetGPIOState(uint8_t pin, GPIOState state);
    
    /**
     * @brief 获取GPIO状态
     * @param pin 引脚号
     * @return 引脚状态
     */
    GPIOState GetGPIOState(uint8_t pin);
    
    /**
     * @brief 批量设置GPIO状态
     * @param port_a A端口状态(bit0-7)
     * @param port_b B端口状态(bit8-15)
     * @return 是否成功
     */
    bool SetPortStates(uint8_t port_a, uint8_t port_b);
    
    /**
     * @brief 批量获取GPIO状态
     * @param port_a A端口状态输出
     * @param port_b B端口状态输出
     * @return 是否成功
     */
    bool GetPortStates(uint8_t& port_a, uint8_t& port_b);
    
    /**
     * @brief 使能中断
     * @param pin 引脚号
     * @param enable 是否使能
     * @return 是否成功
     */
    bool EnableInterrupt(uint8_t pin, bool enable);
    
    /**
     * @brief 读取中断状态
     * @param port_a A端口中断状态
     * @param port_b B端口中断状态
     * @return 是否成功
     */
    bool ReadInterruptStatus(uint8_t& port_a, uint8_t& port_b);
    
    /**
     * @brief 清除中断
     * @return 是否成功
     */
    bool ClearInterrupt();
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口
     */
    void SetCallback(std::shared_ptr<MCPCallback> callback);
    
    /**
     * @brief 检查设备连接状态
     * @return 是否连接
     */
    bool IsConnected();
    
    /**
     * @brief 获取设备信息
     * @return 设备信息
     */
    MCPDeviceInfo GetDeviceInfo() const;
    
    /**
     * @brief 重置设备
     * @return 是否成功
     */
    bool Reset();
    
    /**
     * @brief 读取寄存器
     * @param reg 寄存器地址
     * @param data 读取的数据
     * @return 是否成功
     */
    bool ReadRegister(MCPRegister reg, uint8_t& data);
    
    /**
     * @brief 写入寄存器
     * @param reg 寄存器地址
     * @param data 写入的数据
     * @return 是否成功
     */
    bool WriteRegister(MCPRegister reg, uint8_t data);

private:
    i2c_port_t i2c_port_;
    uint8_t address_;
    gpio_num_t sda_pin_;
    gpio_num_t scl_pin_;
    i2c_master_bus_handle_t bus_handle_;
    i2c_master_dev_handle_t dev_handle_;
    std::shared_ptr<MCPCallback> callback_;
    MCPDeviceInfo device_info_;
    std::vector<GPIOConfig> gpio_configs_;
    mutable std::mutex mutex_;
    bool initialized_;
    
    // 内部辅助函数
    bool InitializeI2C();
    bool ConfigureDevice();
    uint8_t PinToPort(uint8_t pin);
    uint8_t PinToBit(uint8_t pin);
    MCPRegister GetDirectionRegister(uint8_t pin);
    MCPRegister GetOutputRegister(uint8_t pin);
    MCPRegister GetInputRegister(uint8_t pin);
};

/**
 * @brief MCP通信管理器
 */
class MCPCommunicationManager {
public:
    static MCPCommunicationManager& GetInstance();
    
    /**
     * @brief 初始化通信管理器
     * @return 是否成功
     */
    bool Initialize();
    
    /**
     * @brief 添加MCP设备
     * @param device MCP设备实例
     * @return 是否成功
     */
    bool AddDevice(std::shared_ptr<MCP23017> device);
    
    /**
     * @brief 移除MCP设备
     * @param address 设备地址
     * @return 是否成功
     */
    bool RemoveDevice(uint8_t address);
    
    /**
     * @brief 获取MCP设备
     * @param address 设备地址
     * @return 设备实例
     */
    std::shared_ptr<MCP23017> GetDevice(uint8_t address);
    
    /**
     * @brief 配置硬件映射
     * @param mapping 硬件映射配置
     * @return 是否成功
     */
    bool ConfigureHardwareMapping(const HardwareMapping& mapping);
    
    /**
     * @brief 控制硬件设备
     * @param device_name 设备名称
     * @param command 控制命令
     * @param parameters 命令参数
     * @return 是否成功
     */
    bool ControlHardware(const std::string& device_name, const std::string& command,
                        const std::map<std::string, std::string>& parameters);
    
    /**
     * @brief 读取硬件数据
     * @param device_name 设备名称
     * @param data_type 数据类型
     * @return 数据字符串
     */
    std::string ReadHardwareData(const std::string& device_name, const std::string& data_type);
    
    /**
     * @brief 获取所有设备信息
     * @return 设备信息列表
     */
    std::vector<MCPDeviceInfo> GetAllDeviceInfo() const;
    
    /**
     * @brief 设置全局回调
     * @param callback 回调接口
     */
    void SetGlobalCallback(std::shared_ptr<MCPCallback> callback);
    
    /**
     * @brief 启动监控任务
     */
    void StartMonitoring();
    
    /**
     * @brief 停止监控任务
     */
    void StopMonitoring();

private:
    MCPCommunicationManager() = default;
    ~MCPCommunicationManager() = default;
    MCPCommunicationManager(const MCPCommunicationManager&) = delete;
    MCPCommunicationManager& operator=(const MCPCommunicationManager&) = delete;
    
    static void MonitoringTask(void* parameter);
    
    std::map<uint8_t, std::shared_ptr<MCP23017>> devices_;
    std::map<std::string, HardwareMapping> hardware_mappings_;
    std::shared_ptr<MCPCallback> global_callback_;
    TaskHandle_t monitoring_task_;
    bool monitoring_running_;
    bool initialized_;
    mutable std::mutex mutex_;
};

} // namespace hardware_layer

#endif // MCP_COMMUNICATION_H
