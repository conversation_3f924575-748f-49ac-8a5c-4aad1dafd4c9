/**
 * @file mcp_communication.cc
 * @brief MCP23017通信协议实现
 */

#include "mcp_communication.h"
#include <esp_log.h>
#include <esp_err.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <cstring>

static const char* TAG = "MCP_COMM";

namespace hardware_layer {

// MCP23017构造函数
MCP23017::MCP23017(i2c_port_t i2c_port, uint8_t address, gpio_num_t sda_pin, gpio_num_t scl_pin)
    : i2c_port_(i2c_port), address_(address), sda_pin_(sda_pin), scl_pin_(scl_pin),
      bus_handle_(nullptr), dev_handle_(nullptr), initialized_(false) {
    device_info_.address = address;
    device_info_.name = "MCP23017_" + std::to_string(address);
    gpio_configs_.resize(16);
}

MCP23017::~MCP23017() {
    Deinitialize();
}

bool MCP23017::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        ESP_LOGW(TAG, "MCP23017 already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing MCP23017 at address 0x%02X", address_);
    
    // 初始化I2C总线
    if (!InitializeI2C()) {
        ESP_LOGE(TAG, "Failed to initialize I2C bus");
        return false;
    }
    
    // 配置MCP23017设备
    if (!ConfigureDevice()) {
        ESP_LOGE(TAG, "Failed to configure MCP23017 device");
        return false;
    }
    
    // 检查设备连接
    if (!IsConnected()) {
        ESP_LOGE(TAG, "MCP23017 device not responding");
        return false;
    }
    
    device_info_.is_connected = true;
    device_info_.last_access_time = xTaskGetTickCount();
    initialized_ = true;
    
    ESP_LOGI(TAG, "MCP23017 initialized successfully");
    return true;
}

void MCP23017::Deinitialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    if (dev_handle_) {
        i2c_master_bus_rm_device(dev_handle_);
        dev_handle_ = nullptr;
    }
    
    if (bus_handle_) {
        i2c_del_master_bus(bus_handle_);
        bus_handle_ = nullptr;
    }
    
    device_info_.is_connected = false;
    initialized_ = false;
    
    ESP_LOGI(TAG, "MCP23017 deinitialized");
}

bool MCP23017::InitializeI2C() {
    // 配置I2C主机总线
    i2c_master_bus_config_t bus_config = {};
    bus_config.clk_source = I2C_CLK_SRC_DEFAULT;
    bus_config.i2c_port = i2c_port_;
    bus_config.scl_io_num = scl_pin_;
    bus_config.sda_io_num = sda_pin_;
    bus_config.glitch_ignore_cnt = 7;
    bus_config.flags.enable_internal_pullup = true;
    
    esp_err_t ret = i2c_new_master_bus(&bus_config, &bus_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create I2C master bus: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 配置I2C设备
    i2c_device_config_t dev_config = {};
    dev_config.dev_addr_length = I2C_ADDR_BIT_LEN_7;
    dev_config.device_address = address_;
    dev_config.scl_speed_hz = 400000; // 400kHz
    
    ret = i2c_master_bus_add_device(bus_handle_, &dev_config, &dev_handle_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to add I2C device: %s", esp_err_to_name(ret));
        i2c_del_master_bus(bus_handle_);
        bus_handle_ = nullptr;
        return false;
    }
    
    return true;
}

bool MCP23017::ConfigureDevice() {
    // 设置IOCON寄存器 - 启用地址指针自动递增
    uint8_t iocon_value = 0x20; // SEQOP = 0 (sequential operation enabled)
    if (!WriteRegister(MCPRegister::IOCONA, iocon_value)) {
        ESP_LOGE(TAG, "Failed to configure IOCON register");
        return false;
    }
    
    // 初始化所有GPIO为输入模式
    if (!WriteRegister(MCPRegister::IODIRA, 0xFF) || 
        !WriteRegister(MCPRegister::IODIRB, 0xFF)) {
        ESP_LOGE(TAG, "Failed to set GPIO direction");
        return false;
    }
    
    // 清除输出锁存器
    if (!WriteRegister(MCPRegister::OLATA, 0x00) || 
        !WriteRegister(MCPRegister::OLATB, 0x00)) {
        ESP_LOGE(TAG, "Failed to clear output latches");
        return false;
    }
    
    return true;
}

bool MCP23017::ConfigureGPIO(uint8_t pin, GPIOMode mode, GPIOState initial_state) {
    if (pin >= 16) {
        ESP_LOGE(TAG, "Invalid pin number: %d", pin);
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 更新配置
    gpio_configs_[pin].pin = pin;
    gpio_configs_[pin].mode = mode;
    gpio_configs_[pin].initial_state = initial_state;
    
    // 获取寄存器地址
    MCPRegister dir_reg = GetDirectionRegister(pin);
    uint8_t port_bit = PinToBit(pin);
    
    // 读取当前方向寄存器值
    uint8_t dir_value;
    if (!ReadRegister(dir_reg, dir_value)) {
        ESP_LOGE(TAG, "Failed to read direction register for pin %d", pin);
        return false;
    }
    
    // 设置引脚方向
    if (mode == GPIOMode::INPUT || mode == GPIOMode::INPUT_PULLUP) {
        dir_value |= (1 << port_bit);  // 设置为输入
    } else {
        dir_value &= ~(1 << port_bit); // 设置为输出
    }
    
    if (!WriteRegister(dir_reg, dir_value)) {
        ESP_LOGE(TAG, "Failed to write direction register for pin %d", pin);
        return false;
    }
    
    // 如果是上拉输入模式，启用上拉电阻
    if (mode == GPIOMode::INPUT_PULLUP) {
        MCPRegister pullup_reg = (pin < 8) ? MCPRegister::GPPUA : MCPRegister::GPPUB;
        uint8_t pullup_value;
        if (!ReadRegister(pullup_reg, pullup_value)) {
            ESP_LOGE(TAG, "Failed to read pullup register for pin %d", pin);
            return false;
        }
        pullup_value |= (1 << port_bit);
        if (!WriteRegister(pullup_reg, pullup_value)) {
            ESP_LOGE(TAG, "Failed to write pullup register for pin %d", pin);
            return false;
        }
    }
    
    // 如果是输出模式，设置初始状态
    if (mode == GPIOMode::OUTPUT) {
        if (!SetGPIOState(pin, initial_state)) {
            ESP_LOGE(TAG, "Failed to set initial state for pin %d", pin);
            return false;
        }
    }
    
    ESP_LOGI(TAG, "Configured GPIO pin %d as %s", pin, 
             (mode == GPIOMode::OUTPUT) ? "OUTPUT" : "INPUT");
    
    return true;
}

bool MCP23017::SetGPIOState(uint8_t pin, GPIOState state) {
    if (pin >= 16) {
        ESP_LOGE(TAG, "Invalid pin number: %d", pin);
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    MCPRegister output_reg = GetOutputRegister(pin);
    uint8_t port_bit = PinToBit(pin);
    
    uint8_t output_value;
    if (!ReadRegister(output_reg, output_value)) {
        ESP_LOGE(TAG, "Failed to read output register for pin %d", pin);
        return false;
    }
    
    if (state == GPIOState::HIGH) {
        output_value |= (1 << port_bit);
    } else {
        output_value &= ~(1 << port_bit);
    }
    
    if (!WriteRegister(output_reg, output_value)) {
        ESP_LOGE(TAG, "Failed to write output register for pin %d", pin);
        return false;
    }
    
    device_info_.last_access_time = xTaskGetTickCount();
    return true;
}

GPIOState MCP23017::GetGPIOState(uint8_t pin) {
    if (pin >= 16) {
        ESP_LOGE(TAG, "Invalid pin number: %d", pin);
        return GPIOState::LOW;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    MCPRegister input_reg = GetInputRegister(pin);
    uint8_t port_bit = PinToBit(pin);
    
    uint8_t input_value;
    if (!ReadRegister(input_reg, input_value)) {
        ESP_LOGE(TAG, "Failed to read input register for pin %d", pin);
        return GPIOState::LOW;
    }
    
    device_info_.last_access_time = xTaskGetTickCount();
    return (input_value & (1 << port_bit)) ? GPIOState::HIGH : GPIOState::LOW;
}

bool MCP23017::IsConnected() {
    uint8_t test_value;
    return ReadRegister(MCPRegister::IOCONA, test_value);
}

bool MCP23017::ReadRegister(MCPRegister reg, uint8_t& data) {
    if (!dev_handle_) {
        ESP_LOGE(TAG, "Device not initialized");
        return false;
    }
    
    uint8_t reg_addr = static_cast<uint8_t>(reg);
    esp_err_t ret = i2c_master_transmit_receive(dev_handle_, &reg_addr, 1, &data, 1, 1000);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read register 0x%02X: %s", reg_addr, esp_err_to_name(ret));
        device_info_.error_count++;
        return false;
    }
    
    return true;
}

bool MCP23017::WriteRegister(MCPRegister reg, uint8_t data) {
    if (!dev_handle_) {
        ESP_LOGE(TAG, "Device not initialized");
        return false;
    }
    
    uint8_t write_data[2] = {static_cast<uint8_t>(reg), data};
    esp_err_t ret = i2c_master_transmit(dev_handle_, write_data, 2, 1000);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to write register 0x%02X: %s", write_data[0], esp_err_to_name(ret));
        device_info_.error_count++;
        return false;
    }
    
    return true;
}

// 辅助函数实现
uint8_t MCP23017::PinToPort(uint8_t pin) {
    return (pin < 8) ? 0 : 1;
}

uint8_t MCP23017::PinToBit(uint8_t pin) {
    return pin % 8;
}

MCPRegister MCP23017::GetDirectionRegister(uint8_t pin) {
    return (pin < 8) ? MCPRegister::IODIRA : MCPRegister::IODIRB;
}

MCPRegister MCP23017::GetOutputRegister(uint8_t pin) {
    return (pin < 8) ? MCPRegister::OLATA : MCPRegister::OLATB;
}

MCPRegister MCP23017::GetInputRegister(uint8_t pin) {
    return (pin < 8) ? MCPRegister::GPIOA : MCPRegister::GPIOB;
}

MCPDeviceInfo MCP23017::GetDeviceInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return device_info_;
}

bool MCP23017::Reset() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 重置所有寄存器到默认值
    bool success = true;
    success &= WriteRegister(MCPRegister::IODIRA, 0xFF);
    success &= WriteRegister(MCPRegister::IODIRB, 0xFF);
    success &= WriteRegister(MCPRegister::IPOLA, 0x00);
    success &= WriteRegister(MCPRegister::IPOLB, 0x00);
    success &= WriteRegister(MCPRegister::GPINTENA, 0x00);
    success &= WriteRegister(MCPRegister::GPINTENB, 0x00);
    success &= WriteRegister(MCPRegister::DEFVALA, 0x00);
    success &= WriteRegister(MCPRegister::DEFVALB, 0x00);
    success &= WriteRegister(MCPRegister::INTCONA, 0x00);
    success &= WriteRegister(MCPRegister::INTCONB, 0x00);
    success &= WriteRegister(MCPRegister::IOCONA, 0x00);
    success &= WriteRegister(MCPRegister::IOCONB, 0x00);
    success &= WriteRegister(MCPRegister::GPPUA, 0x00);
    success &= WriteRegister(MCPRegister::GPPUB, 0x00);
    success &= WriteRegister(MCPRegister::OLATA, 0x00);
    success &= WriteRegister(MCPRegister::OLATB, 0x00);
    
    if (success) {
        ESP_LOGI(TAG, "MCP23017 reset successfully");
        device_info_.error_count = 0;
    } else {
        ESP_LOGE(TAG, "Failed to reset MCP23017");
    }
    
    return success;
}

void MCP23017::SetCallback(std::shared_ptr<MCPCallback> callback) {
    callback_ = callback;
}

// MCP通信管理器实现
MCPCommunicationManager& MCPCommunicationManager::GetInstance() {
    static MCPCommunicationManager instance;
    return instance;
}

bool MCPCommunicationManager::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        ESP_LOGW(TAG, "MCP Communication Manager already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing MCP Communication Manager");
    
    monitoring_running_ = false;
    monitoring_task_ = nullptr;
    initialized_ = true;
    
    ESP_LOGI(TAG, "MCP Communication Manager initialized successfully");
    return true;
}

bool MCPCommunicationManager::AddDevice(std::shared_ptr<MCP23017> device) {
    if (!device) {
        ESP_LOGE(TAG, "Invalid device pointer");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    uint8_t address = device->GetDeviceInfo().address;
    if (devices_.find(address) != devices_.end()) {
        ESP_LOGW(TAG, "Device with address 0x%02X already exists", address);
        return false;
    }
    
    devices_[address] = device;
    ESP_LOGI(TAG, "Added MCP device at address 0x%02X", address);
    
    return true;
}

std::shared_ptr<MCP23017> MCPCommunicationManager::GetDevice(uint8_t address) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = devices_.find(address);
    if (it != devices_.end()) {
        return it->second;
    }
    
    return nullptr;
}

} // namespace hardware_layer
